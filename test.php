<?php
global $USER;
require($_SERVER["DOCUMENT_ROOT"] . "/bitrix/modules/main/include/prolog_before.php");

use Bitrix\Catalog\PriceTable;
use Bitrix\Iblock\ElementTable;
use Bitrix\Catalog\StoreProductTable;
use DmitrievLab\Domains\Catalog\PriceTypes\StorePriceType;
use DmitrievLab\Helpers\BitrixListEnum;
use DmitrievLab\Repository\IblockPropertyRepository;
use D<PERSON>rievLab\Repository\IblockRepository;
$connection = Bitrix\Main\Application::getConnection();
/** Bitrix\Main\Diag\SqlTracker $tracker */
$tracker = $connection->startTracker();
$startTime = microtime(true);

$storeIds = [3, 5];

// Cache iblock ID to avoid repeated calls
$catalogIblockId = IblockRepository::getIdByCode('aspro_max_catalog');

$query = \CIBlockElement::GetList(
    [],
    [
        'IBLOCK_ID' => $catalogIblockId,
        'ACTIVE' => 'Y',
        'SECTION_ID' => 5910,
        'INCLUDE_SUBSECTIONS' => 'Y'
    ],
    false,
    [],
    ['ID', 'NAME', 'SORT', 'ACTIVE', 'CODE', 'XML_ID', 'SHOW_COUNTER']
);

$products = [];

while ($product = $query->GetNextElement()) {
    $products[] = $product->GetFields();
}


$productIds = array_column($products, 'ID');

$storeProductQuery = StoreProductTable::getList([
    'filter' => [
        '@PRODUCT_ID' => $productIds,
        '@STORE_ID' => $storeIds
    ],
    'select' => ['PRODUCT_ID', 'STORE_ID', 'AMOUNT', 'STORE.XML_ID'],
]);

while ($storeItem = $storeProductQuery->fetch()) {
    $prices = getPriceByStoreId((int)$storeItem['PRODUCT_ID'], (int)$storeItem['STORE_ID']);

    $store = [
        'id' => $storeItem['STORE_ID'],
        'xmlId' => $storeItem['CATALOG_STORE_PRODUCT_STORE_XML_ID'],
        'stock' => $storeItem['AMOUNT'] ?? 0,
        'priceRetail' => $prices['BASE_PRICE'],
        'priceStock' => $prices['DISCOUNT_PRICE'],
    ];

    $filledResult[$storeItem['PRODUCT_ID']]['stores'][] = $store;
}

$productsWithProperties = [];
foreach ($products as $product) {
    $vidProdukta = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'VID_PRODUKTA'
    );

    $vesObemBrutto = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'VES_OBEM_BRUTTO'
    );

    $vkusNachinka = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'VKUS_NACHINKA'
    );

    $processingMethod = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'SPOSOB_OBRABOTKI'
    );

    $manufacturer = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'PROIZVODITEL'
    );

    $productType = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'TIP_PRODUKTA'
    );

    $brand = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'BREND'
    );

    $tags = IblockPropertyRepository::getPropertyValue(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'TEG'
    );

    $tags = array_map(function ($tag) {
        return mb_strtolower(trim($tag));
    }, explode(',', $tags));


    $productData = [
        'id' => $product['ID'],
        'title' => $product['NAME'],
        'sort' => $product['SORT'],
        'showCounter' => $product['SHOW_COUNTER'],
        'active' => BitrixListEnum::fromName($product['ACTIVE']),
        'code' => $product['CODE'],
        'xmlId' => $product['XML_ID'],
        'stores' => $filledResult[$product['ID']]['stores'],
        'sku' => IblockPropertyRepository::getPropertyValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'CML2_ARTICLE'
            ) ?? '',
        'minimalOrder' => IblockPropertyRepository::getPropertyValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'MINIMALNYY_VES_DLYA_ZAKAZA_KG'
            ) ?? 1,
        'step' => IblockPropertyRepository::getPropertyValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'SHAG_IZMENENIYA_VESA_DLYA_ZAKAZA_KG'
            ) ?? 1,
        'tags' => $tags,
        'vidProdukta' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'VID_PRODUKTA'
            ))
        ],
        'vesObemBrutto' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'VES_OBEM_BRUTTO'
            ))
        ],
        'vkusNachinka' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'VKUS_NACHINKA'
            ))
        ],
        'processingMethod' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'SPOSOB_OBRABOTKI'
            ))
        ],
        'manufacturer' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'PROIZVODITEL'
            ))
        ],
        'productType' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'TIP_PRODUKTA'
            ))
        ],
        'brand' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'BREND'
            ))
        ],
    ];

    $resGroups = CIBlockElement::GetElementGroups($product['ID'], true);
    $productData['categories'] = [];
    while ($arGroup = $resGroups->Fetch()) {
        $category = [
            'id' => $arGroup['ID'],
            'name' => $arGroup['NAME'],
            'code' => $arGroup['CODE']
        ];
        $productData['categories'][] = $category;
    }

    $productsWithProperties[] = $productData;
}

$executionTime = microtime(true) - $startTime;

$connection->stopTracker();
//
//foreach ($tracker->getQueries() as $query) {
//    var_dump($query->getSql());
//    var_dump($query->getTime());
//}
//die();
dump($executionTime);
dd($productsWithProperties);

function getPriceByStoreId(int $productId, int $storeId): array
{
    $priceTypes = (new StorePriceType())->setStore($storeId)->getCurrentPrice();

    $basePrice = PriceTable::getList([
        'filter' => [
            "=PRODUCT_ID" => $productId,
            "CATALOG_GROUP_ID" => $priceTypes->getBasePriceType()->getId()
        ],
        'select' => [
            'PRICE'
        ],
    ])->fetch();

    $discountPrice = PriceTable::getList([
        'filter' => [
            "=PRODUCT_ID" => $productId,
            "CATALOG_GROUP_ID" => $priceTypes->getDiscountPriceType()->getId()
        ],
        'select' => [
            'PRICE'
        ],
    ])->fetch();

    return [
        'BASE_PRICE' => $basePrice['PRICE'],
        'DISCOUNT_PRICE' => $discountPrice['PRICE']
    ];
}
