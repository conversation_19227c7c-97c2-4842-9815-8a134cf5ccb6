<?php
global $USER;
require($_SERVER["DOCUMENT_ROOT"] . "/bitrix/modules/main/include/prolog_before.php");
//
//$client = Elastic\Elasticsearch\ClientBuilder::create()
//    ->setHosts(['elasticsearch:9200'])
//    ->build();
//
//
//$params = [
//    'index' => 'de_store',
//    'body'  => [
//        'name' => 'Профитроли с вареной сгущенкой2',
//        'article_number' => '00166091',
//        'tags' => [
//            'мини эклеры'
//        ]
//    ]
//];
//
//$response = $client->index($params);
//echo "Документ добавлен с ID: " . $response['_id'];
//
//$query = 'эклеры';
//
//$params = [
//    'index' => 'de_store',
//    'body' => [
//        'query' => [
//            'term' => [
//                'tags.keyword' => $query
//            ]
//        ]
//    ]
//];
//
//
//$startTime = microtime(true);
//$response = $client->search($params);
//$endTime = microtime(true);
//
//$executionTime = ($endTime - $startTime) * 1000;
//
//echo "Время выполнения запроса: " . $executionTime . " мс" .PHP_EOL;
//
//dump($response->asArray()['hits']['hits'][0]['_source']);
//dump($response->asArray()['hits']['hits'][0]['_score']);
//dump($response->asArray()['hits']['hits'][1]['_source']);
//dump($response->asArray()['hits']['hits'][1]['_score']);
//dump($response->asArray()['hits']['hits'][2]['_source']);
//dump($response->asArray()['hits']['hits'][2]['_score']);
//dump($response->asArray()['hits']['hits'][3]['_source']);
//dump($response->asArray()['hits']['hits'][3]['_score']);
//
//
//$params = [
//    'index' => 'de_store'
//];
//
//try {
//    $response = $client->indices()->getMapping($params);
//    dump($response->asArray());
//} catch (Exception $e) {
//    echo 'Ошибка: ', $e->getMessage();
//}
//
//
//$params = [
//    'index' => 'de_store',
//    'body' => [
//        'field' => 'tags',
//        'text' => 'эклеры'
//    ]
//];
//
//$response = $client->indices()->analyze($params);
//dump($response->asArray());

use Bitrix\Catalog\PriceTable;
use Bitrix\Iblock\ElementTable;
use Bitrix\Catalog\StoreProductTable;
use DmitrievLab\Domains\Catalog\PriceTypes\StorePriceType;
use DmitrievLab\Helpers\BitrixListEnum;
use DmitrievLab\Repository\IblockPropertyRepository;
use DmitrievLab\Repository\IblockRepository;

$startTime = microtime(true);

$storeIds = [3, 5];

$query = \CIBlockElement::GetList(
    [],
    [
        'IBLOCK_ID' => IblockRepository::getIdByCode('aspro_max_catalog'),
        'ACTIVE' => 'Y',
        'SECTION_ID' => 5910,
        'INCLUDE_SUBSECTIONS' => 'Y'
    ],
    false,
    [],
    ['ID', 'NAME', 'SORT', 'ACTIVE', 'CODE', 'XML_ID', 'SHOW_COUNTER']
);

$products = [];

while ($product = $query->GetNextElement()) {
    $products[] = $product->GetFields();
}


$productIds = array_column($products, 'ID');

$storeProductQuery = StoreProductTable::getList([
    'filter' => [
        '@PRODUCT_ID' => $productIds,
        '@STORE_ID' => $storeIds
    ],
    'select' => ['PRODUCT_ID', 'STORE_ID', 'AMOUNT', 'STORE.XML_ID'],
]);

while ($storeItem = $storeProductQuery->fetch()) {
    $prices = getPriceByStoreId((int)$storeItem['PRODUCT_ID'], (int)$storeItem['STORE_ID']);

    $store = [
        'id' => $storeItem['STORE_ID'],
        'xmlId' => $storeItem['CATALOG_STORE_PRODUCT_STORE_XML_ID'],
        'stock' => $storeItem['AMOUNT'] ?? 0,
        'priceRetail' => $prices['BASE_PRICE'],
        'priceStock' => $prices['DISCOUNT_PRICE'],
    ];

    $filledResult[$storeItem['PRODUCT_ID']]['stores'][] = $store;
}

$productsWithProperties = [];
foreach ($products as $product) {
    $vidProdukta = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'VID_PRODUKTA'
    );

    $vesObemBrutto = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'VES_OBEM_BRUTTO'
    );

    $vkusNachinka = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'VKUS_NACHINKA'
    );

    $processingMethod = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'SPOSOB_OBRABOTKI'
    );

    $manufacturer = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'PROIZVODITEL'
    );

    $productType = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'TIP_PRODUKTA'
    );

    $brand = IblockPropertyRepository::getPropertyByProduct(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'BREND'
    );

    $tags = IblockPropertyRepository::getPropertyValue(
        IblockRepository::getIdByCode('aspro_max_catalog'),
        $product['ID'],
        'TEG'
    );

    $tags = array_map(function ($tag) {
        return mb_strtolower(trim($tag));
    }, explode(',', $tags));


    $productData = [
        'id' => $product['ID'],
        'title' => $product['NAME'],
        'sort' => $product['SORT'],
        'showCounter' => $product['SHOW_COUNTER'],
        'active' => BitrixListEnum::fromName($product['ACTIVE']),
        'code' => $product['CODE'],
        'xmlId' => $product['XML_ID'],
        'stores' => $filledResult[$product['ID']]['stores'],
        'sku' => IblockPropertyRepository::getPropertyValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'CML2_ARTICLE'
            ) ?? '',
        'minimalOrder' => IblockPropertyRepository::getPropertyValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'MINIMALNYY_VES_DLYA_ZAKAZA_KG'
            ) ?? 1,
        'step' => IblockPropertyRepository::getPropertyValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'SHAG_IZMENENIYA_VESA_DLYA_ZAKAZA_KG'
            ) ?? 1,
        'tags' => $tags,
        'vidProdukta' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'VID_PRODUKTA'
            ))
        ],
        'vesObemBrutto' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'VES_OBEM_BRUTTO'
            ))
        ],
        'vkusNachinka' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'VKUS_NACHINKA'
            ))
        ],
        'processingMethod' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'SPOSOB_OBRABOTKI'
            ))
        ],
        'manufacturer' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'PROIZVODITEL'
            ))
        ],
        'productType' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'TIP_PRODUKTA'
            ))
        ],
        'brand' => [
            'id' => $vidProdukta['ID'],
            'code' => $vidProdukta['CODE'],
            'name' => $vidProdukta['NAME'],
            'sort' => $vidProdukta['SORT'],
            'values' => IblockPropertyRepository::getPropertyEnumValues(IblockPropertyRepository::getPropertyAllValue(
                IblockRepository::getIdByCode('aspro_max_catalog'),
                $product['ID'],
                'BREND'
            ))
        ],
    ];

    $resGroups = CIBlockElement::GetElementGroups($product['ID'], true);
    $productData['categories'] = [];
    while ($arGroup = $resGroups->Fetch()) {
        $category = [
            'id' => $arGroup['ID'],
            'name' => $arGroup['NAME'],
            'code' => $arGroup['CODE']
        ];
        $productData['categories'][] = $category;
    }

    $productsWithProperties[] = $productData;
}

$executionTime = microtime(true) - $startTime;

dd($executionTime);


function getPriceByStoreId(int $productId, int $storeId): array
{
    $priceTypes = (new StorePriceType())->setStore($storeId)->getCurrentPrice();

    $basePrice = PriceTable::getList([
        'filter' => [
            "=PRODUCT_ID" => $productId,
            "CATALOG_GROUP_ID" => $priceTypes->getBasePriceType()->getId()
        ],
        'select' => [
            'PRICE'
        ],
    ])->fetch();

    $discountPrice = PriceTable::getList([
        'filter' => [
            "=PRODUCT_ID" => $productId,
            "CATALOG_GROUP_ID" => $priceTypes->getDiscountPriceType()->getId()
        ],
        'select' => [
            'PRICE'
        ],
    ])->fetch();

    return [
        'BASE_PRICE' => $basePrice['PRICE'],
        'DISCOUNT_PRICE' => $discountPrice['PRICE']
    ];
}
