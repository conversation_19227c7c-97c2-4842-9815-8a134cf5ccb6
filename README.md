## IblockRepository

Класс `IblockRepository` предоставляет статические методы для работы с инфоблоками в системе Bitrix. Он содержит функционал для получения идентификатора инфоблока по его коду.

### Пространство имен

```
namespace DmitrievLab\Repository;
```

### Исключения

В этом классе может быть выброшено следующее исключение:
- `IblockNotFoundException`: Выбрасывается, если инфоблок с заданным кодом не найден.

### Методы

#### `getIdByCode(string $code): int`

Получает идентификатор инфоблока по его коду.

#### Параметры

- `string $code`: Код инфоблока (необходимый для поиска).

#### Возвращает

- `int`: Идентификатор найденного инфоблока.

#### Исключения

- `IblockNotFoundException`: Выбрасывается, если инфоблок с указанным кодом не существует.

#### Пример использования

```
$iblockId = IblockRepository::getIdByCode('example_code');
echo "ID инфоблока: " . $iblockId;
```

### Описание

Класс `IblockRepository` инкапсулирует логику доступа к инфоблокам в базе данных Bitrix. Он предназначен для упрощения работы с инфоблоками, позволяя разработчикам получать идентификаторы инфоблоков по их кодам и обрабатывать случаи, когда инфоблок не найден.
# Документрация "Дом еды" 
***
***
# IblockPropertyRepository

Класс `IblockPropertyRepository` предоставляет статические методы для работы со свойствами инфоблоков в системе Bitrix. Он включает функции для получения идентификатора свойства по его коду и кода свойства по его идентификатору.

## Пространство имен


namespace DmitrievLab\Repository;


## Исключения

В этом классе может быть выброшено следующее исключение:
- `IblockPropertyNotFoundException`: Выбрасывается, если свойство с заданным кодом или идентификатором не найдено.

## Методы

### `getIdByCode(string $name): int`

Получает идентификатор свойства инфоблока по его коду.

#### Параметры

- `string $name`: Код свойства (необходимый для поиска).

#### Возвращает

- `int`: Идентификатор найденного свойства.

#### Исключения

- `IblockPropertyNotFoundException`: Выбрасывается, если свойство с указанным кодом не существует.

#### Пример использования

## Определение текущей ТТ пользователя
- получить ID ТТ
```
$propertyId = IblockPropertyRepository::getIdByCode('example_property_code');
echo "ID свойства: " . $propertyId;
DmitrievLab\Domains\Store\Store::getCurrentId();
```
---

### `getCodeById(int $id): string`

Получает код свойства инфоблока по его идентификатору.

#### Параметры

- `int $id`: Идентификатор свойства (необходимый для поиска).

#### Возвращает

- `string`: Код найденного свойства.

#### Исключения

- `IblockPropertyNotFoundException`: Выбрасывается, если свойство с указанным идентификатором не существует.

#### Пример использования

- Получить наименование ТТ
```
$propertyCode = IblockPropertyRepository::getCodeById(123);
echo "Код свойства: " . $propertyCode;
DmitrievLab\Domains\Store\Store::getCurrentName();
```

## Описание

Класс `IblockPropertyRepository` инкапсулирует логику доступа к свойствам инфоблоков в базе данных Bitrix. Он упрощает взаимодействие с свойствами, позволяя получать идентификаторы и коды свойств по заданным критериям и обрабатывать ошибки, связанные с отсутствием таких свойств.