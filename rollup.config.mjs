import path from 'path';
import fs from 'fs';
import {fileURLToPath} from 'url';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import {terser} from 'rollup-plugin-terser';
import postcss from 'rollup-plugin-postcss';
import vue from 'rollup-plugin-vue';
import autoprefixer from 'autoprefixer';
import replace from '@rollup/plugin-replace';
import del from 'rollup-plugin-delete';

const fileName = fileURLToPath(import.meta.url);
const dirName = path.dirname(fileName);

const extensionsDir = path.resolve(dirName, 'local/js/dmlab/');

const extensions = fs.readdirSync(extensionsDir).filter((dir) => {
    const fullPath = path.join(extensionsDir, dir, 'src', 'index.js');
    return fs.existsSync(fullPath);
});

console.log(extensions);

const rollupConfigs = extensions.map((ext) => {
    const extPath = path.join(extensionsDir, ext);
    const inputFile = path.join(extPath, 'src', 'index.js');
    const distPath = path.join(extPath, 'dist');

    return {
        input: inputFile,
        output: {
            file: path.join(distPath, 'script.js'),
            format: 'iife',
            name: ext.replace(/\W/g, '_'),
        },
        plugins: [
            del({targets: `${distPath}/*`, runOnce: true}),
            vue(),
            resolve(),
            commonjs(),
            replace({
                'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
                'app.config.devtools = true;': process.env.NODE_ENV === 'development' ?
                    'app.config.devtools = true;' : 'app.config.devtools = false;',
                preventAssignment: true,
            }),
            postcss({
                extract: path.join(distPath, 'style.css'),
                minimize: true,
                plugins: [autoprefixer()],
            }),
            terser(),
        ],
        watch: {
            include: `${extPath}/src/**`,
            exclude: 'node_modules/**',
            clearScreen: false,
        },
    };
});

export default rollupConfigs;
