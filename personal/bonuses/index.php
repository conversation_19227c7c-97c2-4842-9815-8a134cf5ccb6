<?
require($_SERVER["DOCUMENT_ROOT"] . "/bitrix/header.php");
$APPLICATION->SetPageProperty("keywords", "Зарегистрируйте карту лояльности Дом Еды и наслаждайтесь выгодными покупками");
$APPLICATION->SetPageProperty("description", "Зарегистрируйте карту лояльности Дом Еды и наслаждайтесь выгодными покупками");
$APPLICATION->SetPageProperty("title", "Зарегистрировать карту лояльности Дом Еды 💳");
$APPLICATION->SetTitle("Карта лояльности");

use Bitrix\Main\Page\Asset;

Asset::getInstance()->addCss('/personal/bonuses/style.css');
Asset::getInstance()->addCss( SITE_DIR . 'local/js/dmlab/bonuses/dist/style.css',true);
Asset::getInstance()->addJs( SITE_DIR . 'local/js/dmlab/bonuses/dist/script.js',true);


CJSCore::Init('aspro_font_awesome');
global $USER;
if (!$USER->IsAuthorized()) {
	$APPLICATION->IncludeComponent(
		"splash:system.auth.form",
		"main",
		array(
			"SHOW_ERRORS" => "Y",
			"POPUP_AUTH" => "Y",
			"AJAX_MODE" => "Y",
			"BACKURL" => ((isset($_REQUEST['backurl']) && $_REQUEST['backurl']) ? $_REQUEST['backurl'] : "")
		)
	);
}
?>
<?php if ($USER->IsAuthorized()):?>
    <div id="bonusesApp"></div>

    <div class="button_wrap pull-left bonusCardTrouble">
        <span>
            <span
                class="btn btn-default btn-lg animate-load has-ripple"
                data-event="jqm"
                data-name="LOYALTY_CARD_QUESTION"
                data-param-form_id="LOYALTY_CARD_QUESTION">
                Сообщить о проблеме с картой
            </span>
        </span>
    </div>
<?php endif;?>



<? require($_SERVER["DOCUMENT_ROOT"] . "/bitrix/footer.php"); ?>