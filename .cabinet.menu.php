<?
$aMenuLinks = Array(
	Array(
		"Личный кабинет",
		"/personal/index.php",
		<PERSON>rra<PERSON>(),
		<PERSON>rray(),
		""
	),
	<PERSON><PERSON><PERSON>(
		"Личные данные",
		"/personal/private/",
		<PERSON><PERSON><PERSON>(),
		<PERSON><PERSON><PERSON>(),
		""
	),
	<PERSON><PERSON><PERSON>(
		"Текущие заказы",
		"/personal/orders/",
		<PERSON><PERSON><PERSON>(),
		<PERSON><PERSON><PERSON>(),
		""
	),
	<PERSON><PERSON><PERSON>(
		"История заказов",
		"/personal/orders/?filter_history=Y",
		<PERSON>rray(),
		Array(),
		""
	),
	<PERSON><PERSON><PERSON>(
		"Карта лояльности",
		"/personal/bonuses/",
		<PERSON>rray(),
		<PERSON>rray(),
		""
	),
	<PERSON><PERSON><PERSON>(
		"Клуб \"Дом Еды\"",
		"/landings/klub-dom-edy/",
		Array(),
		Array(),
		""
	),
	<PERSON><PERSON><PERSON>(
		"Возврат товара",
		"/personal/vozvrat/",
		<PERSON>rra<PERSON>(),
		<PERSON>rra<PERSON>(),
		""
	),
	<PERSON><PERSON><PERSON>(
		"Корзина",
		"/basket/",
		<PERSON>rra<PERSON>(),
		<PERSON>rra<PERSON>(),
		""
	),
	<PERSON><PERSON><PERSON>(
		"Избранное",
		"/favorites/",
		Array(),
		Array(),
		""
	),
	Array(
		"Контакты",
		"/contacts/",
		Array(),
		Array(),
		""
	),
	Array(
		"Бонусы",
		"/personal/bonus/",
		Array(),
		Array(),
		"\\Aspro\\Functions\\CAsproMax::isBonusSystemOn()"
	),
	Array(
		"Выйти",
		"?logout=yes&login=yes",
		Array(),
		Array("class"=>"exit", "BLOCK"=>"<i class='icons'><svg id='Exit.svg' xmlns='http://www.w3.org/2000/svg' width='12' height='9' viewBox='0 0 12 9'><path class='cls-1' d='M501.849,868.853l-2.011,1.993a0.485,0.485,0,0,1-.694,0,0.506,0.506,0,0,1,0-.707L500.293,869H494.5a0.5,0.5,0,0,1,0-1h5.826l-1.182-1.175a0.5,0.5,0,0,1,0-.7,0.487,0.487,0,0,1,.694,0l2.011,2a0.486,0.486,0,0,1,.138.365A0.492,0.492,0,0,1,501.849,868.853Zm-5.349-3.322a0.486,0.486,0,0,1-.269-0.089l-0.016.024a3.5,3.5,0,1,0,0,6.07l0,0a0.484,0.484,0,0,1,.287-0.1,0.5,0.5,0,0,1,.5.5,0.492,0.492,0,0,1-.242.418l0.008,0.012c-0.022.013-.049,0.018-0.071,0.031h0a4.434,4.434,0,0,1-2.194.6,4.5,4.5,0,1,1,0-9,4.4,4.4,0,0,1,2.057.542A0.5,0.5,0,0,1,496.5,865.531Z' transform='translate(-490 -864)'/></svg></i>"),
		"\$GLOBALS['USER']->IsAuthorized()"
	)
);
?>