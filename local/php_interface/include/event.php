<?php

use Bitrix\Main\Event;
use Bitrix\Main\EventManager;
use Bitrix\Main\Loader;
use Bitrix\Main\LoaderException;
use D<PERSON>rievLab\Domains\Bifit\Bifit;
use DmitrievLab\Domains\Exchange\OrderSyncBuffer;
use Dmitriev<PERSON>ab\Domains\Logging\Logger;
use DmitrievLab\Domains\Logging\LoggerChannel;
use DmitrievLab\Domains\Order\OrderHistory;
use DmitrievLab\Domains\Order\Statuses\OrderStatusPublisher;
use DmitrievLab\Domains\Order\Statuses\StatusChangedEvent;
use DmitrievLab\Domains\Payments\Yookassa\Credentials\MobileAppCredentials;
use DmitrievLab\Domains\Payments\Yookassa\Credentials\SiteCredentials;
use DmitrievLab\Domains\Payments\Yookassa\Observer\PaymentPublisher;
use DmitrievLab\Domains\Payments\Yookassa\Yookassa;
use DmitrievLab\Helpers\BitrixListEnum;
use DmitrievLab\Helpers\Config;
use D<PERSON><PERSON>vLab\Notify\Telegram\Client;
use D<PERSON><PERSON>vLab\Repository\IblockRepository;
use D<PERSON><PERSON><PERSON><PERSON>ab\Repository\IblockSectionRepository;
use DmitrievLab\Domains\Delivery\Delivery;
use Splash\Splash_Catalog;

$eventManager = EventManager::getInstance();
$eventManager->addEventHandler('iblock', 'OnAfterIBlockElementAdd', 'setRatio');
$eventManager->addEventHandler('iblock', 'OnAfterIBlockElementUpdate', 'setRatio');
$eventManager->addEventHandler('dmlabImport', 'OnImport1CFinished', 'normalizeProductProperties');
$eventManager->addEventHandler('dmlabImport', 'OnImport1CFinished', 'setCategoryForProductsWithDiscounts');
$eventManager->addEventHandler('dmlabImport', 'OnImport1CFinished', 'setStockSort');
$eventManager->addEventHandler('dmlabImport', 'OnImport1CFinished', 'activateCategoryProductsWithDiscounts');
$eventManager->addEventHandler('dmlabImport', 'OnImport1CFinished', 'setSorting');
$eventManager->addEventHandler('dmlabImport', 'OnImport1CFinished', 'disablingEmptyCategories');
$eventManager->addEventHandler('importOrderDmitrievlab', 'OnImportOrder1CDone', 'OnImportOrder1CDoneHandler');
$eventManager->addEventHandler('importOrderDmitrievlab', 'OnImportOrder1CFinished', 'orderSyncBufferEvent');
$eventManager->addEventHandler('importOrderDmitrievlab', 'OnImportOrder1CFinished', 'OnImportOrder1CFinishedHandler');
$eventManager->addEventHandler('importOrderDmitrievlab', 'OnImportOrder1CComplete', 'OnImportOrder1CCompleteHandler');
$eventManager->addEventHandler('main', 'OnBeforeUserUpdate', 'checkEmailBeforeUserUpdate');
$eventManager->addEventHandler('main', 'OnAfterUserUpdate', 'updateUserData');
$eventManager->addEventHandler('main', 'OnAfterUserUpdate', 'updateUserOrderProfile');
$eventManager->addEventHandler('sale', 'OnOrderStatusSendEmail', 'modifySendingSaleData');
$eventManager->addEventHandler('sale', 'OnSaleStatusOrderChange', 'orderStatusChangedHandler');
$eventManager->addEventHandler('iblock', 'OnBeforeIBlockSectionUpdate', 'setDiscountedGoodsSectionToActive');
$eventManager->addEventHandler('sale', 'OnBeforeBasketAdd', 'setDefaultBasketProperties');
$eventManager->addEventHandler('main', 'OnAfterUserAuthorize', 'updateUserBonuses');
$eventManager->addEventHandler('main', 'OnBeforeProlog', 'setDeliveryData');
$eventManager->addEventHandler('main', 'OnBeforeUserLogout', 'removeDeliveryCheckerCookie');
$eventManager->addEventHandler('main', 'OnAfterUserAuthorize', 'removeDeliveryCheckerCookie');
$eventManager->addEventHandler('sale', 'OnSaleStatusOrderChange', 'newOrderNotifyTelegram');

function setDiscountedGoodsSectionToActive(&$arFields): void
{
    if ($arFields["ID"] == IblockSectionRepository::getIdByCode('sale')) {
        $arFields["ACTIVE"] = 'Y';
    }
}

function setSorting(): void
{
    Splash_Catalog::setSort();
    BXClearCache(true);

    if (class_exists('\Bitrix\Main\Data\ManagedCache')) {
        (new \Bitrix\Main\Data\ManagedCache())->cleanAll();
    }

    if (class_exists('\CStackCacheManager')) {
        (new \CStackCacheManager())->CleanAll();
    }

    if (class_exists('\Bitrix\Main\Composite\Page')) {
        \Bitrix\Main\Composite\Page::getInstance()->deleteAll();
    }
}

function activateCategoryProductsWithDiscounts(): void
{
    \CModule::IncludeModule("iblock");
    (new \CIBlockSection)->Update(IblockSectionRepository::getIdByCode('sale'), ['ACTIVE' => 'Y']);
}

// TODO разобраться и тип цен добавить
function setCategoryForProductsWithDiscounts(): void
{

    \CModule::IncludeModule("iblock");
    \CModule::IncludeModule("catalog");

    $categoryProductsWithDiscountsId = IblockSectionRepository::getIdByCode('sale');
    $sectionsToUpdate = [];

    $query = \CIBlockElement::GetList(
        [
            "SORT" => "ASC"
        ],
        [
            'IBLOCK_ID' => IblockRepository::getIdByCode('aspro_max_catalog'),
            '=AVAILABLE' => 'Y',
            '@PRICE_TYPE' => [8, 9, 10, 11]
        ],
        false,
        false,
        [
            "ID",
            "IBLOCK_ID",
            "NAME",
            "PRICE_TYPE",
            "PRICE_8",
            "PRICE_9",
            "PRICE_10",
            "PRICE_11"
        ]
    );

//    03.0 Дом Еды - 6
//    Основной - 7
//    DOM_EDY_1_RETAIL - 9
//    DOM_EDY_5_RETAIL - 11
//    DOM_EDY_1_STOCK - 8
//    DOM_EDY_5_STOCK - 10

    while ($product = $query->getNext()) {

        $stockValue = '1';

        if ((!empty($product["PRICE_8"]) && $product["PRICE_8"] < $product["PRICE_9"])
            || (!empty($product["PRICE_10"]) && $product["PRICE_10"] < $product["PRICE_11"])) {
            $stockValue = '0';
        }

        \CIBlockElement::SetPropertyValuesEx($product["ID"], false, ["STOCK" => $stockValue]);

        $productSections = [];
        $queryProductSections = \CIBlockElement::GetElementGroups($product['ID'], false);
        while ($productSection = $queryProductSections->Fetch()) {
            $productSections[] = $productSection['ID'];
        }

        if ($stockValue == '0' && !in_array($categoryProductsWithDiscountsId, $productSections)) {
            $productSections[] = $categoryProductsWithDiscountsId;
            $sectionsToUpdate[$product['ID']] = $productSections;
        } elseif ($stockValue == '1' && in_array($categoryProductsWithDiscountsId, $productSections)) {
            $productSections = array_diff($productSections, [$categoryProductsWithDiscountsId]);
            $sectionsToUpdate[$product['ID']] = $productSections;
        }
    }

    $productElement = new \CIBlockElement;
    foreach ($sectionsToUpdate as $elementId => $sections) {
        $productElement->Update($elementId, ['IBLOCK_SECTION' => $sections]);
    }
    if (class_exists('\Bitrix\Main\Data\ManagedCache')) {
        (new \Bitrix\Main\Data\ManagedCache())->cleanAll();
    }

    if (class_exists('\CStackCacheManager')) {
        (new \CStackCacheManager())->CleanAll();
    }

    if (class_exists('\Bitrix\Main\Composite\Page')) {
        \Bitrix\Main\Composite\Page::getInstance()->deleteAll();
    }
}

function setStockSort(): void
{

    \CModule::IncludeModule("iblock");
    \CModule::IncludeModule("catalog");

    $iterator = \CIBlockElement::GetList(
        array(),
        array('IBLOCK_ID' => IblockRepository::getIdByCode('aspro_max_catalog')),
        false,
        false,
        array('ID', 'NAME', 'IBLOCK_ID', 'PROPERTY_VES_TOVARA_G')
    );

    while ($it = $iterator->fetch()) {
        if ($it['PROPERTY_VES_TOVARA_G_VALUE'] > 0) {
            \CCatalogProduct::Update($it['ID'], array('WEIGHT' => $it['PROPERTY_VES_TOVARA_G_VALUE']));
        }
    }
}

function setRatio(&$arFields): void
{
    \CModule::IncludeModule("iblock");
    \CModule::IncludeModule("catalog");
    if ($arFields['IBLOCK_ID'] == IblockRepository::getIdByCode('aspro_max_catalog')) {
        $ratos = 0;
        $db_props = \CIBlockElement::GetProperty($arFields['IBLOCK_ID'], $arFields['ID'], array("sort" => "asc"), array("CODE" => "SHAG_IZMENENIYA_VESA_DLYA_ZAKAZA_KG"));
        if ($ar_props = $db_props->Fetch()) {
            $ratos = $ar_props['VALUE'];
        }
        $vesos = 0;
        $db_props = \CIBlockElement::GetProperty($arFields['IBLOCK_ID'], $arFields['ID'], array("sort" => "asc"), array("CODE" => "VES_TOVARA_G"));
        if ($ar_props = $db_props->Fetch()) {
            $vesos = $ar_props['VALUE'];
            if ($vesos > 0) {
                $dfs = \CCatalogProduct::Update($arFields['ID'], array('WEIGHT' => $vesos));
            }
        }
        if ($ratos > 0) {
            $curElementRatio = \CCatalogMeasureRatio::getList(
                array(),
                array('PRODUCT_ID' => $arFields['ID']),
                false,
                false
            );
            $ratioId = 0;
            while ($arRatio = $curElementRatio->GetNext()) {
                $ratioId = $arRatio['ID'];
            }
            if ($ratioId > 0) {
                $dfe = \CCatalogMeasureRatio::update(
                    $ratioId,
                    array(
                        'PRODUCT_ID' => $arFields['ID'],
                        'RATIO' => $ratos
                    )
                );
            } else {
                \CCatalogMeasureRatio::add(
                    array(
                        'PRODUCT_ID' => $arFields['ID'],
                        'RATIO' => $ratos
                    )
                );
            }
            unset($vesos);
            unset($ratos);
        }
    }
}

function orderSyncBufferEvent(\Bitrix\Main\Event $event): void
{
    $ordersId = $event->getParameter('ordersId');
    (new OrderSyncBuffer())->add($ordersId);
}

function orderStatusChangedHandler(\Bitrix\Main\Event $event)
{
    /* @var \Bitrix\Sale\Order $order */
    $order = $event->getParameter("ENTITY");

    if ($order->getField("STATUS_ID") === 'CH') {
        if (Config::isProduction()) {
            setUpObservers($order);
        }
    }
}

function setUpObservers(\Bitrix\Sale\Order $order): void
{
    $orderStatusPublisher = new OrderStatusPublisher();
    $paymentPublisher = new PaymentPublisher();

    $propertyCollection = $order->getPropertyCollection();
    $orderOrigin = $propertyCollection->getItemByOrderPropertyCode('ORDER_ORIGIN');
    $yooKassaId = $propertyCollection->getItemByOrderPropertyCode('ID_KASSA');
    $bifitId = $propertyCollection->getItemByOrderPropertyCode('ID_BIFIT');
    $receiptIsPrinted = $propertyCollection->getItemByOrderPropertyCode('YA_PAY_SEND_1');
    $holding = $propertyCollection->getItemByOrderPropertyCode('HOLDING');

    $statusChangedEvent = new StatusChangedEvent(
        orderId: $order->getId(),
        status: $order->getField("STATUS_ID"),
        paymentId: $yooKassaId->getValue(),
        bifitId: $bifitId->getValue(),
        isReceiptPrinted: $receiptIsPrinted->getValue() === '1',
        isCanceled: $order->getField("STATUS_ID") === 'CH',
        isPaid: $order->isPaid(),
        isHolding: BitrixListEnum::fromName($holding->getValue()),
    );

    if (!empty($yooKassaId->getValue())) {
        $logger = new Logger();

        $credentials = ($orderOrigin->getValue() === 'OO_SITE')
            ? new SiteCredentials() : new MobileAppCredentials();

        $yookassa = new Yookassa($credentials, $logger, $paymentPublisher);
        $orderStatusPublisher->subscribe($yookassa, 10);
    }

    if (Config::isProduction()) {
        $orderStatusPublisher->subscribe(new Bifit(), 5);
    }

    $orderStatusPublisher->notify($statusChangedEvent);

    $paymentPublisher->subscribe(new OrderHistory());
}

function setDefaultBasketProperties(&$arFields): void
{
    $n = true;
    $i = true;
    $c = true;
    $r = true;
    foreach ($arFields['PROPS'] as $v) {
        if ($v['CODE'] == 'NAME_PROIZVODITEL' && !empty($v['VALUE'])) {
            $n = false;
        }
        if ($v['CODE'] == 'INN_PROIZVODITEL' && !empty($v['VALUE'])) {
            $i = false;
        }
        if ($v['CODE'] == 'CONTACT_PROIZVODITEL' && !empty($v['VALUE'])) {
            $c = false;
        }
        if ($v['CODE'] == 'VAT' && !empty($v['VALUE'])) {
            $r = false;
        }
    }
    if ($n) {
        $arFields['PROPS'][] = array(
            'NAME' => "NAME_PROIZVODITEL",
            'CODE' => "NAME_PROIZVODITEL",
            'VALUE' => '',
            'SORT' => 0
        );
    }
    if ($i) {
        $arFields['PROPS'][] = array(
            'NAME' => "INN_PROIZVODITEL",
            'CODE' => "INN_PROIZVODITEL",
            'VALUE' => '',
            'SORT' => 1
        );
    }
    if ($c) {
        $arFields['PROPS'][] = array(
            'NAME' => "CONTACT_PROIZVODITEL",
            'CODE' => "CONTACT_PROIZVODITEL",
            'VALUE' => '',
            'SORT' => 2
        );
    }
    if ($r) {
        $arFields['PROPS'][] = array(
            'NAME' => "VAT",
            'CODE' => "VAT",
            'VALUE' => '',
            'SORT' => 3
        );
    }

}


function modifySendingSaleData($orderID, &$eventName, &$arFields, $status): void
{
    if ($orderID > 0) {
        $order = \Bitrix\Sale\Order::load($orderID);
        $propertyCollection = $order->getPropertyCollection();
        $name_prop = $propertyCollection->getItemByOrderPropertyCode('FIO');
        $arFields['PRICE'] = $order->getPrice() . 'руб.';
        $arFields['ORDER_USER'] = $name_prop->getValue();
        $arFields['SITE_URL'] = $_SERVER["HTTP_HOST"];
        $dbRes = \Bitrix\Sale\Basket::getList(
            [
                'select' => ['NAME', 'QUANTITY', 'MEASURE_NAME', 'PRICE'],
                'filter' => [
                    '=ORDER_ID' => $orderID,
                ]
            ]
        );
        while ($item = $dbRes->fetch()) {
            if ($item['MEASURE_NAME'] == 'шт') {
                $arFields['ORDER_LIST'] .= $item['NAME'] . ' - ' . number_format($item['QUANTITY'], 0, '', '') . ' шт. - ' . number_format($item['QUANTITY'] * $item['PRICE'], 2, '.', ' ') . ' руб.<br>';
            }
            if ($item['MEASURE_NAME'] == 'кг') {
                $arFields['ORDER_LIST'] .= $item['NAME'] . ' - ' . number_format($item['QUANTITY'], 1, '.', '') . ' кг - ' . number_format($item['QUANTITY'] * $item['PRICE'], 2, '.', ' ') . ' руб.<br>';
            }
            if ($item['MEASURE_NAME'] == 'г') {
                $arFields['ORDER_LIST'] .= $item['NAME'] . ' - ' . number_format($item['QUANTITY'], 1, '.', '') . ' г - ' . number_format($item['QUANTITY'] * $item['PRICE'], 2, '.', ' ') . ' руб.<br>';
            }
            if ($item['MEASURE_NAME'] == 'л.') {
                $arFields['ORDER_LIST'] .= $item['NAME'] . ' - ' . number_format($item['QUANTITY'], 1, '.', '') . ' л - ' . number_format($item['QUANTITY'] * $item['PRICE'], 2, '.', ' ') . ' руб.<br>';
            }
            if ($item['MEASURE_NAME'] == 'упак') {
                $arFields['ORDER_LIST'] .= $item['NAME'] . ' - ' . number_format($item['QUANTITY'], 0, '', '') . ' упак - ' . number_format($item['QUANTITY'] * $item['PRICE'], 2, '.', ' ') . ' руб.<br>';
            }
            if ($item['MEASURE_NAME'] == '100 г') {
                $arFields['ORDER_LIST'] .= $item['NAME'] . ' - ' . number_format($item['QUANTITY'], 0, '', '') . ' шт - ' . number_format($item['QUANTITY'] * $item['PRICE'], 2, '.', ' ') . ' руб.<br>';
            }
            if ($item['MEASURE_NAME'] == '200 г') {
                $arFields['ORDER_LIST'] .= $item['NAME'] . ' - ' . number_format($item['QUANTITY'], 0, '', '') . ' шт - ' . number_format($item['QUANTITY'] * $item['PRICE'], 2, '.', ' ') . ' руб.<br>';
            }
            if ($item['MEASURE_NAME'] == '320 г') {
                $arFields['ORDER_LIST'] .= $item['NAME'] . ' - ' . number_format($item['QUANTITY'], 0, '', '') . ' шт - ' . number_format($item['QUANTITY'] * $item['PRICE'], 2, '.', ' ') . ' руб.<br>';
            }
        }
        unset($name_prop);
    }
}


function updateUserData($event): void
{
    $userService = new \DmitrievLab\UserService();
    $userService->setCurrentUser($event['ID']);
    $userService->checkIncludedFields($event);
    $userService->updateDataIn1c();
}

function updateUserOrderProfile(): void
{
    global $USER;
    if ($USER->IsAuthorized()) {
        $userId = $USER->GetID();

        $userData = getUsers($userId);

        $userProfilesQuery = CSaleOrderUserProps::GetList(
            ["DATE_UPDATE" => "DESC"],
            ["USER_ID" => $userId]
        );

        $profileId = null;
        while ($userProfile = $userProfilesQuery->Fetch()) {
            $profileId = $userProfile['ID'];
        }

        $profilePropertiesQuery = CSaleOrderUserPropsValue::GetList(array("ID" => "ASC"), array("USER_PROPS_ID" => $profileId));
        while ($profileProperty = $profilePropertiesQuery->Fetch()) {
            $userProfileOrder[$profileProperty['PROP_CODE']] = $profileProperty;
        }


        foreach ($userProfileOrder as $key => $profileValue) {
            $arFields = [
                'USER_PROPS_ID' => $profileValue["USER_PROPS_ID"],
                'ORDER_PROPS_ID' => $profileValue["ORDER_PROPS_ID"],
                'NAME' => $profileValue["NAME"]
            ];

            switch ($key) {
                case 'FIO':
                    $arFields['VALUE'] = $userData['NAME'] . ' ' . $userData['LAST_NAME'] . ' ' . $userData['SECOND_NAME'];
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
                case 'EMAIL':
                    $arFields['VALUE'] = $userData['EMAIL'];
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
                case 'PHONE':

                    $arFields['VALUE'] = $userData['PERSONAL_PHONE'];
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
            }
        }
    }
}

function orderNotification(\Bitrix\Sale\Order $orderEntity): void
{
    Loader::includeModule('sale');

    // $orderEntity->getField('STATUS_ID') === 'Q' временно
    if ($orderEntity->getField('STATUS_ID') === 'S' || $orderEntity->getField('STATUS_ID') === 'Q') {

        $order = new \DmitrievLab\Notify\Order($orderEntity);
        $orderInformation = $order->getPreparedOrder();
        $orderId = $orderEntity->getId();

        $propertyCollection = \Bitrix\Sale\Order::load($orderId)
            ->getPropertyCollection();
        $replacementValue = $propertyCollection->getItemByOrderPropertyCode('ZAMENA');

        $message = "Собран заказ №$orderId" . PHP_EOL;
        foreach ($orderInformation as $key => $value) {
            $message .= $key . ': ' . $value . PHP_EOL;
        }

        if (!$order->isNotifySended()) {

            if ($replacementValue->getValue() === 'PZ_2') {
                $emailProperty = $propertyCollection->getUserEmail();
                $fio = $propertyCollection->getProfileName();
                $orderDate = $propertyCollection->getOrder()->getDateInsert();
                $price = $propertyCollection->getOrder()->getPrice();
                $arFields = [
                    "EMAIL" => $emailProperty->getValue(),
                    "ORDER_USER" => $fio->getValue(),
                    "ORDER_ID" => $orderId,
                    "ORDER_DATE" => $orderDate->format("d.m.Y H:i:s"),
                    "PRICE" => $price,
                ];
                CEvent::Send("SALE_STATUS_CHANGED_QU", "s1", $arFields);
            }

            $telegramApi = new Client();
            $telegramApiResponse = $telegramApi->sendMessage($message, $order->getStoreId());

            (new Logger())->setLogger(LoggerChannel::MESSENGER)
                ->debug('Уведомление о собранном заказе телеграм',
            [
                'response' => $telegramApiResponse,
                'message' => $message,
                'storeId' => $order->getStoreId()
            ]);

            if ($telegramApiResponse) {
                $order->setNotifyIsSend();
            }
        }
    }
}

function newOrderNotifyTelegram(\Bitrix\Main\Event $event): void
{
    /* @var \Bitrix\Sale\Order $order */
    $orderEntity = $event->getParameter("ENTITY");

    if ($orderEntity->getField("STATUS_ID") === 'P') {
        $order = (new \DmitrievLab\Notify\Order($orderEntity));
        $orderInformation = $order->getPreparedOrder();
        unset($orderInformation['ФИО']);
        unset($orderInformation['Номер телефона']);
        unset($orderInformation['Сумма заказа']);
        unset($orderInformation['Оплачено']);
        unset($orderInformation['Наличие/отсутствие оплаты']);

        $message = "Новый заказ №" . $orderEntity->getId() . PHP_EOL;
        foreach ($orderInformation as $key => $value) {
            $message .= $key . ': ' . $value . PHP_EOL;
        }
        if (!$order->isNotifySended()) {
            $telegramApi = new Client();
            $telegramApiResponse = $telegramApi->sendMessage($message, $order->getStoreId());

            (new Logger())->setLogger(LoggerChannel::MESSENGER)
                ->debug('Уведомление о новом заказе в телеграм',
                    [
                        'response' => $telegramApiResponse,
                        'message' => $message,
                        'storeId' => $order->getStoreId()
                    ]);

            if ($telegramApiResponse) {
                $order->setNotifyIsSend();
            }
        }
    }
}

function normalizeProductProperties(): void
{
    $queryProducts = CIBlockElement::GetList(
        array(),
        ['IBLOCK_ID' => IblockRepository::getIdByCode('aspro_max_catalog'), "ACTIVE" => "Y"],
        false,
        false,
        [
            'ID',
            'NAME',
            'PROPERTY_MINIMALNYY_VES_DLYA_ZAKAZA_KG',
            'PROPERTY_SHAG_IZMENENIYA_VESA_DLYA_ZAKAZA_KG',
            'PROPERTY_VES_TOVARA_G'
        ]
    );
    $propertiesForUpdate = [];

    while ($ar = $queryProducts->GetNextElement()) {
        $productsProperties = $ar->GetFields();
        $properties = [];
        if ($productsProperties['PROPERTY_VES_TOVARA_G_VALUE'] !== null) {
            $properties['VES_TOVARA_G'] = preg_replace('/\s+/u', '', $productsProperties['PROPERTY_VES_TOVARA_G_VALUE']);
        } else {
            $properties['VES_TOVARA_G'] = null;
        }

        if ($productsProperties['PROPERTY_MINIMALNYY_VES_DLYA_ZAKAZA_KG_VALUE'] !== null) {
            $properties['MINIMALNYY_VES_DLYA_ZAKAZA_KG'] = str_replace(',', '.', $productsProperties['PROPERTY_MINIMALNYY_VES_DLYA_ZAKAZA_KG_VALUE']);
        } else {
            $properties['MINIMALNYY_VES_DLYA_ZAKAZA_KG'] = null;
        }

        if ($productsProperties['PROPERTY_SHAG_IZMENENIYA_VESA_DLYA_ZAKAZA_KG_VALUE'] !== null) {
            $properties['SHAG_IZMENENIYA_VESA_DLYA_ZAKAZA_KG'] = str_replace(',', '.', $productsProperties['PROPERTY_SHAG_IZMENENIYA_VESA_DLYA_ZAKAZA_KG_VALUE']);
        } else {
            $properties['SHAG_IZMENENIYA_VESA_DLYA_ZAKAZA_KG'] = null;
        }

        $propertiesForUpdate[$productsProperties['ID']] = $properties;

    }

    foreach ($propertiesForUpdate as $productId => $properties) {
        foreach ($properties as $code => $value) {
            CIBlockElement::SetPropertyValuesEx($productId, IblockRepository::getIdByCode('aspro_max_catalog'), [$code => $value]);
        }
    }
}


function checkEmailBeforeUserUpdate(&$arFields): bool
{
    global $APPLICATION;

    if ($arFields["EMAIL"]) {
        $message = 'Почта должна быть в правильном формате и содержать валидный домен';

        if (!filter_var($arFields["EMAIL"], FILTER_VALIDATE_EMAIL)) {
            $APPLICATION->throwException($message);
            return false;
        }

        $domain = substr(strrchr($arFields["EMAIL"], "@"), 1);
        if (!checkdnsrr($domain, "MX") || !checkdnsrr($domain, "A")) {
            $APPLICATION->throwException($message);
            return false;
        }
    }

    return true;
}


/**
 * @throws LoaderException
 * @throws Exception
 */
function disablingEmptyCategories(): void
{
    Loader::includeModule('iblock');

    $iblockId = IblockRepository::getIdByCode('aspro_max_catalog');
    $IblockSection = new \CIBlockSection;
    $sectionsQuery = \CIBlockSection::GetList(
        [],
        ["IBLOCK_ID" => $iblockId, "ACTIVE" => "Y", "ELEMENT_SUBSECTIONS" => "Y"],
        true,
        ["ID", "IBLOCK_ID", "NAME", "TIMESTAMP_X"]
    );

    while ($section = $sectionsQuery->Fetch()) {
        if ($section["ELEMENT_CNT"] == 0) {
            $IblockSection->Update($section["ID"], ["ACTIVE" => "N"]);
        }
    }
}

function OnImportOrder1CDoneHandler(Event $event): void
{
    $orderId = $event->getParameter('orderId');

    if (!$orderId) {
        return;
    }

    (new \DmitrievLab\Domains\Exchange\Exchange())->importOrder1CDone((int)$orderId);

}

function OnImportOrder1CFinishedHandler(): void
{
    (new \DmitrievLab\Domains\Exchange\Exchange())->importOrder1CFinished();
}

function OnImportOrder1CCompleteHandler(): void
{
    (new \DmitrievLab\Domains\Exchange\Exchange())->importOrder1CComplete();
}

function updateUserBonuses(): void
{
    updateBonuses();
}

function setDeliveryData(): void
{
    (new Delivery())->setDefaultData();
}

function removeDeliveryCheckerCookie(): void
{
    (new Delivery())->removeDeliveryChecker();
}