<?php

namespace D<PERSON><PERSON>v<PERSON>ab\Loyalty;

use Bitrix\Main\ObjectException;
use DateTime;
use DateTimeImmutable;
use <PERSON><PERSON>rie<PERSON><PERSON><PERSON>\Utils;

class PlasticBonusCard
{
    /**
     * @throws ObjectException
     */
    public function __construct(
        private readonly string $name,
        private readonly string $surname,
        private readonly string $patronymic,
        private readonly string $birthday,
        private readonly string $phone,
        private readonly string $email,
        private readonly string $cardNumber,
        private readonly string $pin,
    ) {
        $this->assertBirthdayIsValid(new DateTimeImmutable($birthday));
    }

    public function mappingTo1C()
    {
        return [
            'Имя' => $this->name,
            'Фамилия' => $this->surname,
            'Отчество' => $this->patronymic,
            'ДатаРождения' => (new DateTime($this->birthday))->format('d.m.Y'),
            'Телефон' => Utils::clearPhone($this->phone),
            'АдресЭП' => $this->email,
            'Виртуальная' => false,
            'НомерКарты' => $this->cardNumber,
            'ПинКод' => $this->pin,
        ];
    }

    private function assertBirthdayIsValid(DateTimeImmutable $date): void
    {

        if ($date >= new DateTimeImmutable()) {
            throw new ObjectException('Дата дня рождения должна быть в прошлом');
        }

        if ((int)(new DateTime())->diff($date)->format('%y') < 18) {
            throw new ObjectException('Для активации карты вам должно быть не менее 18 лет');
        }

    }

}
