<?php

namespace <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Encryption;

use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Helpers\Config;

class Encryption
{
    private string $key;

    public function __construct()
    {
        $this->key = Config::get('ENCRYPTION_KEY');
    }

    public function encrypt($data): string
    {
        $cipher = "aes-128-cbc";
        $ivLength = openssl_cipher_iv_length($cipher);
        $iv = openssl_random_pseudo_bytes($ivLength);
        $encrypted = openssl_encrypt($data, $cipher, $this->key, 0, $iv);
        return base64_encode($iv . ':' . $encrypted);
    }

    public function decrypt($data): string|false
    {
        list($iv, $encryptedData) = explode(':', base64_decode($data), 2);
        $cipher = "aes-128-cbc";
        return openssl_decrypt($encryptedData, $cipher, $this->key, 0, $iv);
    }
}