<?php

declare(strict_types=1);

namespace DmitrievLab\Domains\Bifit;

use Bitrix\Highloadblock\HighloadBlockTable;
use D<PERSON>riev<PERSON>ab\Domains\Logging\Logger;
use DmitrievLab\Domains\Logging\LoggerChannel;
use Dmitriev<PERSON>ab\Domains\Order\OrderInformation;
use DmitrievLab\Domains\Order\Statuses\OrderStatusSubscriber;
use D<PERSON>rievLab\Domains\Order\Statuses\StatusChangedEvent;
use D<PERSON>rievLab\Helpers\BifitHelper;
use DmitrievLab\Helpers\Config;
use D<PERSON>rievLab\Repository\IblockRepository;

class Bifit implements OrderStatusSubscriber
{

    private string $bifitUrl;
    private string $bifitId;
    private string $contactorId;

    public function __construct()
    {
        $this->bifitUrl = Config::get('BIFIT_URL');
        $this->bifitId = Config::get('BIFIT_ID');
        $this->contactorId = Config::get('CONTACTOR_ID');
    }

    public function orderStatusChangedEvent(StatusChangedEvent $event): void
    {
        if (empty($event->getBifitId()) && ($event->getStatus() === 'S' || $event->getStatus() === 'Q') && !$event->isCanceled()) {
            $this->newOrders($event->getOrderId());
        }
    }


    public function getMarkTable()
    {
        $rew = [];
        $hlblock = HighloadBlockTable::getById(1)->fetch();
        $entity = HighloadBlockTable::compileEntity($hlblock);
        $entity_data_class = $entity->getDataClass();
        $rsData = $entity_data_class::getList(array(
            "select" => array(
                "UF_NAME", "ID"
            ),
            "filter" => array()
        ));
        while ($arData = $rsData->Fetch()) {
            $rew[$arData['ID']] = $arData['UF_NAME'];
        }
        return $rew;
    }

    public function getMark($id)
    {
        $userFieldManager = Main\UserField\Internal\UserFieldHelper::getInstance()->getManager();
        $result = $userFieldManager->GetUserFields(
            \Bitrix\Catalog\ProductTable::getUfId(),
            $id,
        );
        return $result;
    }

    public function CurlFullgetOrders($token, $id)
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->bifitUrl . 'protected/online_orders/' . $id,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept-Encoding: deflate',
                'Authorization: Bearer ' . $token,
                "Content-type: application/json"
            ),
        ));
        $result = curl_exec($curl);
        return json_decode($result);
    }

    public function CurlFullCatalog($token, $mas, $tovar)
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->bifitUrl . 'protected/nomenclatures/list/create',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($mas),
            CURLOPT_HTTPHEADER => array(
                'Accept-Encoding: deflate',
                'Authorization: Bearer ' . $token,
                "Content-type: application/json"
            ),
        ));
        $result = curl_exec($curl);
        $ex = json_decode($result);
        if (is_array($ex)) {
            foreach ($ex as $k => $v) {
                if (!empty($tovar[$k])) {
                    \CIBlockElement::SetPropertyValuesEx($tovar[$k], false, array('BIFIT' => $v));
                }
            }
        }
        return json_decode($result);
    }

    public function CurlUpdateCatalog($token, $mas, $tovar)
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->bifitUrl . 'protected/nomenclatures/' . $tovar,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => 'PUT',
            CURLOPT_POSTFIELDS => json_encode($mas),
            CURLOPT_HTTPHEADER => array(
                'Accept-Encoding: deflate',
                'Authorization: Bearer ' . $token,
                "Content-type: application/json"
            ),
        ));
        $result = curl_exec($curl);
        return json_decode($result);
    }

    public function CurlUpdateCatalogFile($token, $mas, $tovar)
    {
        $filedata = $mas['PREVIEW']['tmp_name'];
        $filesize = $mas['PREVIEW']['tmp_size'];
        $postfields = array('file' => new \CurlFile($mas['PREVIEW']['tmp_name'], 'image/jpeg'));
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->bifitUrl . 'protected/nomenclatures/' . $tovar,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'PUT',
            CURLOPT_POSTFIELDS => $postfields,
            CURLOPT_INFILESIZE => $filesize,
            CURLOPT_HTTPHEADER => array(
                'Accept-Encoding: deflate',
                'Authorization: Bearer ' . $token,
                "Content-Type: multipart/form-data",
            ),
        ));
        $result = curl_exec($curl);
        return json_decode($result);
    }

    public function getToken()
    {
        $mas = [];
        $mas['username'] = '79621871109';
        $mas['password'] = 'tyE2HriLcaV5jA%2BxVUSE8Ik59li4%2BMLcWS52eVrZDyo%3D';
        $mas['client_id'] = 'cashdesk-rest-client';
        $mas['client_secret'] = 'cashdesk-rest-client';
        $mas['grant_type'] = 'password';
        $message = '';
        foreach ($mas as $k => $v) {
            $message .= $k . '=' . $v . '&';
        }
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->bifitUrl . 'oauth/token',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $message,
            CURLOPT_HTTPHEADER => array(
                'Accept-Encoding: deflate',
                "Content-type: application/x-www-form-urlencoded"
            ),
        ));
        $result = curl_exec($curl);
        return json_decode($result);
    }

    public function getCatalog()
    {
        $mark_table = $this->getMarkTable();
        $iterator = \CIBlockElement::GetList(array('id' => 'asc'), array('IBLOCK_ID' => IblockRepository::getIdByCode('aspro_max_catalog'), 'ACTIVE' => 'Y', 'PROPERTY_BIFIT' => false), false, false, array(
            'ID', 'NAME', 'XML_ID', 'IBLOCK_ID', 'WEIGHT',
            'CATALOG_PRICE_6', 'PROPERTY_CML2_BAR_CODE', 'PROPERTY_CML2_ARTICLE'
        ));
        $exit = [];
        $msd = array('8' => 'ALCOHOL', '3' => 'TOBACCO', '4' => 'SHOES', '5' => 'PERFUME', '2' => 'MEDICAMENT');
        while ($row = $iterator->fetch()) {
            $tt = $this->getMark($row['ID']);
            if (!empty($msd[$tt['UF_PRODUCT_GROUP']['VALUE']])) {
                $row['MARK'] = $msd[$tt['UF_PRODUCT_GROUP']['VALUE']];
            } else if (!empty($tt['UF_PRODUCT_GROUP']['VALUE']) && empty($msd[$tt['UF_PRODUCT_GROUP']['VALUE']])) {
                $row['MARK'] = 6;
            } else {
                $row['MARK'] = 'UNKNOWN';
            }
            $exit[] = $row;
        }
        return $exit;
    }

    public function getCatalogBifit()
    {
        $iterator = \CIBlockElement::GetList(array('id' => 'asc'), array('IBLOCK_ID' => IblockRepository::getIdByCode('aspro_max_catalog'), '!PROPERTY_BIFIT' => false), false, false, array(
            'ID', 'NAME', 'XML_ID', 'IBLOCK_ID', 'WEIGHT', 'ACTIVE',
            'CATALOG_PRICE_6', 'PROPERTY_CML2_BAR_CODE', 'PROPERTY_CML2_ARTICLE', 'PROPERTY_BIFIT',
        ));
        $exit = [];
        $mark_table = $this->getMarkTable();
        $msd = array('8' => 'ALCOHOL', '3' => 'TOBACCO', '4' => 'SHOES', '5' => 'PERFUME', '2' => 'MEDICAMENT');
        while ($row = $iterator->fetch()) {
            $tt = $this->getMark($row['ID']);
            if (!empty($msd[$tt['UF_PRODUCT_GROUP']['VALUE']])) {
                $row['MARK'] = $msd[$tt['UF_PRODUCT_GROUP']['VALUE']];
            } else if (!empty($tt['UF_PRODUCT_GROUP']['VALUE']) && empty($msd[$tt['UF_PRODUCT_GROUP']['VALUE']])) {
                $row['MARK'] = 6;
            } else {
                $row['MARK'] = 'UNKNOWN';
            }
            $exit[] = $row;
        }
        return $exit;
    }

    public function getCatalogBifit_file()
    {
        $iterator = \CIBlockElement::GetList(array('id' => 'asc'), array('IBLOCK_ID' => IblockRepository::getIdByCode('aspro_max_catalog'), '!PROPERTY_BIFIT' => false, '!PREVIEW_PICTURE' => false, '!PROPERTY_PICTURE_BIFIT_VALUE' => 1), false, false, array(
            'ID', 'NAME', 'XML_ID', 'IBLOCK_ID', 'WEIGHT', 'ACTIVE', 'PREVIEW_PICTURE', 'PROPERTY_BIFIT',
        ));
        $exit = [];
        while ($row = $iterator->fetch()) {
            $exit[] = $row;
        }
        return $exit;
    }

    public function setCatalogBifit(): static
    {
        $ex = $this->getCatalog();
        $exit = [];
        $mdr = array('1' => 6, '2' => 112, '3' => 163, '4' => 166, '5' => 796, '6' => 778, '7' => 163, '9' => 163, '10' => 163, '11' => 200);
        $ert = [];
        $tovar = [];
        foreach ($ex as $k => $v) {
            $item = [];
            $trade = [];
            $tovar[] = $v['ID'];
            $item['organizationId'] = $this->bifitId;
            $item['barcode'] = $v['PROPERTY_CML2_BAR_CODE_VALUE'];
            $item['vendorCode'] = $v['PROPERTY_CML2_ARTICLE_VALUE'];
            $item['name'] = $v['NAME'];
            $item['vatId'] = 5;
            $item['vatValue'] = 'WITHOUT_VAT';
            $item['unitCode'] = $mdr[$v['CATALOG_MEASURE']];
            $item['purchasePrice'] = '';
            $item['sellingPrice'] = $v['CATALOG_PRICE_6'];
            $item['weighted'] = ($v['CATALOG_MEASURE'] != 5) ? true : false;
            $item['grouped'] = false;
            $item['focused'] = false;
            $item['container'] = false;
            $item['paymentSubject'] = "PRODUCT";
            $item['adultsOnly'] = false;
            $item['code'] = $v['XML_ID'];
            $item['markType'] = $v['MARK'];
            $item['visible'] = ($v['ACTIVE'] == 'Y') ? true : false;
            $item['contractorActivityType'] = 'AGENT';
            $item['contractorId'] = $this->contactorId;
            $item['custom'] = false;
            $item['type'] = "DEFAULT";
            $item['barcodes'] = [];
            $item['countryCode'] = '';
            $item['description'] = '';
            $item['picturesIds'] = [];
            $item['application'] = 'KASSA';
            $ert[] = $item;
            //$exit[] = $item;
        }
        $exit['organizationId'] = $this->bifitId;
        $exit['items'] = $ert;
        $token = $this->getToken();
        if ($token->access_token) {
            $this->CurlFullCatalog($token->access_token, $exit, $tovar);
        }

        return $this;
    }

    public function updateCatalogBifit(): static
    {
        $token = $this->getToken();
        if ($token->access_token) {
            $ex = $this->getCatalogBifit();
            $exit = [];
            $mdr = array('1' => 6, '2' => 112, '3' => 163, '4' => 166, '5' => 796, '6' => 778, '7' => 163, '9' => 163, '10' => 163, '11' => 200);
            $ert = [];
            foreach ($ex as $k => $v) {
                $item = [];
                $item['id'] = $v['PROPERTY_BIFIT_VALUE'];
                $item['organizationId'] = $this->bifitId;
                $item['barcode'] = $v['PROPERTY_CML2_BAR_CODE_VALUE'];
                $item['vendorCode'] = $v['PROPERTY_CML2_ARTICLE_VALUE'];
                $item['name'] = $v['NAME'];
                $item['vatId'] = 5;
                $item['vatValue'] = 'WITHOUT_VAT';
                $item['unitCode'] = $mdr[$v['CATALOG_MEASURE']];
                $item['purchasePrice'] = '';
                $item['sellingPrice'] = $v['CATALOG_PRICE_6'];
                $item['weighted'] = ($v['CATALOG_MEASURE'] != 5) ? true : false;
                $item['grouped'] = false;
                $item['focused'] = false;
                $item['container'] = false;
                $item['paymentSubject'] = "PRODUCT";
                $item['adultsOnly'] = false;
                $item['code'] = $v['XML_ID'];
                $item['markType'] = $v['MARK'];
                $item['visible'] = true;
                $item['contractorActivityType'] = 'AGENT';
                $item['contractorId'] = $this->contactorId;
                $item['custom'] = false;
                $item['type'] = "DEFAULT";
                $item['barcodes'] = [];
                $item['countryCode'] = '';
                $item['description'] = '';
                $item['picturesIds'] = [];
                $item['application'] = 'KASSA';
                $this->CurlUpdateCatalog($token->access_token, $item, $v['PROPERTY_BIFIT_VALUE']);
            }
        }

        return $this;
    }

    public function updateCatalogBifit_file(): static
    {
        $token = $this->getToken();
        if ($token->access_token) {
            $ex = $this->getCatalogBifit_file();
            $exit = [];
            $mdr = array('1' => 6, '2' => 112, '3' => 163, '4' => 166, '5' => 796, '6' => 778, '7' => 163, '9' => 163, '10' => 163, '11' => 200);
            $ert = [];
            foreach ($ex as $k => $v) {
                $item = [];
                $item['PREVIEW'] = \CFile::MakeFileArray($v['PREVIEW_PICTURE']);
                $this->CurlUpdateCatalogFile($token->access_token, $item, $v['PROPERTY_BIFIT_VALUE']);
                \CIBlockElement::SetPropertyValuesEx($v['ID'], false, array('PICTURE_BIFIT' => 1));
            }
        }

        return $this;
    }

    //orders
    public function getProp($PRODUCT_ID, $CODE)
    {
        $db_props = \CIBlockElement::GetProperty(IblockRepository::getIdByCode('aspro_max_catalog'), $PRODUCT_ID, array("sort" => "asc"), array("CODE" => $CODE));
        if ($ar_props = $db_props->Fetch()) {
            return $ar_props;
        }
    }

    public function CurlFullNewOrders($token, $mas)
    {
        (new Logger())
            ->setLogger(LoggerChannel::BIFIT)
            ->debug('protected/online_orders',
                [
                    'token' => $token,
                    'data' => $mas,
                ]
            );
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->bifitUrl . 'protected/online_orders',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($mas),
            CURLOPT_HTTPHEADER => array(
                'Accept-Encoding: deflate',
                'Authorization: Bearer ' . $token,
                "Content-type: application/json"
            ),
        ));
        $result = curl_exec($curl);
        return json_decode($result);
    }

    public function CurlFullNewOrdersPayments($token, $id, $sum)
    {
        $mas = [];
        $mas['onlineOrderId'] = $id;
        $mas['calculationMethod'] = 'PREPAY_FULL';
        $mas['organizationId'] = $this->bifitId;
        $mas['comment'] = 'Онлайн-оплата';
        $mas['total'] = $sum;
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->bifitUrl . 'protected/online_orders/payments',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($mas),
            CURLOPT_HTTPHEADER => array(
                'Accept-Encoding: deflate',
                'Authorization: Bearer ' . $token,
                "Content-type: application/json"
            ),
        ));
        $result = curl_exec($curl);
        return json_decode($result);
    }

    public function newOrders($id)
    {
        $mas = [];
        $order = \Bitrix\Sale\Order::load($id);
        $paymentIds = [];
        $paymentIds = $order->getPaymentSystemId();
        $propertyCollection = $order->getPropertyCollection();
        $name_prop = $propertyCollection->getItemByOrderPropertyCode('FIO');
        $phone_prop = $propertyCollection->getItemByOrderPropertyCode('PHONE');
        $adres_prop = $propertyCollection->getItemByOrderPropertyCode('ADDRESS');
        $kvartira_dost = $propertyCollection->getItemByOrderPropertyCode('NOMER_KV');
        $etag_dost = $propertyCollection->getItemByOrderPropertyCode('ETAZ');
        $podezd_dost = $propertyCollection->getItemByOrderPropertyCode('PODEZD');
        $domofon_dost = $propertyCollection->getItemByOrderPropertyCode('CODE_DOMOFONE');
        $time_dost_prop = $propertyCollection->getItemByOrderPropertyCode('TIME_DOSTAVKA');
        $date_dost_prop = $propertyCollection->getItemByOrderPropertyCode('DATE_DOSTAVKI');
        $lift_prop = $propertyCollection->getItemByOrderPropertyCode('NO_LIFT');
        $comment_prop = $propertyCollection->getItemByOrderPropertyCode('COMMEN_DELIVERY');
        $shipmentCollection = $order->getShipmentCollection()->getNotSystemItems();
        $store_id = 0;
        $info_store = '';
        foreach ($shipmentCollection as $shipment) {
            $store_id = $shipment->getStoreId();
        }
        if ($store_id > 0) {
            $arStore = \Bitrix\Catalog\StoreTable::getById($store_id)->fetch();
            $info_store = $arStore['TITLE'];
        }
        $adress = '';
        $comment = '';
        if (is_object($adres_prop)) {
            $adress = $adres_prop->getValue() . ', ';
        }
        if (is_object($podezd_dost)) {
            $adress .= 'Подъезд: ' . $podezd_dost->getValue() . ', ';
        }
        if (is_object($domofon_dost)) {
            $adress .= 'Код домофона: ' . $domofon_dost->getValue() . ', ';
        }
        if (is_object($etag_dost)) {
            $adress .= 'Этаж: ' . $etag_dost->getValue() . ', ';
        }
        if (is_object($lift_prop)) {
            $adress .= 'Лифт: ' . $lift_prop->getValue() . ', ';
        }
        if (is_object($kvartira_dost)) {
            $adress .= 'Квартира: ' . $kvartira_dost->getValue() . ', ';
        }
        if (is_object($comment_prop)) {
            $comment = $comment_prop->getValue();
        }
        $date_delivery = '';
        if (is_object($date_dost_prop) && is_object($time_dost_prop)) {
            $date_delivery = $date_dost_prop->getValue() . ' ' . $time_dost_prop->getValue();
        }
        $milliseconds = round(microtime(true) * 1000);

        $deliveryTimestamp = BifitHelper::getDeliveryTimestamp($date_dost_prop->getValue(), $time_dost_prop->getValue());

        if ($deliveryTimestamp) {
            $mas['onlineOrder']['deliveryTime'] = $deliveryTimestamp;
        }

        $mas['onlineOrder']['visible'] = true;
        $mas['onlineOrder']['created'] = $milliseconds;
        $mas['onlineOrder']['changed'] = $milliseconds;
        $mas['onlineOrder']['externalId'] = $order->getField('ACCOUNT_NUMBER');
        $mas['onlineOrder']['organizationId'] = $this->bifitId;
        $mas['onlineOrder']['tradeObjectId'] = '*************-*********';
        $mas['onlineOrder']['taxSystem'] = 'COMMON';
        $mas['onlineOrder']['deliveryType'] = 'COURIER';
        $mas['onlineOrder']['deliveryAmount'] = $order->getDeliveryPrice();
        if (!in_array(14, $paymentIds)) {
            $mas['onlineOrder']['paid'] = false;
        } else {
            $mas['onlineOrder']['paid'] = $order->isPaid();
        }
        $mas['onlineOrder']['orderTime'] = $milliseconds;
        $mas['onlineOrder']['address'] = $adress;
        $mas['onlineOrder']['totalAmount'] = OrderInformation::getOrderPayment((int)$id);
        $mas['onlineOrder']['comment'] = 'ТТ:' . $info_store . '  Дата-время доставки:' . $date_delivery . ', Адрес: ' . $adress;
        $mas['onlineOrder']['currentStatusType'] = 'ACCEPTED';
        $mas['onlineOrder']['currentStatusTime'] = $milliseconds;
        //basket
        $items = [];
        $exit = [];
        $withMarking = [];

        foreach ($shipmentCollection as $shipment) {
            $shipmentItemCollection = $shipment->getShipmentItemCollection();
            foreach ($shipmentItemCollection as $item) {
                $shipmentItemStoreCollection = $item->getShipmentItemStoreCollection();
                foreach ($shipmentItemStoreCollection as $v) {
                    $withMarking[$v->getField('ID')] = $item->getField('BASKET_ID');
                    $items[$v->getField('ID')]['BASKET_ID'] = $item->getField('BASKET_ID');
                    $items[$v->getField('ID')]['QUANTITY'] = $item->getField('QUANTITY');
                    $items[$v->getField('ID')]['MARKING_CODE'] = $v->getField('MARKING_CODE');
                }
            }
        }
        $marking = [];
        foreach ($items as $kv => $vv) {
            if (!in_array($vv['MARKING_CODE'], $marking)) {
                $marking[] = $vv['MARKING_CODE'];
            } else {
                unset($items[$kv]);
            }
        }
        unset($marking);
        unset($kv);
        $basket = $order->getBasket();
        $ee = 0;
        $msd = [];
        foreach ($items as $v) {
            foreach ($basket as $basketItem) {
                if ($basketItem->getField('ID') == $v['BASKET_ID']) {
                    if (in_array($basketItem->getField('ID'), array_unique($withMarking)) && !empty($withMarking)) {
                        $exit[$ee]['quantity'] = 1;
                    } else {
                        $exit[$ee]['quantity'] = number_format($v['QUANTITY'], 0, '', '');
                    }
                    $msd[] = $v['BASKET_ID'];
                    $id_n = $this->getProp($basketItem->getField('PRODUCT_ID'), 'BIFIT');

                    //$exit[$ee]['nomenclatureId'] = $id_n['VALUE'];
                    $exit[$ee]['description'] = $basketItem->getField('NAME');
                    $exit[$ee]['price'] = round($basketItem->getField('PRICE'), 2);
                    $exit[$ee]['paymentSubject'] = 'PRODUCT';
                    $exit[$ee]['contractorActivityType'] = 'AGENT';
                    $exit[$ee]['markType'] = 'OTHER';
                    //$exit[$ee]['gs'] = '\u001d';
                    $exit[$ee]['gs'] = 'GS1';
                    $exit[$ee]['ignoreMarkingError'] = true;
                    $exit[$ee]['codeForOfd'] = base64_decode($v['MARKING_CODE']);
                    $exit[$ee]['rawCode'] = base64_decode($v['MARKING_CODE']);
                    $exit[$ee]['groupSeparator'] = 'GS1';

                    if (in_array(14, $paymentIds)) {
                        $exit[$ee]['calculationMethod'] = 'PREPAY_FULL';
                    } else {
                        $exit[$ee]['calculationMethod'] = 'FULL_PAY';
                    }
                    $collection = $basketItem->getPropertyCollection();
                    foreach ($collection as $vs) {
                        if ($vs->getField('CODE') == 'NAME_PROIZVODITEL') {
                            $exit[$ee]['contractorName'] = $vs->getField('VALUE');
                        }
                        if ($vs->getField('CODE') == 'INN_PROIZVODITEL') {
                            $exit[$ee]['contractorInn'] = $vs->getField('VALUE');
                        }
                        if ($vs->getField('CODE') == 'CONTACT_PROIZVODITEL') {
                            $exit[$ee]['contractorPhone'] = str_replace(array(" ", "+", "(", ")"), "", $vs->getField('VALUE'));
                        }
                        if ($vs->getField('CODE') == 'VAT') {
                            if ($vs->getField('VALUE') == 'БезНДС') {
                                $exit[$ee]['vatValue'] = '';
                            } else {
                                $exit[$ee]['vatValue'] = $vs->getField('VALUE');
                            }
                        }
                    }
                    $ee++;
                }
            }
        }
        foreach ($basket as $basketItem) {
            if (in_array($basketItem->getField('ID'), $msd)) continue;
            $id_n = $this->getProp($basketItem->getField('PRODUCT_ID'), 'BIFIT');
            //$exit[$ee]['nomenclatureId'] = $id_n['VALUE'];
            $exit[$ee]['description'] = $basketItem->getField('NAME');
            $exit[$ee]['quantity'] = number_format((float)$basketItem->getField('QUANTITY'), 3, '.', '');
            $exit[$ee]['price'] = round($basketItem->getField('PRICE'), 2);
            $exit[$ee]['paymentSubject'] = 'PRODUCT';
            $exit[$ee]['contractorActivityType'] = 'AGENT';

            if (in_array(14, $paymentIds)) {
                //$exit[$ee]['calculationMethod'] = 'PREPAY_FULL';
                $exit[$ee]['calculationMethod'] = 'FULL_PAY';
            } else {
                $exit[$ee]['calculationMethod'] = 'FULL_PAY';
            }
            $collection = $basketItem->getPropertyCollection();
            foreach ($collection as $v) {
                if ($v->getField('CODE') == 'NAME_PROIZVODITEL') {
                    $exit[$ee]['contractorName'] = $v->getField('VALUE');
                }
                if ($v->getField('CODE') == 'INN_PROIZVODITEL') {
                    $exit[$ee]['contractorInn'] = $v->getField('VALUE');
                }
                if ($v->getField('CODE') == 'CONTACT_PROIZVODITEL') {
                    $exit[$ee]['contractorPhone'] = str_replace(array(" ", "+", "(", ")"), "", $v->getField('VALUE'));
                }

                if ($v->getField('CODE') == 'VAT') {
                    if ($v->getField('VALUE') == 'БезНДС') {
                        $exit[$ee]['vatValue'] = '';
                    } else {
                        $exit[$ee]['vatValue'] = $v->getField('VALUE');
                    }
                }
            }
            $ee++;
        }

        if ($order->getDeliveryPrice() > 0) {
            $exit[$ee]['description'] = 'Доставка';
            $exit[$ee]['quantity'] = 1;
            $exit[$ee]['price'] = round($order->getDeliveryPrice(),2);
            $exit[$ee]['paymentSubject'] = 'SERVICE';
            $exit[$ee]['vatValue'] = '20';
            $exit[$ee]['contractorName'] = 'ООО Декс';
            $exit[$ee]['contractorInn'] = '4400002754';
            $exit[$ee]['contractorPhone'] = '79621871109';
            if (in_array(14, $paymentIds)) {
                $exit[$ee]['calculationMethod'] = 'PREPAY_FULL';
            } else {
                $exit[$ee]['calculationMethod'] = 'FULL_PAY';
            }
        }
        $mas['onlineOrderItems'] = $exit;
        if ($order->isPaid()) {
            // $mas['onlineOrderPayments'][0]['id'] = 0;
            // $mas['onlineOrderPayments'][0]['calculationMethod'] = 'FULL_PAY';
            // $mas['onlineOrderPayments'][0]['total'] = $order->getPrice();
            // $mas['onlineOrderPayments'][0]['paymentTime'] = $milliseconds;
            // $mas['onlineOrderPayments'][0]['paid'] = true;
            // $mas['onlineOrderPayments'][0]['created'] = $milliseconds;
            // $mas['onlineOrderPayments'][0]['comment'] = 'Оплата-онлайн';
        } else {
            $mas['properties']['fullSettlement'] = true;
        }
        //если оплатил бонусами
        $paymentCollection = $order->getPaymentCollection();
        $sum_opl = 0;
        foreach ($paymentCollection as $payment) {
            if ($payment->getSum() > 0 && $payment->isInner()) {
                $sum_opl = $payment->getSum();
            }
        }

        //client
        $mas['client']['organizationId'] = $this->bifitId;
        $mas['client']['firstName'] = $name_prop->getValue();
        $mas['client']['address'] = $adress;
        $mas['client']['phone'] = $phone_prop->getValue();
        $mas['client']['email'] = $propertyCollection->getUserEmail()->getValue();
        $mas['client']['comment'] = '';
        //curl
        $we = true;
        if ($_REQUEST['dds'] == 'Y') {
            $we = false;
            $json_output = json_encode($mas, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            print('<pre>');
            print_r($json_output);
            print('</pre>');
        }


        $token = $this->getToken();
        if ($token->access_token && $we) {
            $id_bitos = $this->CurlFullNewOrders($token->access_token, $mas);

            (new Logger())
                ->setLogger(LoggerChannel::BIFIT)
                ->debug('Создали заказ в БИФИТ',
                    [
                        'orderId' => $order->getField('ACCOUNT_NUMBER'),
                        'bifit' => $id_bitos,
                        'request' => $mas,
                    ]
                );

            if ($id_bitos->type != 'ERROR') {
                $historyEntityType = 'ORDER';
                $historyType = 'ORDER_COMMENTED';
                \Bitrix\Sale\OrderHistory::addAction(
                    $historyEntityType,
                    $order->getId(),
                    $historyType,
                    $order->getId(),
                    $order,
                    ['COMMENTS' => 'Создали заказ в БИФИТ']
                );

                if (in_array(14, $paymentIds)) {
                    $fr = $this->CurlFullNewOrdersPayments(
                        $token->access_token,
                        $id_bitos,
                        OrderInformation::getOrderPayment((int)$id)
                    );

                    (new Logger())
                        ->setLogger(LoggerChannel::BIFIT)
                        ->debug('Указали оплату для БИФИТ',
                            [
                                'orderId' => $order->getField('ACCOUNT_NUMBER'),
                                'bifit' => $fr,
                                'price' => OrderInformation::getOrderPayment((int)$id),
                            ]
                        );

                    $historyEntityType = 'ORDER';
                    $historyType = 'ORDER_COMMENTED';
                    \Bitrix\Sale\OrderHistory::addAction(
                        $historyEntityType,
                        $order->getId(),
                        $historyType,
                        $order->getId(),
                        $order,
                        ['COMMENTS' => 'Указали оплату для БИФИТ']
                    );
                }
                $bitos_prop = $propertyCollection->getItemByOrderPropertyCode('ID_BIFIT');
                $bitos_prop->setValue($id_bitos);
                $order->setField('STATUS_ID', 'Q');
                $r = $order->save();
            } else {
                (new Logger())
                    ->setLogger(LoggerChannel::BIFIT)
                    ->error('newOrders ошибка',
                        [
                            'orderId' => $order->getField('ACCOUNT_NUMBER'),
                            'bifit' => $id_bitos,
                            'request' => $mas
                        ]
                    );
            }
            return $id_bitos;
        }
    }

    public function getOrder($id, $token)
    {
        $order = \Bitrix\Sale\Order::load($id);
        $propertyCollection = $order->getPropertyCollection();
        $bitos_prop = $propertyCollection->getItemByOrderPropertyCode('ID_BIFIT');
        if (is_object($bitos_prop)) {
            if (!empty($bitos_prop->getValue())) {
                return $this->CurlFullgetOrders($token, $bitos_prop->getValue());
            }
        }
    }

    public function setStatus($id, $status)
    {
        $order = \Bitrix\Sale\Order::load($id);
        $order->setField('STATUS_ID', $status);
        //if (!$order->isPaid()) {
        //$order->setField('PAYED', 'Y');
        $paymentCollection = $order->getPaymentCollection();
        foreach ($paymentCollection as $paymentItem) {
            $paymentItem->setPaid("Y");
        }
        //}
        $r = $order->save();
        return $r;
    }

    public function getOrderList($status)
    {
        $token = $this->getToken();
        if ($token->access_token) {
            $dbRes = \Bitrix\Sale\Order::getList([
                'select' => [
                    "ID", "ACCOUNT_NUMBER"
                ],
                'filter' => [
                    '>ID' => 998,
                    'PROPERTY.ORDER_PROPS_ID' => 41,
                    '!PROPERTY.VALUE' => false,
                    'CANCELED' => 'N',
                    '!STATUS_ID' => 'F'
                ],
            ]);
            while ($order = $dbRes->fetch()) {
                $dd = $this->getOrder($order['ID'], $token->access_token);
                //if ($dd->currentStatusType == 'FINISHED' || $dd->currentStatusType == 'ACCEPTED') {
                if ($dd->currentStatusType == 'FINISHED') {
                    $this->setStatus($order['ID'], $status);
                }
            }
        }
    }
}
