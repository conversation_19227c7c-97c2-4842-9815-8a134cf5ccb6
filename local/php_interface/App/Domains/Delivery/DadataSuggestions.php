<?php

declare(strict_types=1);

namespace D<PERSON><PERSON>v<PERSON>ab\Domains\Delivery;

use Dadata\DadataClient;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Helpers\Config;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class DadataSuggestions implements Suggestions
{
    protected DadataClient $client;
    private string $token;
    private string $secretKey;

    /**
     * @throws Exception
     */
    public function __construct()
    {
        try {
            $this->token = Config::get('DADATA_TOKEN');
            $this->secretKey = Config::get('DADATA_SECRET_KEY');

            $this->client = new DadataClient($this->token, $this->secretKey);
        } catch (\Throwable $exception) {
            throw new Exception(
                sprintf('Ошибка инициализации DaData %s', $exception->getMessage())
            );
        }
    }

    /**
     * @throws GuzzleException
     */
    public function getSuggestionsByAddress($data)
    {
        return $this->client->suggest("address", $data['query'], $data['count'], ['locations' => $data['locations']]);
    }

    /**
     * @throws GuzzleException
     */
    public function getAddressByGeo($lat, $lon)
    {
        return $this->client->geolocate("address", $lat, $lon);
    }
}