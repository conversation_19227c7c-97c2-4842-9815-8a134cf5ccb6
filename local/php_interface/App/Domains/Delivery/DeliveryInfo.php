<?php

declare(strict_types=1);

namespace DmitrievLab\Domains\Delivery;

class DeliveryInfo
{
    public function __construct(
        private readonly float $minimalTotalPrice,
        private readonly float $price,
        private readonly float $freeFrom,
    )
    {
    }

    public function getMinimalTotalPrice(): float
    {
        return $this->minimalTotalPrice;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function getFreeFrom(): float
    {
        return $this->freeFrom;
    }
}