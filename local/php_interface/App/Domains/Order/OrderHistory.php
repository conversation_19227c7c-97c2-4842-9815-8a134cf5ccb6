<?php

declare(strict_types=1);

namespace Dmitriev<PERSON>ab\Domains\Order;

use Bitrix\Sale\Order;
use DmitrievLab\Domains\Payments\Yookassa\Observer\PaymentEvent;
use DmitrievLab\Domains\Payments\Yookassa\Observer\PaymentSubscriber;

class OrderHistory implements PaymentSubscriber
{

    public function update(PaymentEvent $event): void
    {
        $order = Order::load((int)$event->getOrderId());

        \Bitrix\Sale\OrderHistory::addAction(
            'ORDER',
            $order->getId(),
            'ORDER_COMMENTED',
            $order->getId(),
            $order,
            ['COMMENTS' => $event->getMessage()]
        );
    }
}