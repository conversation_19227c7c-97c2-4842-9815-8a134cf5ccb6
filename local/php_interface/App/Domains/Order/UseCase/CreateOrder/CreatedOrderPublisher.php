<?php

declare(strict_types=1);

namespace DmitrievLab\Domains\Order\UseCase\CreateOrder;

class CreatedOrderPublisher implements CreatedOrderPublisherInterface
{
    private \SplObjectStorage $observers;

    public function __construct()
    {
        $this->observers = new \SplObjectStorage;
    }

    public function subscribe(CreatedOrderSubscriber $subscriber): void
    {
        $this->observers->attach($subscriber);
    }

    public function unsubscribe(CreatedOrderSubscriber $subscriber): void
    {
        $this->observers->detach($subscriber);
    }

    public function notify(CreatedOrderEvent $event): void
    {
        foreach ($this->observers as $observer) {
            $observer->orderCreated($event);
        }
    }
}