<?php

declare(strict_types=1);

namespace DmitrievLab\Domains\Order;

use DateInterval;
use DatePeriod;
use DateTime;

class DeliveryInterval
{
    private DatePeriod $intervals;

    public function __construct()
    {
        date_default_timezone_set('Europe/Moscow');
        $this->generateIntervals();
    }

    private function generateIntervals()
    {
        $this->intervals = new DatePeriod(
            new DateTime('09:00'),
            new DateInterval('PT1H'),
            new DateTime('22:00')
        );
    }

    private function getFormattedIntervals(bool $currentDay)
    {
        $formattedIntervals = [];
        foreach ($this->intervals as $value) {
            $currentTime = new DateTime();
            $currentTime->add(new DateInterval('PT30M'));
            $currentTime = $currentTime->getTimestamp();

            if ($currentDay && $currentTime >= $value->getTimestamp()) {
                continue;
            }
            $formattedIntervals[] = $value->format('H:00');
        }
        return $formattedIntervals;
    }

    public function getIntervals($currentDay = false)
    {
        $intervals = [];
        $times = $this->getFormattedIntervals($currentDay);

        foreach ($times as $key => $time) {
            $startTime = $time;
            $endTime = $times[$key + 1];
            $intervals[] = $startTime . '-' . $endTime;
        }

        array_pop($intervals);

        return $intervals;
    }

}
