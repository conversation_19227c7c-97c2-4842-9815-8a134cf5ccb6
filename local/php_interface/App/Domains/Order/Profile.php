<?php

namespace D<PERSON>rievLab\Domains\Order;

use CSaleOrderUserProps;
use CSaleOrderUserPropsValue;
use DmitrievLab\Domains\Order\UseCase\CreateOrder\CreatedOrderEvent;
use DmitrievLab\Domains\Order\UseCase\CreateOrder\CreatedOrderSubscriber;
use Bitrix\Main\Engine\CurrentUser;

// TODO логировать ошибки при сохранении данных
class Profile implements CreatedOrderSubscriber
{
    public function getBuyerProfile(int $userId = null): array
    {
        if($userId === null && !(int)CurrentUser::get()->getId())  return [];

        $userProfilesQuery = CSaleOrderUserProps::GetList(
            ["DATE_UPDATE" => "DESC"],
            ["USER_ID" => ($userId === null) ? (int)CurrentUser::get()->getId() : $userId ],
        );

        $profileId = null;
        while ($userProfile = $userProfilesQuery->Fetch()) {
            $profileId = $userProfile['ID'];
        }

        $profilePropertiesQuery = CSaleOrderUserPropsValue::GetList(array("ID" => "ASC"), array("USER_PROPS_ID" => $profileId));
        while ($profileProperty = $profilePropertiesQuery->Fetch()) {
            $userProfileOrder[$profileProperty['PROP_CODE']] = $profileProperty;
        }

        return $userProfileOrder ?? [];

    }

    private function updateUser(CreatedOrderEvent $event): void
    {
        [$userSurname, $userName, $userPatronymic] = explode(" ", $event->getRequest()->fio);
        global $USER;
        $USER->Update($event->getUserId(), [
            'NAME' => $userName,
            'LAST_NAME' => $userSurname,
            'SECOND_NAME' => $userPatronymic,
            'EMAIL' => $event->getRequest()->email,
            'PERSONAL_PHONE' => $event->getRequest()->phone,
        ]);
    }

    private function updateBuyerProfile(CreatedOrderEvent $event): void
    {
        $userProfilesQuery = CSaleOrderUserProps::GetList(
            ["DATE_UPDATE" => "DESC"],
            ["USER_ID" => $event->getUserId()]
        );

        $profileId = null;
        while ($userProfile = $userProfilesQuery->Fetch()) {
            $profileId = $userProfile['ID'];
        }

        if ($profileId === null) {

            $this->createNewBuyerProfile($event);

        } else {
            $this->updateExistUserBuyerProfile($profileId, $event);
        }
    }

    private function updateExistUserBuyerProfile(mixed $profileId, CreatedOrderEvent $event): void
    {
        $profilePropertiesQuery = CSaleOrderUserPropsValue::GetList(array("ID" => "ASC"), array("USER_PROPS_ID" => $profileId));
        while ($profileProperty = $profilePropertiesQuery->Fetch()) {
            $userProfileOrder[$profileProperty['PROP_CODE']] = $profileProperty;
        }

        [$userSurname, $userName, $userPatronymic] = explode(" ", $event->getRequest()->fio);

        foreach ($userProfileOrder as $key => $profileValue) {
            $arFields = [
                'USER_PROPS_ID' => $profileValue["USER_PROPS_ID"],
                'ORDER_PROPS_ID' => $profileValue["ORDER_PROPS_ID"],
                'NAME' => $profileValue["NAME"]
            ];

            switch ($key) {
                case 'FIO':
                    $arFields['VALUE'] = $userSurname . ' ' . $userName . ' ' . $userPatronymic;
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
                case 'EMAIL':
                    $arFields['VALUE'] = $event->getRequest()->email;
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
                case 'PHONE':
                    $arFields['VALUE'] = $event->getRequest()->phone;
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
                case 'ADDRESS':
                    $arFields['VALUE'] = $event->getRequest()->address;
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
                case 'NOMER_KV':
                    $arFields['VALUE'] = $event->getRequest()->numberApartment;
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
                case 'ETAZ':
                    $arFields['VALUE'] = $event->getRequest()->floor;
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
                case 'PODEZD':
                    $arFields['VALUE'] = $event->getRequest()->entrance;
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
                case 'CODE_DOMOFONE':
                    $arFields['VALUE'] = $event->getRequest()->intercomCode;
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
                case 'NO_LIFT':
                    $arFields['VALUE'] = $event->getRequest()->lift;
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
                case 'COMMEN_DELIVERY':
                    $arFields['VALUE'] = $event->getRequest()->commentOrder;
                    CSaleOrderUserPropsValue::Update($profileValue["ID"], $arFields);
                    break;
            }
        }
    }

    private function createNewBuyerProfile(CreatedOrderEvent $event): void
    {
        $arProfileProps = [
            [
                "ORDER_PROPS_ID" => 1,
                "NAME" => "ФИО",
                "VALUE" => $event->getRequest()->fio
            ],
            [
                "ORDER_PROPS_ID" => 2,
                "NAME" => "E-Mail",
                "VALUE" => $event->getRequest()->email
            ],
            [
                "ORDER_PROPS_ID" => 3,
                "NAME" => "Телефон",
                "VALUE" => $event->getRequest()->phone
            ],
            [
                "ORDER_PROPS_ID" => 7,
                "NAME" => "Адрес доставки",
                "VALUE" => $event->getRequest()->address
            ],
            [
                "ORDER_PROPS_ID" => 22,
                "NAME" => "Номер квартиры / офис",
                "VALUE" => $event->getRequest()->numberApartment
            ],
            [
                "ORDER_PROPS_ID" => 23,
                "NAME" => "Этаж",
                "VALUE" => $event->getRequest()->floor
            ],
            [
                "ORDER_PROPS_ID" => 24,
                "NAME" => "Подъезд",
                "VALUE" => $event->getRequest()->entrance
            ],
            [
                "ORDER_PROPS_ID" => 25,
                "NAME" => "Код домофона",
                "VALUE" => $event->getRequest()->intercomCode
            ],
            [
                "ORDER_PROPS_ID" => 26,
                "NAME" => "Нет лифта",
                "VALUE" => $event->getRequest()->lift
            ],
            [
                "ORDER_PROPS_ID" => 27,
                "NAME" => "Комментарий по доставке",
                "VALUE" => $event->getRequest()->commentOrder
            ],

        ];


        $arFields = [
            "NAME" => $event->getRequest()->fio,
            "USER_ID" => $event->getUserId(),
            "PERSON_TYPE_ID" => 1
        ];
        $profileId = CSaleOrderUserProps::Add($arFields);

        foreach ($arProfileProps as $arProp) {
            CSaleOrderUserPropsValue::Add(
                array_merge($arProp, [
                    "USER_PROPS_ID" => $profileId,
                ])
            );
        }
    }

    public function orderCreated(CreatedOrderEvent $event): void
    {
        $this->updateUser($event);
        $this->updateBuyerProfile($event);
    }
}