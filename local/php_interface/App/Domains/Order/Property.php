<?php

declare (strict_types=1);

namespace DmitrievLab\Domains\Order;

use Bitrix\Main\ArgumentException;
use Bitrix\Main\NotImplementedException;
use Bitrix\Main\ObjectPropertyException;
use Bitrix\Main\SystemException;
use Bitrix\Sale\Order;
use Bitrix\Sale\PropertyValue;

class Property
{
    protected Order $order;

    public function setOrder(Order $order): static
    {
        $this->order = $order;

        return $this;

    }

    /**
     * @throws ArgumentException
     * @throws ObjectPropertyException
     * @throws SystemException
     * @throws NotImplementedException
     */
    public function getOrderPropertyByCode($code)
    {
        /** @var PropertyValue $property */
        foreach ($this->order->getPropertyCollection() as $property) {
            if ($property->getField('CODE') === $code) {
                return $property->getValue();
            }
        }

        return null;
    }
}