<?php

declare(strict_types=1);

namespace DmitrievLab\Domains\Exchange\Bonuses;

use Symfony\Component\Validator\Constraints as Assert;

final readonly class OrderBonusesSyncDto
{
    /* @param OrderBonusDto[] $orders */
    public function __construct(
        #[Assert\Valid]
        public array $orders,
    )
    {
    }

    /* @param array<array{orderId: int, bonuses: float}> $orders */
    public static function fromArray(array $orders): self
    {
        $ordersBonuses = array_map(
            fn(array $item) => new OrderBonusDto(
                orderId: (int)$item['orderId'],
                bonuses: (float)$item['bonuses'],
            ),
            $orders
        );

        return new self($ordersBonuses);
    }
}