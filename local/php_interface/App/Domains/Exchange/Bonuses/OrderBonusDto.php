<?php

declare(strict_types=1);

namespace DmitrievLab\Domains\Exchange\Bonuses;

use Symfony\Component\Validator\Constraints as Assert;

final class OrderBonusDto
{
    public function __construct(
        #[Assert\Positive(message: 'Номер заказ должен быть положительным числом')]
        public int   $orderId,

        #[Assert\PositiveOrZero(message: 'Количество бонусов должно быть больше или равно нулю')]
        public float $bonuses,
    )
    {
    }
}