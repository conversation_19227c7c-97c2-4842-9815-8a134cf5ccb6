<?php

declare(strict_types=1);

namespace D<PERSON>riev<PERSON>ab\Domains\Exchange;

use Dmitriev<PERSON>ab\Domains\Logging\Logger;
use Dmitriev<PERSON>ab\Domains\Logging\LoggerChannel;
use Throwable;

abstract class SwitchActiveManager
{
    protected \Monolog\Logger $logger;

    public function __construct()
    {
        $this->logger = (new Logger())
            ->setLogger(LoggerChannel::EXCHANGE);
    }

    protected function setActive(array $elementsFromDb, Table $table, array $activeElementsFrom1C): void
    {
        $deactivateElements = array_diff(array_column($elementsFromDb, 'XML_ID'), $activeElementsFrom1C);

        $this->prepareSql(ActiveType::INACTIVE, $table, $deactivateElements);
        $this->prepareSql(ActiveType::ACTIVE, $table, $activeElementsFrom1C);
    }

    private function prepareSql(ActiveType $type, Table $table, array $elements): void
    {
        $elementsWithQuotes = array_map(function ($element) {
            return sprintf("%s$element%s", "'", "'");
        }, $elements);

        $preparedSql = implode(',', [...$elementsWithQuotes]);

        // TODO вынести SQL в свои классы
        if ($table->value === 'b_iblock_section') {
            $query = sprintf(
                "UPDATE %s SET ACTIVE = '%s', GLOBAL_ACTIVE = '%s' WHERE XML_ID IN ($preparedSql) AND IBLOCK_ID = 26;",
                $table->value,
                $type->value,
                $type->value
            );
        } else {
            $query = sprintf(
                "UPDATE %s SET ACTIVE = '%s' WHERE XML_ID IN ($preparedSql);",
                $table->value,
                $type->value
            );
        }

        try {
            \Bitrix\Main\Application::getConnection()->query($query);
        } catch (Throwable $exception) {
            $this->logger->error('Ошибка при установке активности товаров/категорий', [
                'exception' => $exception,
                'time' => date("d.m.Y H:i:s")
            ]);
        }
    }

    abstract protected function getData(): array;

    abstract public function disable(ActiveDto $dto): void;
}