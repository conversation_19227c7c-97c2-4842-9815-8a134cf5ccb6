<?php

declare(strict_types=1);

namespace DmitrievLab\Domains\User;

use Bitrix\Main\Mail\Event;
use CUser;

class Profile
{
    public function delete(int $userId): void
    {
        \Bitrix\Main\Loader::includeModule('form');
        $userData = \CUser::GetByID($userId)->fetch();
        $name = trim(
            (!empty($userData['LAST_NAME']) ? $userData['LAST_NAME'] . ' ' : '') .
            (!empty($userData['NAME']) ? $userData['NAME'] . ' ' : '') .
            (!empty($userData['SECOND_NAME']) ? $userData['SECOND_NAME'] : '')
        );
        $email = $userData['EMAIL'];
        $phone = $userData['PERSONAL_PHONE'];

        $arValues = array(
            "form_text_75" => $name,
            "form_text_74" => $email,
            "form_text_76" => $phone
        );

        if (\CFormResult::Add(17, $arValues)) {
            $this->sendEventDelete($arValues);
        }
    }

    private function sendEventDelete($arValues): void
    {
        Event::send(array(
            "EVENT_NAME" => "SEND_DELETE",
            "LID" => "s1",
            "C_FIELDS" => array(
                "NAME" => $arValues['new_field_63797'],
                "EMAIL" => $arValues['new_field_83679'],
                "PHONE" => $arValues['new_field_21423'],
            ),
        ));
    }

    public function checkRequestDeleteAccount(): bool
    {
        global $USER, $APPLICATION;
        $currentUri = $APPLICATION->GetCurUri();
        $isPersonalSection = strpos($currentUri, '/personal/') === 0;
        $isUserAuthorized = $USER->IsAuthorized();
        return $isPersonalSection ? $isUserAuthorized : false;
    }

    public static function updateData($id, $fields = []): bool
    {
        if ($id > 0 && (new CUser)->Update($id, $fields)) return true;

        return false;
    }

    public static function getData(int $id): array
    {
        return \CUser::GetByID($id)->Fetch();
    }

    public function getNumberById(int $userId): string|null
    {
        $user = \Bitrix\Main\UserPhoneAuthTable::getList([
            'select' => ['PHONE_NUMBER'],
            'filter' => ['=USER_ID' => $userId],
        ])->fetch();

        if ($user !== false) {
            return (string)$user['PHONE_NUMBER'];
        }

        return null;
    }

    public function getIdbyNumber(string $numberPhone): string|null
    {
        $numberPhone = preg_replace('/[^+0-9]/m', "", $numberPhone);

        $user = \Bitrix\Main\UserPhoneAuthTable::getList([
            'select' => ['USER_ID'],
            'filter' => ['PHONE_NUMBER' => $numberPhone],
        ])->fetch();

        if ($user !== false) {
            return (string)$user['USER_ID'];
        }

        return null;
    }
}
