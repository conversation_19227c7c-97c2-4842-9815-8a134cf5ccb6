<?php

declare(strict_types=1);

namespace D<PERSON>rievLab\Domains\Catalog;
use Bitrix\Iblock\ElementTable;
use CIBlockElement;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Domains\Store\DeliveryId;
use D<PERSON><PERSON><PERSON><PERSON><PERSON>\Repository\IblockRepository;
use D<PERSON><PERSON>vLab\Repository\ProductRepository;
use D<PERSON>rievLab\Domains\Catalog\PriceTypes\StorePriceType;


class Products
{
    public function updateProductsPrice(): void
    {
        $iblockId = IblockRepository::getIdByCode('aspro_max_catalog');
        $elementsIblock = ElementTable::getList(['select' => ['ID'], 'filter' => ['IBLOCK_ID' => $iblockId, 'ACTIVE' => 'Y']])->fetchAll();

        $priceServiceLenina = (new StorePriceType())->setStore(DeliveryId::LENINA->value);
        $priceServiceMikhalevski = (new StorePriceType())->setStore(DeliveryId::MIKHALEVSKI->value);

        $basePriceLenina = $priceServiceLenina->getBasePriceType()->getName();
        $discountPriceLenina = $priceServiceLenina->getDiscountPriceType()->getName();

        $basePriceMikhalevski = $priceServiceMikhalevski->getBasePriceType()->getName();
        $discountPriceMikhalevski = $priceServiceMikhalevski->getDiscountPriceType()->getName();

        foreach ($elementsIblock as $element) {

            $priceProducts = (new ProductRepository())->getPriceByProductId($element['ID']);

            $priceProducts = [
                $basePriceMikhalevski => [
                    $priceProducts[$discountPriceMikhalevski],
                    $priceProducts[$basePriceMikhalevski],
                ],
                $basePriceLenina => [
                    $priceProducts[$discountPriceLenina],
                    $priceProducts[$basePriceLenina],
                ],
            ];

            foreach ($priceProducts as $code => $price) {

                $price = !empty($price[0]) ? $price[0]['PRICE'] : $price[1]['PRICE'];

                CIBlockElement::SetPropertyValues(
                    $element['ID'],
                    $iblockId,
                    $price,
                    'PRICE_' . $code
                );
            }
        }
    }
}