<?php

declare(strict_types=1);

namespace DmitrievLab\Domains\Catalog\PriceTypes;

class PriceTypeDto
{
    public function __construct(
        private readonly BasePriceType $basePriceType,
        private readonly DiscountPriceType $discountPriceType
    )
    {
    }

    public function getBasePriceType(): BasePriceType
    {
        return $this->basePriceType;
    }

    public function getDiscountPriceType(): DiscountPriceType
    {
        return $this->discountPriceType;
    }
}