<?php

declare(strict_types=1);

namespace D<PERSON>riev<PERSON><PERSON>\Domains\Catalog\PriceTypes;

use Bitrix\Catalog\GroupTable;
use D<PERSON>rievLab\Domains\Store\Store;
use Exception;

class StorePriceType
{
//    const DOMEDY_MAIN_PRICE = 'cae12ff7-4550-11e7-a50f-2c56dc99cce9';
//    const DOMEDY_DISCOUNT_PRICE = 'cae12ff7-4550-11e7-a50f-2c56dc99cce8';

    const string DOM_EDY_5_RETAIL = 'cae12ff7-4550-11e7-a50f-2c56dc99cce4';
    const string DOM_EDY_5_STOCK = 'cae12ff7-4550-11e7-a50f-2c56dc99cce6';
    const string DOM_EDY_1_RETAIL = 'cae12ff7-4550-11e7-a50f-2c56dc99cce5';
    const string DOM_EDY_1_STOCK = 'cae12ff7-4550-11e7-a50f-2c56dc99cce7';

//    private array $pricesName = [
//        '3' => [self::DOMEDY_MAIN_PRICE, self::DOMEDY_DISCOUNT_PRICE],
//        '5' => [self::DOMEDY_MAIN_PRICE, self::DOMEDY_DISCOUNT_PRICE],
//    ];

    private array $pricesName = [
        '3' => [self::DOM_EDY_5_RETAIL, self::DOM_EDY_5_STOCK],
        '5' => [self::DOM_EDY_1_RETAIL, self::DOM_EDY_1_STOCK]
    ];

    private ?BasePriceType $basePriceType = null;
    private ?DiscountPriceType $discountPriceType = null;

    private int $storeId;

    public function setStore(int $storeId = null): static
    {
        if ($storeId !== null) {
            $this->storeId = $storeId;
        } else {
            $this->storeId = Store::getCurrentId();
        }

        $this->setUpPrice();

        return $this;
    }

    private function setUpPrice(): void
    {
        $query = GroupTable::getList([
            "select" => ["ID", "NAME", 'XML_ID'],
            "filter" => ["=XML_ID" => $this->pricesName[$this->storeId]]
        ]);

        foreach ($query->fetchAll() as $priceType) {
//            if (str_ends_with(strtolower($priceType['NAME']), 'Основной')) {
            if (str_ends_with(strtolower($priceType['NAME']), 'retail')) {
                $this->basePriceType = new BasePriceType(
                    id: (int)$priceType['ID'],
                    name: $priceType['NAME'],
                    xmlId: $priceType['XML_ID'],
                );
            } else {
                $this->discountPriceType = new DiscountPriceType(
                    id: (int)$priceType['ID'],
                    name: $priceType['NAME'],
                    xmlId: $priceType['XML_ID'],
                );
            }
        }
    }

    public function getBasePriceType(): BasePriceType
    {
        $this->checkPriceTypeIsInitial();

        return $this->basePriceType;
    }

    public function getDiscountPriceType(): DiscountPriceType
    {
        $this->checkPriceTypeIsInitial();

        return $this->discountPriceType;
    }

    public function getCurrentPrice(): PriceTypeDto
    {
        $this->checkPriceTypeIsInitial();

        return new PriceTypeDto(
            $this->basePriceType,
            $this->discountPriceType
        );
    }

    public function getCurrentPriceNames(): array
    {
        $this->checkPriceTypeIsInitial();

        return [
            $this->basePriceType->getName(),
            $this->discountPriceType->getName()
        ];
    }

    public function getCurrentPriceIds(): array
    {
        $this->checkPriceTypeIsInitial();

        return [
            $this->basePriceType->getId(),
            $this->discountPriceType->getId()
        ];
    }


    public function getCurrentPriceXmlIds(): array
    {
        $this->checkPriceTypeIsInitial();

        return [
            $this->basePriceType->getXmlId(),
            $this->discountPriceType->getXmlId()
        ];
    }

    /**
     * @return void
     * @throws Exception
     */
    private function checkPriceTypeIsInitial(): void
    {
        if (is_null($this->basePriceType) || is_null($this->discountPriceType)) {
            throw new Exception('Типы цен не инициализированы');
        }
    }
}