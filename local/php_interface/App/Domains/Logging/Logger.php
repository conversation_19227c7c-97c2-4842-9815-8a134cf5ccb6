<?php

declare(strict_types=1);

namespace DmitrievLab\Domains\Logging;

use Bitrix\Main\Application;
use Itspire\MonologLoki\Formatter\LokiFormatter;
use Itspire\MonologLoki\Handler\LokiHandler;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\WhatFailureGroupHandler;
use Monolog\Logger as MonologLogger;
use Monolog\Processor\IntrospectionProcessor;
use Monolog\Processor\UidProcessor;

class Logger
{
    private string $url;
    private MonologLogger $logger;

    public function __construct()
    {
        $this->url = 'http://62.113.104.85:3100';
    }

    public function setLogger(LoggerChannel $loggerChannel): MonologLogger
    {
        $channel = $loggerChannel->value;
        $this->logger = new MonologLogger($channel);
        $this->logger->pushProcessor(new UidProcessor());
        $this->logger->pushProcessor(new IntrospectionProcessor());

        $this->setLokiHandler();
        $this->setFileHandler($channel);

        return $this->logger;
    }

    protected function setLokiHandler(): void
    {

        $handler = new WhatFailureGroupHandler(
            [
                new LokiHandler(
                    [
                        'entrypoint' => $this->url
                    ]
                )
            ]
        );
        $handler->setFormatter(new LokiFormatter());
        $this->logger->pushHandler($handler);
    }

    protected function setFileHandler(string $fileName): void
    {
        $handler = new StreamHandler(Application::getDocumentRoot() . "/local/logs/monolog/$fileName.log", MonologLogger::DEBUG);
        $this->logger->pushHandler($handler);
    }

}