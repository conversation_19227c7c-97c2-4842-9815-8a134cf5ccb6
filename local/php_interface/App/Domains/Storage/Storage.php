<?php

declare(strict_types=1);

namespace D<PERSON>riev<PERSON>ab\Domains\Storage;

use Bitrix\Catalog\StoreProductTable;
use Bitrix\Iblock\ElementTable;
use Bitrix\Main\Loader;
use CModule;
use D<PERSON><PERSON>vLab\Repository\IblockRepository;

class Storage
{
    private const ID_STORAGE_LENINA = 3;
    private const ID_STORAGE_MIKHALEVSKY = 5;

    public static function updateTotalCountStorage(): void
    {
        Loader::includeModule('catalog');
        $iblockId = IblockRepository::getIdByCode('aspro_max_catalog');

        $elementsIblock = ElementTable::getList(['select' => ['ID'], 'filter' => ['IBLOCK_ID' => $iblockId, 'ACTIVE' => 'Y']])->fetchAll();

        foreach ($elementsIblock as $element) {
            $totalQuantity = 0;
            $stores = StoreProductTable::getList([
                    'select' => ['AMOUNT'],
                    'filter' => ['PRODUCT_ID' => $element['ID'], 'STORE_ID' => [
                        self::ID_STORAGE_LENINA,
                        self::ID_STORAGE_MIKHALEVSKY
                    ],
                        '>AMOUNT' => '0'
                    ],
                ]
            )->fetchAll();

            foreach ($stores as $store) {
                $totalQuantity += floatval($store['AMOUNT']);
            }

            $fieldQuantityUpdate = ['QUANTITY' => $totalQuantity];
            \Bitrix\Catalog\Model\Product::update($element['ID'], $fieldQuantityUpdate);

        }
    }
}