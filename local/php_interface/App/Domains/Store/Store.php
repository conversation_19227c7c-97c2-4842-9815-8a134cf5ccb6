<?php

declare(strict_types=1);

namespace D<PERSON>riev<PERSON>ab\Domains\Store;

use Bitrix\Catalog\StoreTable;
use Bitrix\Iblock\ElementTable;
use Bitrix\Main\Application;
use Bitrix\Main\Engine\CurrentUser;
use D<PERSON>rievLab\Domains\Delivery\DeliveryInfo;
use CUser;
use DmitrievLab\Repository\DeliveryInfoRepository;
use D<PERSON>rievLab\Repository\IblockRepository;

class Store
{
    public static function getCurrentId(): int
    {
        return DeliveryId::fromZone(self::getDeliveryZone());
    }

    public static function getCurrentName(): string
    {
        return DeliveryName::fromZone(self::getDeliveryZone());
    }

    public static function getDeliveryInfoByCurrentStore(?int $storeId = null): DeliveryInfo
    {
        $deliveryInfoElementId = StoreTable::getList(array(
            'filter' => array('ID' => $storeId ?? self::getCurrentId()),
            'select' => ['elementId' => 'UF_ZONA'],
        ))->fetch();

        $deliveryInfoElement = (new DeliveryInfoRepository())->get(
            ['ID' => $deliveryInfoElementId['elementId']],
            ['ID', 'NAME'],
            true
        );

        $deliveryProperties = array_map(function ($deliveryInfo) {
            return $deliveryInfo['VALUE'];
        }, $deliveryInfoElement[0]['PROPERTIES']);

        // TODO возможно доставать только числа, пока удаляем только пробелы, чтобы и 1 000 работало
        $pattern = '/\s+/';
        return new DeliveryInfo(
            (float)preg_replace($pattern, '', $deliveryProperties['MINIMAL']),
            (float)preg_replace($pattern, '', $deliveryProperties['PRICE']),
            (float)preg_replace($pattern, '', $deliveryProperties['SALES'])
        );
    }

    public static function productIsIsAvailable(int $productId, int $storeId): bool
    {

        $minWeightForOrder = \CIBlockElement::GetProperty(
            IblockRepository::getIdByCode('aspro_max_catalog'),
            $productId,
            ["sort" => "asc"],
            ["CODE" => "MINIMALNYY_VES_DLYA_ZAKAZA_KG"]
        )->Fetch();

        $weightForOrder['VALUE'] = (($minWeightForOrder['VALUE'] === 0)
            || $minWeightForOrder['VALUE'] === null) ? 1 : $minWeightForOrder['VALUE'];

        $storeProductQuery = \Bitrix\Catalog\StoreProductTable::getList(array(
            'filter' => [
                '=PRODUCT_ID' => $productId,
                '=STORE_ID' => $storeId
            ],
            'limit' => 1,
            'select' => ['AMOUNT'],
        ));

        $amount = $storeProductQuery->fetch();

        return (float)$amount['AMOUNT'] >= (float)$weightForOrder['VALUE'];
    }

    // пока сюда перенес, далее решим, оставить тут или вынести в другое место
    public static function checkProductIsActive(int $productId): bool
    {
        $query = ElementTable::getById($productId);
        $product = $query->fetch();

        return $product['ACTIVE'] === 'Y';
    }

    public static function getProductAmount(int $productId, int $storeId): float
    {
        $storeProductQuery = \Bitrix\Catalog\StoreProductTable::getList(array(
            'filter' => [
                '=PRODUCT_ID' => $productId,
                '=STORE_ID' => $storeId
            ],
            'limit' => 1,
            'select' => ['AMOUNT'],
        ));

        $amount = $storeProductQuery->fetch();

        return (float)$amount['AMOUNT'];
    }

    private static function getDeliveryZone(): int
    {
        $session = Application::getInstance()->getSession();
        $userId = (int)CurrentUser::get()->getID();
        $selectedDeliveryZone = 1;

        if ($userId) {
            $userData = self::getUserData($userId);
            if ($userData['UF_TYPE'] === '1') {
                $selectedDeliveryZone = $userData['WORK_WWW'] ?? $selectedDeliveryZone;
            }
        } elseif ($session->get('ADRES_DOSTAVKI_TYPE') === '1') {
            $selectedDeliveryZone = $session->get('ADRES_DOSTAVKI_ZONES') ?? $selectedDeliveryZone;
        }

        return (int)$selectedDeliveryZone;
    }

    private static function getUserData(int $userId): array
    {
        $filter = ['ID' => $userId];
        $rsUser = CUser::GetList(($by = "NAME"), ($order = "desc"), $filter, ['SELECT' => ['UF_TYPE']]);

        if ($arUser = $rsUser->Fetch()) {
            return $arUser;
        }

        return [];
    }
}
