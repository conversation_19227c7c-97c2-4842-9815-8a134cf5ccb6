<?php

declare(strict_types=1);

namespace DmitrievLab\Domains\Payments\Yookassa\Observer;

class PaymentPublisher implements PaymentPublisherInterface
{
    private \SplObjectStorage $observers;

    public function __construct()
    {
        $this->observers = new \SplObjectStorage;
    }

    public function subscribe(PaymentSubscriber $subscriber): void
    {
        $this->observers->attach($subscriber);
    }

    public function unsubscribe(PaymentSubscriber $subscriber): void
    {
        $this->observers->detach($subscriber);
    }

    public function notify(PaymentEvent $event): void
    {
        foreach ($this->observers as $observer) {
            $observer->update($event);
        }
    }
}