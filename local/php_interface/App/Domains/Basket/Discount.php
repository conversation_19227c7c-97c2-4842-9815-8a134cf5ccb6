<?php

namespace D<PERSON><PERSON>v<PERSON><PERSON>\Domains\Basket;

use Bitrix\Sale\Discount as BitrixDiscount;
use Bitrix\Sale\DiscountCouponsManager;
use Bitrix\Sale\Order;
use CSaleUser;
use D<PERSON>rievLab\Domains\Order\UseCase\CreateOrder\CreatedOrderEvent;
use DmitrievLab\Domains\Order\UseCase\CreateOrder\CreatedOrderSubscriber;
use D<PERSON>rievLab\Domains\Store\Store;

class Discount implements CreatedOrderSubscriber
{
    const COUPON_ERROR = "Промокод не может быть применен к заказу";

    protected BasketRepository $basketRepository;

    public function __construct(int $userId)
    {
        $this->basketRepository = new BasketRepository($userId);
    }
    // TODO залогировать купон, юзера и заказ???
    /**
     * @throws FailedAddCouponException
     */
    public function add(string $coupon)
    {
        global $USER;
        DiscountCouponsManager::init();

        if (!DiscountCouponsManager::add($coupon)) {
            $this->delete();
            throw new FailedAddCouponException(self::COUPON_ERROR);
        }
        \Bitrix\Sale\DiscountCouponsManager::saveApplied();
        if (!$this->basketRepository->getOrder()) {
            $userId = $USER->GetID() ?: CSaleUser::GetAnonymousUserID();
            $order = Order::create(\Bitrix\Main\Context::getCurrent()->getSite(), $userId);
            $order->appendBasket($this->basketRepository->orderableBasket(Store::getCurrentId()));

            $discounts = BitrixDiscount::buildFromOrder($order);
            $discounts->calculate();
            $showPrices = $discounts->getShowPrices();

            if (!empty($showPrices['BASKET'])) {
                foreach ($showPrices['BASKET'] as $basketCode => $data) {
                    $basketItem = $this->basketRepository->orderableBasket(Store::getCurrentId())->getItemByBasketCode($basketCode);
                    if ($basketItem instanceof \Bitrix\Sale\BasketItemBase) {
                        $basketItem->setFieldNoDemand('BASE_PRICE', $data['SHOW_BASE_PRICE']);
                        $basketItem->setFieldNoDemand('PRICE', $data['SHOW_PRICE']);
                        $basketItem->setFieldNoDemand('DISCOUNT_PRICE', $data['SHOW_DISCOUNT']);
                    }
                }
            }
        }

        $order = $this->basketRepository->getOrder();
        $discounts = BitrixDiscount::buildFromOrder($order);
        $discounts->calculate();
        $calcResults = $discounts->getApplyResult(true);

        if ($calcResults['COUPON_LIST'] === []) {
            $this->delete();
            throw new FailedAddCouponException(self::COUPON_ERROR);
        }

        $appliedDiscountList = [];
        foreach ($calcResults['DISCOUNT_LIST'] as $discountData) {
            if (isset($calcResults['FULL_DISCOUNT_LIST'][$discountData['REAL_DISCOUNT_ID']])) {
                $appliedDiscountList[$discountData['REAL_DISCOUNT_ID']] = $calcResults['FULL_DISCOUNT_LIST'][$discountData['REAL_DISCOUNT_ID']];
            }
        }

        return $this->isActive();
    }

    public function isActive(): bool|string
    {
        $order = $this->basketRepository->getOrder();
        $discounts = BitrixDiscount::buildFromOrder($order);
        $discounts->calculate();
        $discountResult = $discounts->getApplyResult(true);

        if ($discountResult['DISCOUNT_LIST'] === []) {
            $this->delete();
            return false;
        }

        return array_key_first($discountResult['COUPON_LIST']);
    }

    public function delete(): bool
    {
        if (DiscountCouponsManager::clear(true)) {
            $this->basketRepository->orderableBasket(Store::getCurrentId())->refresh();
            $this->basketRepository->save();

            return true;
        }

        return false;
    }

    public function clear(): void
    {
        DiscountCouponsManager::clear(true);
    }

    public function orderCreated(CreatedOrderEvent $event): void
    {
        $this->clear();
    }
}