<?php

declare(strict_types=1);

namespace DmitrievLab\Domains\Basket;

use Bitrix\Sale\BasketItem;
use CCatalogProduct;
use CFile;

class MobileAppApiBasketService extends BaseBasketService
{

    public function getProducts(): array
    {
        $this->basketRepository->basket()->refresh();
        return $this->getAll();
    }

    public function getAll()
    {
        $this->calculatePack();
        $basketItems = $this->basketRepository->getAll($this->storeId);
        $data = [];

        /* @var BasketItem $basketItem */
        foreach ($basketItems as $basketItem) {
            $productId = $basketItem->getProductId();
            $product_info = CCatalogProduct::GetByID($productId);

            $stepWeight = $this->getStepsWeight($productId);
            $amount = $basketItem->getQuantity();
            $maxAmount = $this->getAmountByProductId($productId);
            $quantity = $amount / $stepWeight;

            $product = $this->getProductInformation($productId);

            $data[] = [
                'ID' => $basketItem->getProductId(),
                'NAME' => $basketItem->getField('NAME'),
                'PREVIEW_PICTURE' => CFile::getPath($product['PREVIEW_PICTURE']),
                'DETAIL_PICTURE' => CFile::getPath($product['DETAIL_PICTURE']),
                'PRODUCT_PRICE' => ($product['DISCOUNT_PRICE'] < $product['PRICE'] && $product['DISCOUNT_PRICE'] !== null) ? $product['DISCOUNT_PRICE'] : $product['PRICE'],
                'MEASURE' => Measure::$measure[$product_info['MEASURE'] - 1],
                'quantity' => round($quantity, 1),
                'coefficient_quantity' => round($amount, 1),
                'max_quantity' => $maxAmount,
                'weight' => $product_info['WEIGHT'],
                'min_weight' => \CIBlockElement::GetProperty(
                    $this->iblockId, 
                    $productId, 
                    array("sort" => "asc"), 
                    array("CODE" => "MINIMALNYY_VES_DLYA_ZAKAZA_KG")
                )->Fetch()['VALUE'],
                'step_weight' => $stepWeight,
                'canBuy' => $basketItem->canBuy(),
            ];
        }

        return [
            'products' => $data,
            'count' => count($basketItems),
            'price' => round($this->basketRepository->basket()->getPrice(), 2),
            'weight' => round($this->basketRepository->basket()->getWeight(), 1)
        ];
    }

}