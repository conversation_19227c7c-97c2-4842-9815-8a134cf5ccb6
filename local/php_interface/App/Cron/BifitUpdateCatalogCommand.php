<?php

namespace DmitrievLab\Cron;

use DmitrievLab\Domains\Bifit\Bifit;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'bifit:updateCatalog')]
class BifitUpdateCatalogCommand extends Command
{
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        (new Bifit)
            ->setCatalogBifit()
            ->updateCatalogBifit()
            ->updateCatalogBifit_file();

        $output->writeln('Выполнена');
        return Command::SUCCESS;
    }
}