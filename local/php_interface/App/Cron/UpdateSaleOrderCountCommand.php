<?php

namespace App\Cron;

use D<PERSON><PERSON>vLab\Domains\Catalog\CountingProductSale;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Repository\IblockPropertyRepository;
use <PERSON><PERSON><PERSON>vLab\Repository\OrderRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'catalog:updateCount',
    description: 'Updates product sales count for the last day',
)]
class UpdateProductsSaleCountCommand extends Command
{
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ((new CountingProductSale())->calcCountProductSales()) {
            return Command::SUCCESS;
        } else {
            return Command::FAILURE;
        }
    }


}