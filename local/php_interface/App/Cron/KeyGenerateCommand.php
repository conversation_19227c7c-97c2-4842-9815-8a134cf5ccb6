<?php

namespace DmitrievLab\Cron;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'key:generate')]
class KeyGenerateCommand extends Command
{
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $cstrong = false;
        $randomBytes = openssl_random_pseudo_bytes(16, $cstrong);
        $hexString = bin2hex($randomBytes);

        $output->writeln($hexString);
        return Command::SUCCESS;
    }
}