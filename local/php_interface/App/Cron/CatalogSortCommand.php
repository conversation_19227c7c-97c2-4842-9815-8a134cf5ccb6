<?php

namespace D<PERSON>rievLab\Cron;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Splash\Splash_Catalog;

#[AsCommand(name: 'catalog:sort')]
class CatalogSortCommand extends Command
{
    protected function execute(InputInterface $input, OutputInterface $output): int
    {   
        \CModule::IncludeModule("iblock");
        \CModule::IncludeModule("catalog");

        $iterator = \CIBlockElement::GetList(
            array(),
            array('IBLOCK_ID' => 26),
            false,
            false,
            array('ID', 'NAME', 'IBLOCK_ID', 'PROPERTY_VES_TOVARA_G')
        );

        while ($it = $iterator->fetch()) {
            if ($it['PROPERTY_VES_TOVARA_G_VALUE'] > 0) {
                \CCatalogProduct::Update($it['ID'], array('WEIGHT' => $it['PROPERTY_VES_TOVARA_G_VALUE']));
            }
        }

        Splash_Catalog::setSort();
        BXClearCache(true);

        if (class_exists('\Bitrix\Main\Data\ManagedCache')) {
            (new \Bitrix\Main\Data\ManagedCache())->cleanAll();
        }

        if (class_exists('\CStackCacheManager')) {
            (new \CStackCacheManager())->CleanAll();
        }

        if (class_exists('\Bitrix\Main\Composite\Page')) {
            \Bitrix\Main\Composite\Page::getInstance()->deleteAll();
        }

        $output->writeln('Выполнена');
        return Command::SUCCESS;
    }
}