<?php

declare(strict_types=1);

namespace D<PERSON>riev<PERSON>ab\Repository;

use Bitrix\Iblock\PropertyTable;
use CIBlockElement;
use CIBlockProperty;
use CIBlockPropertyEnum;
use D<PERSON>rievLab\Exceptions\IblockPropertyNotFoundException;
use D<PERSON><PERSON>vLab\Exceptions\SalesCountErrorException;

class IblockPropertyRepository
{
    public static function getIdByCode(string $name): int
    {
        $property = self::getProperty(['ID'], ['CODE' => $name]);
        return (int)$property['ID'];
    }

    public static function getCodeById(int $id): string
    {
        $property = self::getProperty(['CODE'], ['ID' => $id]);
        return $property['CODE'];
    }

    public static function getPropertyValue(int $iblockId, int $elementId, string $propertyCode): mixed
    {
        $query = CIBlockElement::GetProperty(
            $iblockId,
            $elementId,
            ["sort" => "asc"],
            ["CODE" => $propertyCode]
        );

        $property = $query->Fetch();

        if ($property === null) {
            throw new IblockPropertyNotFoundException('Свойство не найдено');
        }

        return $property['VALUE'];
    }

    public static function getPropertyByProduct(int $iblockId, int $elementId, string $propertyCode): mixed
    {
        $query = CIBlockElement::GetProperty(
            $iblockId,
            $elementId,
            ["sort" => "asc"],
            ["CODE" => $propertyCode]
        );

        $property = $query->Fetch();

        if ($property === null) {
            throw new IblockPropertyNotFoundException('Свойство не найдено');
        }

        return $property;
    }

    public static function getPropertyAllValue(int $iblockId, int $elementId, string $propertyCode): mixed
    {
        $query = CIBlockElement::GetProperty(
            $iblockId,
            $elementId,
            ["sort" => "asc"],
            ["CODE" => $propertyCode]
        );

        $values = [];
        while($property = $query->Fetch()) {
            $values[] = $property['VALUE'];
        }

        return $values ?? [];
    }

    public static function getPropertyEnumValues(array $values): ?array
    {
        $propertyEnumQuery = CIBlockPropertyEnum::GetList(
            [],
            ['ID' => array_values($values)]
        );

        $values = [];
        while ($enum = $propertyEnumQuery->Fetch()) {
            $values[] = [
                'xmlId' => $enum['XML_ID'],
                'value' => $enum['VALUE'],
                'sort' => $enum['SORT'],
            ];
        }

        return $values ?? null;
    }

    private static function getProperty(array $select, array $filter): array
    {
        $query = PropertyTable::getList([
            'select' => $select,
            'filter' => $filter,
            'order' => ['ID' => 'DESC'],
        ]);

        $property = $query->fetch();

        if (empty($property)) {
            throw new IblockPropertyNotFoundException('Свойство не найдено');
        }

        return $property;
    }

    public function updateProperty(int $elementId, int $iblockId, $property): void
    {
        CIBlockElement::SetPropertyValuesEx($elementId, $iblockId, $property);
    }
}
