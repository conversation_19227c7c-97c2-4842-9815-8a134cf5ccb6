<?php

declare(strict_types=1);

namespace DmitrievLab\Repository;

use CIBlockElement;

abstract class Repository
{
    public int $IblockId;

    public function get(array $filter = [], array $select = [], $withProperties = false, array $order = ["SORT" => "ASC"])
    {
        // TODO потом дописать для постранички
        $query = CIBlockElement::GetList(
            $order,
            ['IBLOCK_ID' => $this->IblockId, ...$filter],
            false,
            ['nTopCount' => 10],
            ['IBLOCK_ID','ID', 'NAME', ...$select]);

        $elements = [];
        while ($element = $query->GetNextElement()) {
            $temp = $element->GetFields();
            if ($withProperties) {
                $temp['PROPERTIES'] = $element->GetProperties();
            }

            $elements[] = $temp;
        }

        return $elements;
    }

    public function getById(int $id): mixed
    {
        $query = CIBlockElement::GetByID($id);
        return $query->GetNext() ?? [];
    }


}
