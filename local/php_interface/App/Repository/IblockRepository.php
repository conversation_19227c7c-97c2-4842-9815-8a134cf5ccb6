<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>v<PERSON>ab\Repository;

use Bitrix\Iblock\IblockTable;
use D<PERSON>rievLab\Exceptions\IblockNotFoundException;

class IblockRepository
{
    public static function getIdByCode(string $code): int
    {
        $query = IblockTable::getList([
            'filter' => [
                'CODE' => $code,
            ]
        ]);

        $iblock = $query->fetch();

        if (empty($iblock)) {
            throw new IblockNotFoundException('Инфоблок не найден');
        }

        return (int) $iblock['ID'];
    }
}