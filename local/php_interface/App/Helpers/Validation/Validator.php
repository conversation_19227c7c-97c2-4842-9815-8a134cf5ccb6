<?php

declare(strict_types=1);

namespace DmitrievLab\Helpers\Validation;

use Symfony\Component\Validator\ValidatorBuilder;

class Validator
{
    /**
     * @param object $dto Объект для валидации
     * @throws ValidationFailedException
     */
    public static function validate(object $dto): void
    {
        $validator = (new ValidatorBuilder())
            ->enableAttributeMapping()
            ->getValidator();

        $validateErrors = $validator->validate($dto);

        $errors = [];
        foreach ($validateErrors->getIterator() as $error) {
            $errors[] = new ErrorDto(
                message: $error->getMessage(),
                propertyPath: $error->getPropertyPath(),
            );
        }

        if (!empty($errors)) {
            throw new ValidationFailedException($errors);
        }
    }
}