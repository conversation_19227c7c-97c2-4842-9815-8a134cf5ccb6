<? if (!defined("B_PROLOG_INCLUDED") || B_PROLOG_INCLUDED !== true) die();
if (is_array($arResult['INFO_DB'])) {
?>
	<? if (!empty($arParams['USER']['UF_QR'])) { ?>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">Для начисления и списания бонусов предъявите этот QR код на кассе:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum">
							<img src="<?= CFile::GetPath($arParams['USER']['UF_QR']) ?>" width="250">
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">Номер карты:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum"><?= $arResult['INFO_DB']['НомерКарты'] ?></div>
					</div>
				</div>
			</div>
		</div>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">Пин код карты:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum"><?= $arParams['USER']['PERSONAL_WWW'] ?></div>
					</div>
				</div>
			</div>
		</div>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">Количество бонусов:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum"><?= $arResult['INFO_DB']['НакопленоБонусов'] ?></div>
					</div>
				</div>
			</div>
		</div>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">За 10 рублей покупки начисляется:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum"><?= $arResult['INFO_DB']['ОбъемНачисляемыхБаллов'] ?><?= declen($arResult['INFO_DB']['ОбъемНачисляемыхБаллов'], array(' балл', ' балла', ' баллов')); ?></div>
					</div>
				</div>
			</div>
		</div>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">Сумма покупок:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum"><?= number_format($arResult['INFO_DB']['СуммаПокупок'], 2, '.', ' ') ?> руб.</div>
					</div>
				</div>
			</div>
		</div>
	<? } else { ?>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">QR код карты:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum" style="font-size: 13px;">
							Если код не отображается, просто обновите страницу
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">Номер карты:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum"><?= $arParams['USER']['PERSONAL_ICQ'] ?></div>
					</div>
				</div>
			</div>
		</div>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">Пин код карты:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum"><?= $arParams['USER']['PERSONAL_WWW'] ?></div>
					</div>
				</div>
			</div>
		</div>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">Количество бонусов:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum"><?= $arResult['INFO_DB']['НакопленоБонусов'] ?></div>
					</div>
				</div>
			</div>
		</div>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">За 10 рублей покупки начисляется:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum"><?= $arResult['INFO_DB']['ОбъемНачисляемыхБаллов'] ?><?= declen($arResult['INFO_DB']['ОбъемНачисляемыхБаллов'], array(' балл', ' балла', ' баллов')); ?></div>
					</div>
				</div>
			</div>
		</div>
		<div class="sale-personal-account-wallet-container">
			<div class="sale-personal-account-wallet-title">Сумма покупок:</div>
			<div class="sale-personal-account-wallet-list-container">
				<div class="sale-personal-account-wallet-list">
					<div class="sale-personal-account-wallet-list-item">
						<div class="sale-personal-account-wallet-sum"><?= number_format($arResult['INFO_DB']['СуммаПокупок'], 2, '.', ' ') ?> руб.</div>
					</div>
				</div>
			</div>
		</div>
	<? } ?>
<?
} else {
?>
	<div class="sale-personal-account-wallet-container">
		<div class="sale-personal-account-wallet-title">Карта не зарегистрирована!</div>
	</div>
<?
}
