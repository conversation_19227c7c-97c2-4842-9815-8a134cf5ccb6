<?php
require($_SERVER['DOCUMENT_ROOT'] . '/bitrix/modules/main/include/prolog_before.php');
// TODO логирование
use DmitrievLab\Helpers\Config;

if (!empty($_POST)) {
    $_p = $_POST;
    global $USER, $_SESSION, $APPLICATION;
    $arResult = [];
    $arResult['USER_ID'] = 0;
    if ($_p['TYPE'] == 'AUTH') {
        $syst_r = 1;
        $arResult['STEP'] = 1;
        $arResult['PHONE'] = $_p['USER_PHONE_NUMBER'];
        $phone = \Bitrix\Main\UserPhoneAuthTable::normalizePhoneNumber($_p['USER_PHONE_NUMBER']);
        $userss = \Bitrix\Main\UserPhoneAuthTable::getList($parameters = array('filter' => array('PHONE_NUMBER' => $phone)));
        if ($row = $userss->fetch()) {
            $rsUserss = CUser::GetByID($row['USER_ID']);
            $arUser = $rsUserss->Fetch();
            if ($arUser['ID'] > 0) {
                $syst_r = 0;
                $arResult['USER_ID'] = $arUser['ID'];
            }
        }
        $arResult['new_user'] = $syst_r;

        $pass = rand(1000, 9999);

        $getPhone = preg_replace('/[^0-9]/m', "", $phone);
        $get = array(
            "method" => "push_msg",
            "format" => "json",
            "key" => Config::get('SMS_TOKEN'),
            "phone" => $getPhone,
            "text" => $pass,

        );

        //$arResult['sms'] = $pass;
        //$_SESSION['USER_CHECK_WORD'] = $pass;
        $ch = curl_init(Config::get('SMS_URL') . http_build_query($get));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        $body = curl_exec($ch);
        curl_close($ch);
        $json = json_decode($body);
        if ($json) {
            if ($json->response->msg->err_code == "0") {
                $_SESSION['USER_CHECK_WORD'] = $pass;
                $exit['result'] = 'ok';
            }
        }

        $file = fopen("log_s.txt", "a+");
        $rews = $json->response->msg->err_code . " " . $json->response->msg->text;
        fwrite($file, date('d.m.Y H:i:s') . '-' . $phone . '-' . $rews . " \n");
        fclose($file);
    }
    if ($_p['TYPE'] == 'CODE_SMS') {
        $arResult['STEP'] = 2;
        if ($_p['SLOVO'] == $_SESSION['USER_CHECK_WORD']) {
            $syst_r = 1;
            $phone = \Bitrix\Main\UserPhoneAuthTable::normalizePhoneNumber($_p['USER_PHONE_NUMBER']);
            $userss = \Bitrix\Main\UserPhoneAuthTable::getList($parameters = array('filter' => array('PHONE_NUMBER' => $phone)));
            if ($row = $userss->fetch()) {
                $rsUserss = CUser::GetByID($row['USER_ID']);
                $arUser = $rsUserss->Fetch();
                if ($arUser['ID'] > 0) {
                    $syst_r = 0;
                    $arResult['USER_ID'] = $arUser['ID'];
                }
            }
            if ($syst_r == 1) {
                $user = new CUser;
                $pasw = 'RTg' . rand(1111, 9999) . 'fg_u' . rand(11, 99999);
                $arFields = array(
                    "LOGIN" => $_p['USER_PHONE_NUMBER'],
                    "LID" => "ru",
                    "ACTIVE" => "Y",
                    "GROUP_ID" => array(6),
                    "PASSWORD" => $pasw,
                    "CONFIRM_PASSWORD" => $pasw,
                    "PERSONAL_PHONE" => $_p['USER_PHONE_NUMBER'],
                    "PHONE_NUMBER" => $_p['USER_PHONE_NUMBER'],
                );
                $arResult['USER_ID'] = $user->Add($arFields);
                $USER->Authorize($arResult['USER_ID'], true);
                $arResult['ALERT'] = 'ok';
                $arResult['NEW_REG'] = true;
            } else {
                $user_dan = [];
                $rsUser = CUser::GetByID($arResult['USER_ID']);
                $user_dan = $rsUser->Fetch();
                if (!empty($user_dan['PERSONAL_ICQ'])) {
                    updateBonuses((int)$arResult['USER_ID']);
                }
                $USER->Authorize($arResult['USER_ID'], true);
                $arResult['ALERT'] = 'ok';
                $arResult['NEW_REG'] = false;
                unset($user_dan);
                unset($summa);
            }
        } else {
            $arResult['TEXT'] = 'Код введен неверно!';
            $arResult['ALERT'] = 'error';
        }
    }
}
echo json_encode($arResult);