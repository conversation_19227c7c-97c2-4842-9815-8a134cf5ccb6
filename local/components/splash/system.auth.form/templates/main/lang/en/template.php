<?php
$MESS["AUTH_A_INTERNAL"] = 'Built-in authorization';
$MESS["AUTH_A_LIVEID"] = 'LiveID';
$MESS["AUTH_A_OPENID"] = 'OpenID';
$MESS["AUTH_BU_LOGIN_EMAIL_TITLE"] = 'Login by login/email';
$MESS["AUTH_BU_PHONE_TITLE"] = 'Login by login';
$MESS["AUTH_ERROR"] = 'Wrong login or password';
$MESS["AUTH_FORGOT_PASSWORD_2"] = 'Forgot your password?';
$MESS["AUTH_LIVEID_LOGIN"] = 'Log In';
$MESS["AUTH_LOGIN_BUTTON"] = 'Log in';
$MESS["AUTH_LOGOUT_BUTTON"] = 'Go out';
$MESS["AUTH_NONSECURE_NOTE"] = 'The password will be sent in clear text. Please enable JavaScript in your browser to encrypt the password before sending.';
$MESS["AUTH_OPENID"] = 'OpenID';
$MESS["AUTH_PROFILE"] = 'My profile';
$MESS["AUTH_REGISTER"] = 'Registration';
$MESS["AUTH_REGISTER_NEW"] = 'Registration';
$MESS["AUTH_REMEMBER_ME"] = 'Remember me';
$MESS["AUTH_REMEMBER_SHORT"] = 'Remember me';
$MESS["AUTH_SECURE_NOTE"] = 'Before submitting the authorization form, the password will be encrypted in the browser. This will avoid passing the password in the clear.';
$MESS["CAPTCHA_PROMT"] = 'Enter code';
$MESS["REFRESH"] = 'Change picture';
$MESS["RELOAD"] = 'Refresh';
$MESS["SOCSERV_AS_USER_FORM"] = 'Social login';
$MESS["auth_code_sent"] = 'An SMS with an authorization code has been sent to your number.';
$MESS["auth_get_sms_code"] = 'Send code';
$MESS["auth_login"] = 'Login';
$MESS["auth_password"] = 'Password';
$MESS["auth_password2"] = 'Confirm that this is your account and enter your password';
$MESS["auth_password_continue"] = 'Proceed';
$MESS["auth_phone_number"] = 'Phone number';
$MESS["auth_phone_number_or_login"] = 'Enter your username or phone number';
$MESS["auth_sms_code"] = 'SMS authorization code';