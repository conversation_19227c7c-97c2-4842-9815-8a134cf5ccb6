body .bx_filter .bx_filter_button_box.btns .btn.bx_filter_search_reset{width: 100%;border-bottom-right-radius: 3px;border-right:0px;}
.bx_filter_parameters_box.set {overflow: hidden;}
.bx_filter_parameters_box.set:before {
    content: "";
    display: block !important;
    position: absolute;
    left: -6px !important;
    top: 17px;
    width: 11px;
    height: 11px;
    border-radius: 100%;
}
.smartfilter-additional input{display:none;}
.smartfilter-additional label{
    display: block;
    border: 1px solid #00000014;
    padding: 5px 10px;
    border-radius: 15px;
    cursor:pointer;	
}
.smartfilter-additional .bx_filter_parameters_box_container{
    display:flex;
    grid-gap:5px;
    margin:10px 0;
    flex-wrap: wrap;
    align-items: center;
    max-height: 40px;
    overflow:hidden;
}
.smartfilter-additional .l-active, .smartfilter-additional .selected{
    background: #539348;
    color:white;
}
.smartfilter-additional .additional-filter-opened-field{
    max-height: none !important;
}
.additional-expand_block{
    cursor: pointer;
    margin: 10px 0 10px;
    display: flex;
    align-items: center;
    grid-gap: 6px;
}
.additional-filter-opened-btn i{
    transform: rotate(180deg);
}