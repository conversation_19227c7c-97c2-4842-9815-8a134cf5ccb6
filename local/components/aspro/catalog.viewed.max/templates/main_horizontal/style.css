.viewed_product_block {
  border-bottom: 1px solid #ececec;
  border-color: var(--stroke_black);
}
.wrapper1:not(.front_page) + footer .viewed_product_block,
.wrapper1:not(.front_page) + .js_seo_title + footer .viewed_product_block {
  border-top: 1px solid #ececec;
  border-color: var(--stroke_black);
}
.viewed_product_block.no_fill {
  background: #fafafa;
  background: var(--darkerblack_bg_black);
}
.viewed_product_block.fill {
  border: none;
}
.viewed_product_block.fill > .wrapper_inner {
  border-bottom: 1px solid #ececec;
  border-color: var(--stroke_black);
}
.viewed_product_block > .wrapper_inner {
  background: none;
}
.viewed_product_block .viewed-wrapper {
  padding: 3.133rem 0px 3.133rem;
}
.viewed_product_block .viewed-wrapper h3 {
  margin-bottom: 1.533rem;
}
.viewed_product_block .viewed-wrapper .cost .price {
  margin: 3px 0px 0px;
  font-weight: bold;
}
.viewed_product_block .viewed-wrapper .block-item {
  background: none;
}
.viewed_product_block .viewed-wrapper .block-item .item {
  background: #eeeff1;
  background: var(--card_bg_black);
}
.viewed_product_block .viewed-wrapper .block-item .item.has-item {
  background: #fff;
  background: var(--card_bg_black);
}
.viewed_product_block .viewed-wrapper .block-item__title {
  max-height: 53px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  word-break: break-word;
}
.viewed_product_block .viewed-wrapper .block-item__title > a {
  max-height: 100%;
}
