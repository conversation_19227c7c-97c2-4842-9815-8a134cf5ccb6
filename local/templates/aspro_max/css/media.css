body {
  min-width: 300px;
}

.rating-mobile-prices,
.rating-mobile-star {
  display: none;
}

ul.tabs li.stretch {
  display: none;
}
@media (max-width: 1124px) {
  .top-block .social-icons li a {
    width: 30px;
  }
}
@media (max-width: 767px) {
  a.scroll-to-top {
    display: none;
  }
}
@media screen and (min-width: 992px) {
  .bx_filter.bx_filter_vertical {
    display: block !important;
  }
  .js_filter.filter_horizontal .bx_filter.bx_filter_vertical {
    display: none !important;
  }
}
@media all and (max-width: 960px) {
  .info_item .top_info .brand + div {
    float: none;
  }
  .info_item .top_info .article {
    text-align: left;
  }
}
@media (min-width: 1200px) {
  /*catalog block*/
  .catalog_block.items .item_block.col-5,
  .col-lg-20 {
    width: 20%;
  }
  .col-lg-12-5 {
    width: 12.5%;
  }
  .col-lg-40 {
    width: 40%;
  }
  .col-lg-60 {
    width: 60%;
  }

  .adv_list.top .item .img {
    padding-left: 20px;
  }
  /*personal*/
  .personal_wrapper .row .col-lg-4 {
    width: 33.33333333%;
  }

  .tizers_block .item {
    padding: 0px 0px 0px 20px;
  }
}
@media only screen and (max-width: 1174px) {
  #bx-soa-order .bx-soa-pp-desc-container .bx-soa-pp-company-graf-container {
    float: none;
  }
  #bx-soa-order .bx-soa-pp-desc-container .bx-soa-pp-company .bx-soa-pp-company-desc {
    margin-top: 15px;
    padding-left: 0;
  }

  div.title-search-result.title-search-input_fixedtf .bx_searche .bx_img_element + .bx_item_element,
  div.title-search-result.title-search-input_fixed .bx_searche .bx_img_element + .bx_item_element {
    padding-left: 20px;
    margin-left: 32px;
  }
  div.title-search-result.title-search-input_fixedtf .bx_searche .bx_img_element,
  div.title-search-result.title-search-input_fixed .bx_searche .bx_img_element {
    width: 30px;
    height: 50px;
    line-height: 50px;
  }

  div.title-search-result.title-search-input_fixed .bx_searche .bx_item_element {
    white-space: normal;
  }
  div.title-search-result.title-search-input_fixed .bx_searche .bx_item_element > span {
    line-height: 20px;
    margin-top: 5px;
    display: block;
  }
  div.title-search-result.title-search-input_fixed .bx_searche .bx_item_element .title-search-price {
    margin-top: 8px;
  }
  div.title-search-result.title-search-input_fixed .bx_searche .bx_item_element .title-search-price > .price {
    margin-bottom: 5px;
  }
}

@media only screen and (max-width: 1700px) {
  .fix-logo .content-block .float_wrapper {
    display: none;
  }
  body .fix-logo .content-block .subcontent {
    padding: 0px;
  }
}

@media only screen and (max-width: 1500px) {
  .pull-right.region-phones {
    padding-left: 40px;
  }
  body .wrapper1 .header-v20 .smalls.logo_and_menu-row .paddings .wides .logo-block {
    padding-right: 40px;
  }

  .header-v25 .logo_and_menu-row .wrap_icon .title,
  .header-v25 .logo_and_menu-row .wrap_icon .name {
    display: none;
  }
  .header-v25 .logo_and_menu-row .subcontent .subtop > .row > div {
    width: auto;
  }
  .smalls.big_header .fix-logo .logo_and_menu-row .wides .content-block .subcontent .address {
    padding: 0px 0px 0px 0px;
    right: 40px;
  }
  .smalls.big_header .fix-logo .logo_and_menu-row .wrap_icon.person {
    padding-left: 20px;
  }
}

@media only screen and (min-width: 1500px) {
  .header-wrapper.header-v20 .mega-menu table td:hover > .wrap > .dropdown-menu {
    left: auto !important;
    right: auto !important;
  }

  .header-v22.top-block.top-block-v1 .wrapp_block .top-block-item.soc li a {
    width: 46px;
  }
}

@media only screen and (max-width: 1500px) and (min-width: 1200px) {
  .pull-right.region-phones {
    padding-top: 14px;
  }
  .header-wrapper.header-v20 .mega-menu table .dropdown-submenu .dropdown-menu {
    left: 100% !important;
  }
  .header-wrapper.header-v20 .region-phones > .pull-left {
    float: none !important;
    display: block;
  }
  .header-wrapper.header-v20 .region-phones > .pull-left > div {
    height: auto;
  }
  .header-wrapper.header-v20 .region-phones > .pull-left > div .phone-block {
    margin-top: 2px;
  }
}
@media (max-width: 1550px) {
  body .top_slider_wrapp .flexslider .flex-direction-nav .flex-nav-prev {
    left: 0;
  }
  body .top_slider_wrapp .flexslider .flex-direction-nav .flex-nav-next {
    right: 21px;
  }
}

@media only screen and (max-width: 1400px) {
  .bg_image_site {
    display: none;
  }
  .basket_normal .header-v16 .logo-block {
    width: 16.666666666666664%;
  }

  .basket_normal.regions_Y .logo_and_menu-row .top-description .region_wrapper {
    margin-left: 0px;
  }
  .header-wrapper .wrap_icon .title,
  .header-wrapper .auth_wr_inner  .name,
  .top-block.header-wrapper  .auth_wr_inner  .name,
  .header-wrapper .sites__current
   {
    display: none;
  }
  .sites__dropdown {
    left:-40px;
  }
  .sites__dropdown--typeLang {
    left: -15px;
  } 
  
  /* .wrapper1.basket_normal .logo_and_menu-row .wrap_icon.person {
    padding-right: 3px; padding-left:30px;
  } */
  /* .wrapper1.basket_normal .logo_and_menu-row .basket-link {
    padding-left: 22px;
  } */
 /* .top-block .auth_wr_inner .svg.downs {
    top: 6px;
  } */
}

@media only screen and (max-width: 1290px) {
  header .menu.top > li.full > .dropdown > li,
  header .menu.top.catalogfirst li.full > .dropdown > li {
    width: 50%;
  }
  .menu_top_block li.full > .dropdown > li:nth-child(3n + 1) {
    clear: both;
  }
  .logo_and_menu-row .float_wrapper {
    display: none;
  }

  body .wrapper1 .header_wrap .top-block .menus {
    margin: 0px;
  }
  body .wrapper1 .top-block + .header-wrapper .smalls.logo_and_menu-row .paddings .wides .menu-row {
    padding-right: 222px;
  }
  body .wrapper1 .header_wrap .top-block .logo_and_menu-row .wrap_icon .title {
    display: none;
  }
}

@media all and (min-width: 1200px) {
  footer .info.contacts_block_footer {
    padding-left: 12%;
  }

  .wrapper1.sticky_menu.sm .header-v28 .content-block .subcontent .basket-link.compare {
    padding-left: 87px;
  }
}
@media all and (max-width: 1300px) {
  .logo_and_menu-row .block2.phone-block {
    line-height: 18px;
  }
  .logo_and_menu-row .block2.phone-block .phone {
    display: block;
  }
  .logo_and_menu-row .block2.phone-block .callback-block {
    margin-left: 0px;
  }
  .logo_and_menu-row .block2.phone-block .phone.with_dropdown > .dropdown {
    top: -24px;
  }
}
@media all and (max-width: 1200px) {
  .rows_block .block_list .col-4 {
    width: 33.33%;
  }
  footer .info .phone .dropdown {
    text-align: right;
  }
  footer .info .phone.blocks .dropdown {
    right: -8px;
    left: inherit;
  }
  footer .info .phone.blocks .dropdown.with_icons {
    right: -8px;
    left: inherit;
  }
  .type_clothes .info_item .middle_info .buy_block .counter_wrapp {
    white-space: normal;
  }

  body #headerfixed .wproduct {
    width: 100%;
  }
  body #headerfixed .wproduct .logo-block {
    display: none;
  }
  #headerfixed .logo-row.wproduct > div.product_block {
    padding-left: 0px;
  }
  .basket_normal .logo_and_menu-row .basket-link {
    padding-left: 10px;
    padding-right: 10px;
  }
  #headerfixed .basket-link {
    padding-left: 10px;
    padding-right: 10px;
  }
  .sites_bottom_menu .sites__dropdown {
    left:-40px;
  }
  .sites_bottom_menu .sites__dropdown--typeLang {
    left:-15px;
  }
}
@media all and (max-width: 1199px) {
  .catalog_item.big .icons-basket-wrapper .btn {
    padding: 0px !important;
  }
  .controls-linecount {
    display: none;
  }

  /* .regions_Y.basket_normal .logo_and_menu-row .basket-link,
  body .smalls.big_header.sticky_menu .fix-logo .logo_and_menu-row .wides .content-block .subcontent .basket-link {
    padding-left: 17px;
  } */
  body .sticky_menu.basket_normal .fix-logo .content-block .search_wraps {
    padding-right: 410px;
  }
  /* body .regions_Y.wrapper1 .logo_and_menu-row .wrap_icon.person {
    padding-left: 20px;
    padding-right: 3px;
  } */

  .bx_filter .bx_filter_button_box .bx_filter_parameters_box_container > .btn {
    padding-left: 10px;
    padding-right: 10px;
  }
  /* body .wrapper1.sticky_menu.sm .header-v28 .logo_and_menu-row .search_wrap {
    padding-left: 50px;
    padding-right: 50px;
  } */

  .menu-row.middle-block .mega-menu table td.catalog {
    width: 210px;
  }
  .menu-row.middle-block:not(.bglight) .mega-menu table td.catalog > .wrap {
    width: 209px;
  }

  .header-wrapper.header-v20 .menu-row .menu-only {
    padding: 0px !important;
  }
  /* .header-wrapper.header-v20 .menu-row .menu-only .mega-menu:before {
    content: "";
    display: block;
    position: absolute;
    background: #f2f2f2;
    left: 15px;
    right: 15px;
    top: 89px;
    height: 1px;
  } */

  .header-wrapper.header-v20 .mega-menu table td .wrap > a {
    height: 60px;
    padding: 21px 12px 20px;
  }

  .header-wrapper.header-v20 .smalls.logo_and_menu-row .wides .mega-menu td .wrap > a .line-wrapper .line {
    bottom: -14px;
  }
  .big_header.basket_normal .header-wrapper.header-v26 .logo-row .subbottom > .menu,
  .big_header.basket_normal .header-wrapper.header-v27 .logo-row .subbottom > .menu {
    padding-right: 280px;
  }

  .auth_wr_inner .dropdown-menu {
    right: 0px;
    left: auto;
  }
  body .wrapper1.basket_normal .logo_and_menu-row .wrap_icon .auth_wr_inner .dropdown-menu {
    left: auto;
  }

  .smalls.big_header .fix-logo .logo_and_menu-row .wides .content-block .subcontent .address {
    display: none;
  }

  .catalog_item.big .icons-basket-wrapper .btn .svg {
    display: block;
  }
  .catalog_item.big .icons-basket-wrapper .btn .svg + span {
    display: none;
  }
  .catalog_item.big .icons-basket-wrapper .btn {
    padding: 0px;
  }

  /*list catalog*/
  .display_list .list_item {
    padding-left: 18px;
    padding-right: 18px;
  }
  .display_list .list_item .image_wrapper_block,
  .list_item .image_wrapper_block > a {
    width: 150px;
    height: 150px;
    line-height: 150px;
  }
  .display_list .list_item .image_block .fast_view_block {
    left: 0px;
    display: block;
  }
  .display_list .list_item .image_block .fast_view_block .svg {
    display: none;
  }
  .display_list .list_item .information_wrapp {
    flex-basis: 155px;
  }
  .display_list .list_item .information_wrapp > div {
    width: 155px;
  }
  .display_list .list_item .counter_wrapp.list > div {
    width: 100%;
  }
  .display_list .list_item .view_sale_block .values .item {
    font-size: 12px;
  }
  .display_list .list_item .view_sale_block.v2 .quantity_block {
    padding-left: 5px;
  }
  body .view_sale_block.v2 > div:first-of-type {
    display: none;
  }
  body .display_list .description_wrapp {
    padding-left: 20px;
    padding-right: 20px;
  }
  body .display_list .description_wrapp .like_icons.list {
    left: 20px;
    right: 20px;
  }

  body .display_list .js_price_wrapper .js-info-block {
    left: -90px;
    z-index: 55;
  }

  /*table catalog*/
  body .table-view .item-buttons .counter_wrapp.list > div {
    width: 100%;
    margin-bottom: 8px;
  }
  body .table-view .item-buttons .counter_wrapp.list > div:last-of-type {
    margin-bottom: 0px;
  }

  .table-view .table-view__item-wrapper .item-icons {
    width: 55px;
  }
  .table-view .item-icons .like_icons > div {
    margin-bottom: 4px;
  }
  .table-view .item-icons .like_icons > div:last-of-type {
    margin-bottom: 0px;
  }
}

@media all and (max-width: 1180px) {
  .block_wr .top_block a {
    display: inline-block;
  }
  .footer_top .wrap_md .phones .phone_wrap a {
    font-size: 17px;
  }
  .catalog_detail .element_detail_text .sh {
    padding-right: 3%;
  }
  .wrapper_inner .stores .stores_list {
    padding: 0px 20px 0px 0px;
  }
  .wrapper_inner .stores .all_map {
    margin: 00px 0px 0px 0px;
  }
  .has_menu #header .middle-h-row .center_block {
    white-space: nowrap;
  }
}

@media all and (max-width: 1168px) {
  .footer_top .wrap_md .phones {
    padding-left: 2%;
  }
  .footer_bottom .social_block .social {
    padding-left: 13%;
  }
}
@media all and (max-width: 1150px) and (min-width: 992px) {
  #bx-soa-order #bx-soa-auth .filter .forgot {
    float: none !important;
    margin: 10px 0 -10px;
    display: block;
  }
}
@media all and (max-width: 1120px) {
  .tizers_block .item .title {
    font-size: 11px;
  }
  .tizers_block .item .title a {
    font-size: 11px;
  }
  .bx-firefox .top-h-row .phones {
    padding-top: 7px;
  }
  .top-h-row ul.menu {
    text-align: left;
  }
  .top-h-row .phones {
    white-space: nowrap;
  }
  .catalog_detail .set_block .popup_open {
    display: none;
  }
  .wrapper_inner .stores .stores_list {
    width: 71%;
  }
}
@media (max-width: 1100px) {
  .specials.tab_slider_wrapp ul.tabs_content li.tab .catalog_block .col-4,
  .rows_block .col-4 {
    width: 33%;
  }
  .footer_inner .rows_block .col-4 {
    width: 25%;
  }

  .top-block.top-block-v1 .wrapp_block > .row > div.col-md-5 {
    width: 20%;
  }
  .top-block.top-block-v1 .wrapp_block > .row > div.col-md-7 {
    width: 80%;
  }
  .subtop .phone-block {
    flex-direction: column;
    align-items: flex-start;
  }
}
@media all and (max-width: 1050px) {
  .wrapper_inner .info_item .middle_info .buy_block .counter_wrapp {
    white-space: normal;
  }
  .top-h-row .phone_wrap .phone_text a {
    display: none;
  }
  .top-h-row .phone_wrap .phone_text a:first-child {
    display: inline-block;
  }
  .info_item .top_info .article {
    text-align: center;
  }
}
@media all and (max-width: 1020px) {
  .no_goods .button {
    float: none;
    margin-top: 37px;
  }
  #order_form_div .info_block .wrap_md > div {
    width: 100%;
  }
  #order_form_div .info_block .l_block:after,
  #order_form_div .info_block .r_block:before {
    display: none;
  }
  #order_form_div .info_block .wrap_md .l_block {
    border-bottom: 1px solid #eee;
  }
  .bx_ordercart .module-cart td {
    white-space: normal;
  }

  .bx_ordercart .bx_ordercart_order_table_container tbody td.control {
    width: 90px;
  }
  .bx_ordercart .bx_ordercart_order_table_container tbody td.itemphoto {
    width: 129px;
  }
  .bx_ordercart .bx_ordercart_order_table_container tbody td.itemphoto > div {
    width: 90px;
    height: 90px;
    line-height: 90px;
  }
  .bx_ordercart .bx_ordercart_order_table_container .bx_ordercart_photo {
    height: 90px;
    background-size: contain;
  }
  .bx_ordercart .bx_ordercart_order_table_container table thead td.item {
    padding-left: 145px;
  }
}
@media all and (max-width: 1299px) and (min-width: 1200px) {
  .wrapper1.long_banner .header_wrap .top-block .wrapp_block .col-lg-5 {
    width: 46%;
  }
  .wrapper1.long_banner .header_wrap .top-block .wrapp_block .col-lg-7 {
    width: 54%;
  }
  .top-block.top-block-v1 .wrapp_block .top-block-item.soc {
    max-width: 39%;
  }
}
@media all and (max-width: 1499px) and (min-width: 992px) {
  body .wrapper1.basket_normal .header-v25 .logo_and_menu-row .wrap_icon .auth_wr_inner .dropdown-menu {
    left: auto;
  }
}
@media all and (max-width: 1199px) and (min-width: 992px) {
  .top-block.top-block-v1.header-v16 .region_wrapper {
    padding-right: 0px;
  }

  .wrapper1 .header-v22 .wrapp_block .col-md-6 {
    width: 63%;
  }
  .wrapper1 .header-v22 .wrapp_block .col-md-6 + .col-md-6 {
    width: 37%;
  }
  .wrapper1.basket_normal .header-v22 .smalls.logo_and_menu-row .paddings .wides .menu-row {
    padding-right: 270px;
  }

  .menu-row .mega-menu table td.wide_menu > .wrap > .dropdown-menu > li {
    width: 33.33%;
  }
  .menu-row .mega-menu table td.wide_menu .dropdown-menu > li:nth-child(3n + 1) {
    clear: left;
  }
  .menu-row .mega-menu table td.wide_menu .dropdown-menu > li:nth-child(4n + 1) {
    clear: none;
  }

  #headerfixed .logo-row .logo-block {
    max-width: 160px;
    min-width: 160px;
  }

  /*table catalog*/
  body .table-view .sale_block .value {
    display: none;
  }
}
@media all and (max-width: 1100px) and (min-width: 992px) {
  .banners-small.blog .items > .row > div:nth-child(4) {
    display: none;
  }
  .banners-small.blog .items > .row > .col-m-20 {
    width: 30%;
  }
  .top_big_banners.short_block .slide .banner_buttons.with_actions {
    margin-top: 8px;
  }

  .logo-row:not(.row) .col-md-2.hidden-sm.hidden-xs {
    display: none !important;
  }
  .catalog_block .counter_wrapp {
    white-space: normal;
  }
  .catalog_block .counter_wrapp > div:last-of-type {
    margin-bottom: 0px;
  }

  body #bx-soa-order .bx-soa {
    width: 70%;
  }
  body #bx-soa-order .bx-soa-sidebar {
    width: 30%;
  }
}
@media all and (max-width: 1020px) and (min-width: 950px) {
  .flexslider .banner_title,
  .flexslider .banner_text,
  .flexslider .banner_buttons {
    margin-right: 40px;
  }
  body .top_big_banners.half_block .flexslider .banner_title .head-title:after {
    right: -120px;
  }
}
@media all and (max-width: 1000px) {
  #header .middle-h-row .center_block,
  .has_menu #header .middle-h-row .center_block {
    padding-right: 30px;
    padding-left: 30px;
  }
  body #footer .bottom_left_icons,
  body #footer ul.bottom_main_menu,
  body #footer ul.bottom_submenu {
    clear: both;
    display: block;
    width: 100%;
  }
  .top-h-row ul.menu {
    width: 40%;
  }
  .top-h-row .phones {
    text-align: right;
  }
  body #header .basket_fly {
    width: 700px;
    right: -700px;
  }
  #header .basket_fly .basket_title {
    margin-right: 15px;
  }
  .basket_sort ul.tabs li {
    margin-right: 0px;
  }
  .info_item .top_info .article + .brand {
    padding-left: 0px;
  }
  .catalog_detail .element_detail_text .sh {
    padding-right: 0;
  }
}
@media all and (max-width: 992px) {
  .menu_top_block.catalog_block .dropdown > li.full > .dropdown > li,
  header .menu.top.catalogfirst li.full > .dropdown > li {
    width: 50%;
  }
  .menu_top_block li.full > .dropdown > li:nth-child(2n + 1) {
    clear: both;
  }
  .menu_top_block li.full > .dropdown > li:nth-child(3n + 1) {
    clear: none;
  }

  .footer_bottom .social_block,
  .footer_top .wrap_md .phones {
    width: 100%;
  }
  .footer_top .wrap_md .phones {
    padding-left: 0px;
    padding-top: 25px;
  }
  #footer .wrap_md .empty_block {
    width: 36%;
  }
  .footer_top .wrap_md .phones .phone_block,
  .footer_bottom .social_block .social_wrapper {
    padding: 0px 0px 0px 19px;
    width: 64%;
  }
  .footer_top .wrap_md .phones .order {
    text-align: left;
  }
  .footer_top .sblock,
  .footer_bottom .menu_block {
    width: 100%;
    padding: 0px;
  }
  .footer_bottom .social_block {
    padding-top: 23px;
  }
  .footer_bottom .social_block .social_wrapper .social {
    padding-left: 0px;
  }
  #footer .footer_bottom {
    padding-top: 19px;
  }
  .top-h-row ul.menu > li a {
    padding: 0px 5px 0px;
  }
  .bx-firefox .menu > li > a span {
    padding-top: 8px;
  }
  #header .middle-h-row .main-nav ul.menu > li > a {
    padding: 0px 9px;
  }
  .main-nav ul.menu > li > a span {
    font-size: 12px;
  }
  .wrapper_inner #content .catalog_block .catalog_item_wrapp:nth-child(3n) {
    width: 210px;
  }
  .wrapper_inner .stores .stores_list {
    width: 68%;
  }
  #header .middle-h-row td.text_wrapp {
    display: none;
  }

  /* ORDER */
  #bx-soa-total {
    width: 100%;
    display: none;
  }
  #bx-soa-total-mobile {
    display: block !important;
  }
  #bx-soa-order .bx-soa-cart-total-button-container {
    display: none !important;
  }
  #bx-soa-order > .bx-soa {
    width: 100%;
  }
  #bx-soa-order #bx-soa-total .bx-soa-cart-total.bx-soa-cart-total-fixed {
    position: relative !important;
    opacity: 1 !important;
  }
  #bx-soa-order #bx-soa-total .bx-soa-cart-total-ghost {
    padding: 0 !important;
  }
  #bx-soa-order .bx-soa-pp-desc-container .bx-soa-pp-company-graf-container {
    float: left;
  }
  #bx-soa-order .bx-soa-pp-desc-container .bx-soa-pp-company .bx-soa-pp-company-desc {
    margin-top: 0;
    padding-left: 140px;
  }
  #bx-soa-order .bx-soa-section .bx-soa-section-content {
    padding-left: 32px;
  }
  #bx-soa-order .bx-soa-pp-company-selected {
    float: left;
    width: 75%;
  }
  #bx-soa-order .bx-soa-pp-price {
    float: right;
    width: 25%;
  }
  #bx-soa-order .bx-soa-item-tr {
    padding: 20px 20px 20px 110px;
    border-top: 1px solid #f3f3f3;
    border-color: var(--stroke_black);
  }
  #bx-soa-order .bx-soa-item-tr.bx-soa-item-tr-first {
    border-top: none;
  }
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-block {
    padding-left: 0;
    overflow: visible;
  }
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-tr .bx-soa-item-td {
    border: none;
    padding: 0 !important;
    min-width: 0 !important;
  }
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-img-block {
    margin-left: -90px;
    margin-top: 0;
  }
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-content {
    padding-left: 0;
  }
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-tr .bx-soa-item-td.bx-soa-item-properties {
    margin-top: 10px;
  }
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-tr .bx-soa-item-td.bx-soa-item-properties > div {
    padding: 0;
    text-align: left;
  }
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-tr .bx-soa-item-td:first-child {
    padding-left: 0 !important;
  }
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-tr .bx-soa-item-td:last-child {
    padding-right: 0 !important;
  }
  #bx-soa-order #bx-soa-basket .bx-soa-item-td-title {
    padding-bottom: 0;
  }
  #bx-soa-order .bx-soa-coupon {
    margin-left: -29px;
  }
  #bx-soa-order .bx-soa-more {
    margin-left: -29px;
  }
  #bx-soa-order .alert-danger {
    margin-left: -29px;
  }
  #bx-soa-order .bx-soa-cart-total .bx-soa-cart-total-line-total .bx-soa-cart-d {
    white-space: normal;
  }
  .bx-soa-item-tr.bx-soa-item-info-container,
  .bx-soa-item-tr .bx-soa-item-td {
    height: auto;
  }

  /*personal*/
  .sale-acountpay-block .sale-acountpay-pp div .sale-acountpay-pp-company {
    width: 100%;
  }
  .personal_wrapper .orders_wrapper .sale-order-list-status-alert,
  .personal_wrapper .orders_wrapper .sale-order-list-status-success,
  .personal_wrapper .orders_wrapper .sale-order-list-shipment-status-block,
  .sale-order-detail-payment-options-methods-info-title-status-alert,
  .sale-order-detail-payment-options-methods-info-title-status-success,
  .sale-order-payment-change-status-alert,
  .sale-order-payment-change-status-success {
    margin-left: 1px;
  }
  .personal_wrapper .orders_wrapper .sale-order-detail-about-order-inner-container-repeat > a {
    display: block;
    float: none;
  }
  .personal_wrapper
    .orders_wrapper
    .sale-order-detail-payment-options-methods-information-block
    .sale-order-detail-payment-options-methods-image-container.opened {
    width: 100%;
  }
  .personal_wrapper
    .orders_wrapper
    .sale-order-detail-payment-options-methods-information-block
    .sale-order-detail-payment-options-methods-info.opened {
    margin: 0px;
    width: 100%;
    padding-left: 15px !important;
    padding-top: 10px;
  }
  .personal_wrapper .row div.sale-order-detail-payment-inner-row-template {
    margin-left: 15px;
  }
  .sale-order-detail-order-item-td.sale-order-detail-order-item-properties > div {
    float: left;
  }
  .personal_wrapper
    .orders_wrapper
    .sale-order-detail-total-payment-container
    .sale-order-detail-total-payment-list-right
    > li {
    text-align: left;
  }
}
@media (min-width: 992px) {
  .col-m-20 {
    width: 20%;
  }
  .col-m-40 {
    width: 40%;
  }
  .col-m-60 {
    width: 60%;
  }
  .col-m-80 {
    width: 80%;
  }
  .col-m-21 {
    width: 21%;
  }
  .col-m-58 {
    width: 58%;
  }

  .col-m-pull-60 {
    right: 60%;
  }
  .col-m-pull-80 {
    right: 80%;
  }
  .col-m-pull-75 {
    right: 75%;
  }
  .col-m-pull-50 {
    right: 50%;
  }
  .col-m-pull-25 {
    right: 25%;
  }
  .col-m-pull-58 {
    right: 58%;
  }

  .col-m-push-25 {
    left: 25%;
  }
  .col-m-push-20 {
    left: 20%;
  }
  .col-m-push-21 {
    left: 21%;
  }

  .top_mobile_region {
    display: none;
  }

  .mobile .with_fast_view .fast_view_block {
    display: block;
    opacity: 1;
    visibility: visible;
  }

  .tabs_section.type_more .col-md-6 .char_block {
    padding-left: 40px;
  }

  .catalog_detail.detail.fixed_wrapper #reviews_content {
    width: 75%;
    padding-right: 7px;
  }

  .box-shadow:hover {
    border-color: #fff;
    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.1);
    -webkit-transform: translateY(-1px);
    transform: translateY(-1px);
    background-color: #fff;
    z-index: 1;
  }
  .box-shadow-sm:hover {
    border-color: #fff;
    border-color: var(--stroke_black);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    z-index: 1;
  }

  .type_more.tabs_section {
    padding-right: 20px;
  }

  .sticky_menu header.fixed,
  .sticky_menu #headerfixed,
  .sticky_menu #headerfixed.fixed,
  .sticky_menu ~ .basket_bottom_block,
  .wrapper1.sticky_menu .product-item-detail-tabs-container-fixed {
    width: auto;
    left: 271px;
    right: 0;
  }

  .sticky_menu.sm header.fixed,
  .sticky_menu.sm #headerfixed,
  .sticky_menu.sm #headerfixed.fixed,
  .sticky_menu.sm ~ .basket_bottom_block,
  .wrapper1.sticky_menu.sm .product-item-detail-tabs-container-fixed {
    left: 77px;
    right: 0;
  }

  .with_left_block .wrapper_inner.front .drag-block.container .content_wrapper_block > .maxwidth-theme {
    padding-left: 0px;
    padding-right: 0px;
  }

  .right_block.wide_N,
  .right_block.wide_,
  .catalog_page.wide_N .section-content-wrapper,
  .catalog_page .section-content-wrapper.with-leftblock {
    float: right;
    width: calc(100% - 277px);
    position: relative;
  }
  .side_RIGHT .right_block.wide_N,
  .side_RIGHT .right_block.wide_ {
    width: calc(100% - 244px) !important;
    float: left;
  }
  .side_RIGHT.front_page .right_block.wide_N,
  .side_RIGHT.front_page .right_block.wide_ {
    padding-right: 33px;
  }
  .side_RIGHT .catalog_page.wide_N .section-content-wrapper,
  .side_RIGHT .catalog_page .section-content-wrapper.with-leftblock {
    width: calc(100% - 277px) !important;
    float: left;
  }

  #main .container_inner > .right_block.catalog_page {
    width: 100% !important;
  }

  /*search*/
  .search_page .section-content-wrapper .menu_top_block {
    display: none;
  }

  .catalog_in_content .section-content-wrapper .menu_top_block {
    display: none;
  }

  .wrapper1:not(.front_page):not(.catalog_page) .right_block.wide_N,
  .wrapper1:not(.front_page):not(.catalog_page) .right_block.wide_ {
    width: calc(100% - 302px);
  }
  .wrapper1.side_RIGHT:not(.front_page):not(.catalog_page) .right_block.wide_N > .middle,
  .wrapper1.side_RIGHT:not(.front_page):not(.catalog_page) .right_block.wide_ > .middle {
    padding-right: 40px;
  }

  .banners-content .img {
    display: table-cell !important;
  }

  .with_fast_view .list_item .fast_view_block {
    display: inline-block;
  }

  .banners-content .maxwidth-banner .maxwidth-theme {
    min-height: 500px;
  }

  .with-text-block-wrapper > .row > div:first-of-type {
    padding-bottom: 60px;
  }
  .wrapper1:not(.with_left_block)
    .wrapper_inner.front
    .drag-block.container
    .with-text-block-wrapper
    > .row
    > div:first-of-type {
    padding-bottom: 70px;
  }

  body .cost.prices .price_matrix_block .price_wrapper_block .price.discount {
    display: none;
  }
  body .catalog_block .col-5 .catalog_item .image_wrapper_block {
    padding: 0px 10px;
  }

  .blog_wrapper.blog .first-item .item {
    background-size: cover;
  }
  .blog_wrapper.blog .first-item .inner-item {
    padding: 0px;
    padding-bottom: 70%;
  }
  .blog_wrapper.blog .first-item .image {
    max-height: none;
    display: none;
  }
  .blog_wrapper.blog .first-item .title {
    position: absolute;
    bottom: 30px;
    color: #fff;
    z-index: 2;
  }
  .blog_wrapper.blog .first-item .title .date-block,
  .blog_wrapper.blog .first-item .title a {
    color: #fff !important;
  }
  .blog_wrapper.blog .first-item .title a {
    font-size: 1.286em;
  }
  .blog_wrapper.blog .first-item .gradient_block {
    z-index: 1;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    background: rgba(0, 0, 0, 0);
    background: -moz-linear-gradient(90deg, rgba(0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
    background: -webkit-linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
    background: -o-linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
    background: -ms-linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
  }

  .adv_bottom_block .img_inner span {
    height: 150px;
  }

  /*.catalog_section_list.items.row{margin:0px;}*/

  .flexslider.color-controls .flex-control-nav {
    display: none;
  }
  .col-sm-offset-2 {
    margin-left: 0px;
  }
  .bx-soa-item-table .bx-soa-item-tr {
    display: table-row !important;
  }

  .basket_bottom .basket-link {
    display: none !important;
    width: 0px !important;
  }

  /*filter*/
  body #content .wrapper_inner .left_block > .visible_mobile_filter {
    display: block !important;
  }
  .catalog .top_block_filter_section {
    display: none;
  }
  .bx_filter.bx_filter_vertical #modef_mobile {
    display: none !important;
  }

  /*personal*/
  .personal_wrapper .col-md-offset-3 {
    margin-left: 25%;
  }
  .personal_wrapper .col-md-offset-5 {
    margin-left: 41.66666667%;
  }
  .personal_wrapper .orders_wrapper .sale-order-detail-payment-options-shipment-composition-map {
    margin-left: 16.66666667%;
  }

  .bx_filter.bx_filter_vertical {
    display: block !important;
  }

  .bx_filter.bx_filter_vertical.empty-items {
    display: none !important;
  }

  .catalog_item.big .image_wrapper_block {
    padding-top: 50%;
    flex-grow: 1;
    height: 100%;
  }
  html.bx-mac.bx-chrome .catalog_item.big .image_wrapper_block {
    height: auto;
  }
}
@media all and (min-width: 992px) and (max-width: 1299px) {
  .header-v7.basket_normal.regions_Y .logo_and_menu-row .personal-link .wrap,
  .header-v7.basket_normal.regions_Y .logo_and_menu-row .basket-link .wrap {
    display: none;
  }
  .header-v16.basket_normal.regions_Y .logo_and_menu-row .phone {
    display: none;
  }
}
@media all and (min-width: 992px) and (max-width: 1199px) {
  .front:not(.wide_page) .tabs_slider .catalog_item_wrapp.col-m-20 {
    width: 33.33333333333333%;
  }
  .social-block .social-icons {
    margin: 0px 0px 20px;
  }
  .header-v4.basket_normal.regions_Y .logo_and_menu-row .personal-link .wrap,
  .header-v4.basket_normal.regions_Y .logo_and_menu-row .basket-link .wrap,
  .header-v4.basket_normal.regions_Y .logo_and_menu-row  .sites__current.sites__current,
  .header-v13.basket_normal.regions_Y .logo_and_menu-row .personal-link .wrap,
  .header-v13.basket_normal.regions_Y .logo_and_menu-row .basket-link .wrap,
  .header-v13.basket_normal.regions_Y .logo_and_menu-row .sites__current.sites__current  {
    display: none;
  }
  .header-v12.basket_normal.regions_Y .logo_and_menu-row .phone-block.with_btn > .inner-table-block:first-of-type {
    display: none;
  }
  .header-v14.basket_normal.regions_Y .logo_and_menu-row .search_wrap {
    display: none;
  }
  .header-v15.basket_normal.regions_Y .logo_and_menu-row .phone {
    display: none;
  }
  .header-v15.basket_normal.regions_Y .logo_and_menu-row .callback-block {
    margin: 0px;
  }
  .header-v16.basket_normal.regions_Y .logo_and_menu-row .search-wrapper {
    display: none;
  }

  .header-v4 .logo_and_menu-row .svg-cabinet,
  .header-v4 .logo_and_menu-row .svg-cabinet-login {
    top: -2px;
  }
}
@media all and (max-width: 992px) and (min-width: 768px) {
  .wrapper_inner .staff.list .item .info {
    margin: 10px 0px 0px 0px;
  }
}
@media (max-width: 991px) and (min-width: 768px) {
  /*personal*/
  .personal_wrapper .orders_wrapper .visible-sm {
    display: block !important;
  }
  .hidden-sm {
    display: none !important;
  }
  footer .ext_view .pay_system_icons {
    max-width: 200px;
  }

  .header-cart .basket_fly {
    width: 700px;
    right: -700px;
  }

  .catalog_section_list .section_item .image {
    width: 90px;
  }
  .catalog_section_list .section_item .image img {
    max-width: 100%;
    max-height: 100%;
  }
}
/* SM */
@media screen and (max-width: 991px),
  projection and (max-width: 991px),
  tv and (max-width: 991px),
  handheld and (max-width: 991px) {
  .basket-coupon-alert {
    margin-bottom: 17px;
  }
  .basket-coupon-alert-section {
    margin-bottom: 0;
  }
  .mega_fixed_menu {
    display: none !important;
  }
  #headerfixed.fixed,
  #headerfixed > .maxwidth-theme,
  #headerfixed > .wrapper_inner,
  .top-block .address,
  body .product-item-detail-tabs-container-fixed {
    display: none;
  }
  .item-views.blocks {
    padding-top: 40px;
  }
  .share.top {
    margin-top: 0px;
  }
  .mega-menu {
    z-index: 3002;
  }

  .front_page .wraps > .wrapper_inner {
    padding-left: 0;
    padding-right: 0;
  }

  div#mobileheader.fixed ~ #content {
    padding-top: 63px;
  }
  .right_block #filter-helper {
    display: none !important;
  }

  .blog_wrapper.blog .first-item .item {
    background: none !important;
  }

  .with_left_block .hot-wrapper-items .items {
    border: none;
    padding: 0;
  }

  .top_slider_wrapp .flexslider .slides > li,
  .top_slider_wrapp .flexslider .slides > li,
  .top_slider_wrapp .flexslider .slides > li td,
  .top_slider_wrapp .flexslider .slides > li td,
  .top_slider_wrapp .flexslider,
  .top_slider_wrapp .flexslider {
    height: 380px;
  }
  .top_slider_wrapp .banner_title .section {
    display: none;
  }
  .top_slider_wrapp .flexslider .flex-control-nav {
    top: -48px;
  }
  .front.wide_page .col-m-60 .top_slider_wrapp {
    padding-bottom: 30px;
  }
  .top_slider_wrapp .flex-direction-nav li {
    right: -10px;
  }
  .top_slider_wrapp .flex-direction-nav li:first-child {
    left: -10px;
  }
  body .front_slider .item-title a span {
    font-size: 17px;
    line-height: 22px;
  }
  body .flexslider .text .banner_title .head-title {
    font-size: 22px;
    line-height: 30px;
  }
  body .flexslider .text .banner_text {
    line-height: 1.6em;
    margin-top: 11px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  body .flexslider .text .banner_buttons {
    margin-top: 15px;
  }

  .top_big_banners .wrap_tizer .wrap_outer.title {
    font-size: 13px;
  }
  .wrap_tizer .wrap_outer.title {
    font-size: 13px;
  }
  .wrap_tizer .wr_block.price {
    line-height: 16px;
  }
  .wr_block.price .wrap_outer_desc {
    font-size: 12px;
  }

  .bx-touch .section-gallery-wrapper__item-nav:before {
    display: none;
  }

  .top_big_banners > .row > div.col-m-20 {
    font-size: 0px;
  }
  .top_big_banners .col-m-20 {
    margin: 0px -15px;
  }
  .top_big_banners .col-m-20 .item {
    width: 50%;
    display: inline-block;
    vertical-align: top;
    padding: 0px 15px 30px;
  }

  .top_slider_wrapp .slides .banner_title .view_sale_block,
  .top_slider_wrapp .slides .banner_title .sale_block {
    display: none;
  }

  .top_slider_wrapp .slides .banner_title .stickers {
    padding-bottom: 11px;
  }
  body .top_slider_wrapp .slides .text .banner_title .votes_block {
    margin-top: 7px;
  }
  .top_slider_wrapp .slides .banner_title .prices {
    margin-top: 6px;
  }
  .top_slider_wrapp .slides .banner_buttons .btn {
    padding: 12px 21px 12px;
    margin: 3px 3px 7px 3px;
  }
  .top_slider_wrapp .slides .wraps_buttons .wrap {
    height: 40px;
    width: 40px;
  }
  .top_slider_wrapp .slides .banner_buttons.with_actions {
    margin-top: 8px;
  }
  .top_slider_wrapp .box .btn.btn-video:not(.play) {
    padding: 28px 23px 10px;
  }

  .flexslider .banner_text {
    max-height: 75px;
    margin-top: 13px;
  }
  .top_slider_wrapp .slides .banner_title .prices .price:not(.price_old) {
    font-size: 1.067em;
  }

  body .with_fast_view .fast_view_block {
    display: none;
  }
  .mobile:not(.previewMode) .fast_view_frame.popup {
    display: none !important;
  }
  .mobile .all_viewed .jqmOverlay,
  .all_viewed .jqmOverlay {
    display: block !important;
  }

  .adv_bottom_block .img_inner span {
    padding-bottom: 17%;
    background-size: cover;
  }

  body .ui-panel-top-devices-inner {
    display: none;
  }

  /*personal*/
  .personal_wrapper .orders_wrapper .col-sm-12 {
    width: 100%;
  }
  .personal_wrapper .orders_wrapper .col-sm-10 {
    width: 83.33333333%;
  }
  .personal_wrapper .orders_wrapper .col-sm-7 {
    width: 58.33333333%;
  }
  .personal_wrapper .orders_wrapper .col-sm-6 {
    width: 50% !important;
  }
  .personal_wrapper .orders_wrapper .col-sm-5 {
    width: 41.66666667%;
  }
  .personal_wrapper .orders_wrapper .col-sm-2 {
    width: 16.66666667%;
  }
  .personal_wrapper .orders_wrapper .sale-order-detail-payment-options-methods-button-element {
    display: block;
    float: none;
    width: 50%;
    margin: 10px auto 0px;
  }
  .personal_wrapper .orders_wrapper .sale-order-detail-payment-options-shipment-composition-map > .row {
    margin: 0px -1px 0px -1px;
  }
  .personal_wrapper .orders_wrapper .sale-order-title {
    padding-right: 0px;
  }

  .basket_bottom_block {
    display: none;
  }
  .basket_bottom_block .maxwidth-theme .basket-link .wrap,
  .basket_bottom_block .maxwidth-theme .basket-link .title {
    display: none;
  }
  body .basket_bottom_block .basket-link.basket.basket-count .count {
    position: static;
  }
  .basket_bottom_block .maxwidth-theme .svg {
    margin: 0px 10px 0px 0px;
  }

  .wrapper.has_menu #header .middle-h-row .center_block .search,
  .wrapper.has_menu #header .middle-h-row .center_block .middle_phone {
    width: 100%;
    display: block;
    margin-left: 0px;
  }
  .wrapper.has_menu #header .middle-h-row .center_block .middle_phone {
    margin: 0px 0px 10px;
  }
  .middle_phone .phones .order_wrap_btn {
    margin-top: 0px;
  }
  .item-views.table-type-block.news-project .items > div:nth-child(2n + 1) {
    clear: left;
  }

  body #content .wrapper_inner .left_block {
    display: none;
  }
  body #content .wrapper_inner .left_block > * {
    display: none;
  }
  body .wrapper_inner .left_block .left_menu {
    display: block;
  }
  body #content .wrapper_inner .right_block:not(.ordered-block) {
    padding-left: 0px;
    margin: 0px !important;
  }
  body #content .wrapper_inner .contents_page .right_block.maxwidth-theme {
    padding-left: 30px;
  }
  .news_akc_block .img img {
    max-height: 100%;
  }
  body .wrapper_inner .left_block.vertical {
    position: absolute;
    width: 95%;
    float: none;
    padding: 0px;
    margin: 0px;
    display: block;
  }

  body #content .wrapper_inner .left_block.filter_visible {
    display: block;
    position: static;
  }
  body #content .wrapper_inner .left_block > .visible_mobile_filter {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    z-index: 100;
  }
  body #content .wrapper_inner .left_block.filter_ajax {
    float: none;
    width: 100%;
  }
  body #content .wrapper_inner .left_block.filter_ajax > .bx_filter {
    position: absolute;
    left: 15px;
    right: 15px;
    width: auto;
    z-index: 55;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
  }

  .catalog.vertical .adaptive_filter,
  .catalog.with_filter .adaptive_filter,
  .search-page-wrap + .catalog .adaptive_filter {
    display: block;
  }
  .js_filter .bx_filter.bx_filter_vertical .bx_filter_section {
    margin: 0px;
  }

  /*filter*/
  .filter_exists .filter-panel .filter-vertical.filter-panel__filter {
    display: block;
  }
  #mobilefilter .bx_filter.bx_filter_vertical.empty-items .filter-bnt-wrapper {
    display: none;
  }
  /**/

  .m_color_none.h_color_colored .main-nav {
    background: #fff;
  }
  .m_color_none.h_color_colored .main-nav .menu > li > a {
    color: #1d1a1a;
  }
  .m_color_none.h_color_colored .header_wrap .center_block .search_block .icon {
    background-position: -66px -178px;
  }
  .m_color_none.h_color_colored #header {
    margin-bottom: 47px;
  }
  .h_color_colored.m_color_none ul.menu .child {
    padding-top: 8px;
  }
  .h_color_white.m_color_none ul.menu .child {
    padding-top: 6px;
  }
  .head_type_1 #header {
    border-bottom-width: 1px;
  }
  .basket_fly #header .middle-h-row .center_block .main-nav ul.menu {
    width: 100%;
  }
  .basket_fly .wrapper_inner ul.menu .child {
    margin-top: 0px;
  }
  .basket_fly .main-nav .search_middle_block {
    top: -100%;
    right: 65px;
    margin: -16px 0px 0px;
    width: 50%;
  }
  .top_big_banners > .row > div .row {
    margin: 0px;
  }
  body .wrapper .top_slider_wrapp .flexslider .slides > li,
  body .wrapper .top_slider_wrapp .flexslider .slides > li td,
  body .wrapper .top_slider_wrapp .flexslider {
    height: 300px !important;
  }
  body .right_side.catalog {
    display: none;
  }
  body .left_side.catalog_detail {
    padding-right: 0;
  }
  body .top-h-row .phone {
    width: auto;
  }
  body .top-h-row ul.menu {
    width: 45%;
    padding-left: 0;
  }
  body #header .center_block .main-nav {
    width: 100%;
    position: absolute;
    right: 0;
    left: 0px;
    top: 100%;
    margin: 18px 0px 0px 0px;
    padding: 7px 0px;
  }
  body .wrapper.head_type_1:not(.front_page) #header {
    margin-bottom: 60px;
  }
  body .wrapper.head_type_1:not(.front_page) #header + .wrapper_inner {
    border-top: 1px solid #f0f0f0;
    box-shadow: 0px 0px 5px #f0f0f0;
    -moz-box-shadow: 0px 0px 5px #f0f0f0;
    -o-box-shadow: 0px 0px 5px #f0f0f0;
    -webkit-box-shadow: 0px 0px 5px #f0f0f0;
  }
  body .wrapper.head_type_1:not(.front_page) .middle {
    margin-top: 10px;
  }
  body .wrapper.head_type_1 .top_slider_wrapp {
    margin-top: 49px !important;
  }
  body #header:not(.border) + .middle.main {
    padding-top: 207px;
  }
  body #header:not(.border) + .middle {
    padding-top: 227px;
  }

  body .top_slider_wrapp .flexslider .slides li td.text.left .banner_text,
  body .top_slider_wrapp .flexslider .slides li td.text.left .banner_title,
  body .top_slider_wrapp .flexslider .slides li td.text.left .banner_buttons,
  .flexslider .left .text .section {
    margin-left: 80px;
  }

  body .top_slider_wrapp .flexslider .slides li td.text.right .banner_text,
  body .top_slider_wrapp .flexslider .slides li td.text.right .banner_title,
  body .top_slider_wrapp .flexslider .slides li td.text.right .banner_buttons,
  .flexslider .right .text .section {
    margin-right: 80px;
  }

  body .right_block.catalog .catalog_block .catalog_item_wrapp {
    width: 33%;
    width: 230px;
  }

  body .projects-blocks .bx_item_detail_inc_two {
    margin-top: 20px;
    padding-top: 20px;
  }

  .banners-content .maxwidth-banner .maxwidth-theme {
    min-height: 400px;
  }

  .catalog_detail .element_detail_text .sh {
    margin: 0px;
  }
  .wrapper_inner .bottom.middle td > .coupon #COUPON {
    width: 200px;
  }
  .wrapper_inner .middle .module-cart table tr td.count-cell {
    padding-left: 5px;
    padding-right: 5px;
  }
  .wrapper_inner .middle .module-cart table tr td.count-cell .counter_block.big_basket {
    width: 74px;
  }
  .wrapper_inner .middle .module-cart .counter_block.big_basket > span {
    line-height: 27px;
    height: 27px;
    width: 21px;
  }
  .wrapper_inner .middle .module-cart .counter_block.big_basket input[type="text"] {
    font-size: 10px;
    height: 27px;
    width: 32px;
  }
  .has_menu #header .middle-h-row .center_block {
    white-space: normal;
  }
  .basket_fly .search_middle_block,
  .has_menu .search_middle_block {
    position: absolute;
  }
  .wrapper.m_color_none .top_slider_wrapp {
    margin-top: 50px;
  }
  body .wrapper:not(.front_page) .middle {
    float: none;
  }

  .js_filter.filter_horizontal {
    overflow: visible;
    padding: 0;
    position: relative;
  }
  .js_filter .bx_filter.bx_filter_vertical {
    display: none;
    position: absolute;
    margin: 0;
    width: 100%;
    top: 37px;
    box-shadow: 0 0px 10px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 0px 10px rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: 0 0px 10px rgba(0, 0, 0, 0.15);
    z-index: 101;
  }
  .bx_filter .bx_filter_section {
    border: none;
  }
  .vacancy.item-views.accordion .pay {
    float: none;
  }

  .bottom-menu,
  .social-block .social-icons {
    margin: 0px 0px 20px;
  }
  .subscribe-block-wrapper .text {
    margin: 0px 0px 20px;
  }
  .bottom-menu .items > .item-link {
    margin-bottom: 8px;
  }
  #footer .footer_bottom_inner .phones,
  #footer .footer_bottom_inner .social_wrapper {
    float: left;
  }
  #footer .footer_bottom_inner .social_wrapper .social {
    padding-top: 0px;
    padding-left: 15px;
  }

  #bx-soa-order .bx-soa-cart-total.bx-soa-cart-total-fixed {
    position: static;
    width: 100% !important;
  }

  /*basket2*/
  #basket-root .basket-checkout-section-inner {
    display: block;
  }
  #basket-root .basket-checkout-section-inner > div {
    display: inline-block;
    vertical-align: top;
    text-align: left;
  }
  #basket-root .basket-checkout-container .basket-checkout-block-btns {
    float: right;
  }

  /*personal*/
  body .personal_page #content .wrapper_inner .left_block {
    display: block;
    float: none;
    width: 100%;
  }
  body .personal_page #content .wrapper_inner .left_block .left_menu {
    display: block;
    text-align: left;
  }
  body .personal_page #content .wrapper_inner .left_block .left_menu > li {
    display: inline-block;
    margin-top: 3px;
  }
  body .personal_page #content .wrapper_inner .left_block .left_menu > li > a {
    border: none;
  }
  body .personal_page #content .wrapper_inner .left_block .left_menu > li {
    display: none;
  }
  body .personal_page #content .wrapper_inner .left_block .left_menu > li.exit {
    display: block;
    width: 50%;
    padding-right: 10px;
  }
  body .personal_page #content .wrapper_inner .right_block .breadcrumbs {
    display: none;
  }

  .front_page.with_left_block .banners_slider_wrap.CONTENT_TOP,
  .front_page.with_left_block .banners_slider_wrap.CONTENT_BOTTOM {
    padding-left: 30px;
    padding-right: 30px;
  }
}

@media all and (max-width: 900px) {
  .wrapper_inner .stores .stores_list {
    width: 66%;
  }
  .basket_wrapp .module-cart table.bottom.middle .bottom_btn td.last_blockk.basket_error_wrapp {
    float: left !important;
  }
  .count-cell div.error {
    white-space: normal;
  }

  .footer_inner .left_block {
    display: none;
  }
  .footer_inner .right_block {
    padding-left: 0px;
  }
  #footer .mobile_copy {
    display: block;
  }
}
@media all and (max-width: 870px) {
  body .module-cart .weight-th,
  body .module-cart .discount-th,
  body .module-cart .weight-cell,
  body .module-cart .discount-cell {
    display: none;
  }
  .wrap_md .news_wrap,
  .wrap_md .subscribe_wrap {
    width: 100%;
    margin: 0px;
  }
  .wrap_md .subscribe_wrap .subscribe-form {
    margin: 30px 0px 30px;
  }
  .subscribe-form .wrap_bg {
    background-position: center -34px;
  }
  .subscribe-form .wrap_bg .top_block,
  .subscribe-form .wrap_bg .sform {
    width: 100%;
    display: block;
    margin-top: 0px;
  }
  .subscribe-form .wrap_bg .top_block {
    padding: 0px 0px 20px 0px;
  }
  .subscribe-form .top_block .image {
    display: none;
  }
  .subscribe_wrap .subscribe-form .top_block .image + .text {
    padding: 0px;
  }
  .subscribe_wrap .subscribe-form form.sform > div {
    display: block;
    width: 100%;
  }
  .top-h-row .phones {
    text-align: left;
  }
  body .top-h-row ul.menu {
    width: 40%;
  }
  .module_products_list td.price-cell {
    width: 18%;
  }
  .services_block .item {
    width: 100%;
  }
  .module-order-history.orderdetail .module-orders-list tr td.vimg {
    padding-right: 2px;
    padding-left: 2px;
  }
  .module-order-history.orderdetail .module-orders-list tr td.vdscnt {
    padding-right: 5px;
    padding-left: 5px;
  }
  .shops.list .item .schedule_phone_email {
    text-align: center;
  }
  .shops.list .item .schedule,
  .shops.list .item .phone_email {
    width: 100%;
  }
  .wrapper_inner .info_item .middle_info .prices_block,
  .wrapper_inner .info_item .middle_info .buy_block {
    width: 100%;
    padding: 0px;
  }
  .wrapper_inner .info_item .middle_info .buy_block {
    padding: 20px 0px 0px;
  }
  .wrapper_inner .info_item .middle_info .buy_block .counter_wrapp .button_block {
    margin: 0px;
  }
  .info_item .middle_info .buy_block .one_click {
    display: inline-block;
  }
  .wrapper_inner .stores .stores_list {
    width: 64%;
  }
  .table .wrapp_stockers .like_icons {
    height: auto;
    margin-top: 0px !important;
    position: initial;
  }
  .table .module_products_list td.like_icons {
    width: 56px;
    padding-left: 5px;
  }
  .module_products_list td.like_icons.full {
    width: 30px;
  }
  .wrapp_stockers .like_icons > div {
    margin-right: 0px;
  }

  .rows_block:not(.slides) .item_block {
    width: 50%;
  }

  .info_item .top_info .brand {
    float: none;
  }
  .footer_inner .social_wrapper .rows_block .item_block {
    width: auto;
  }
  .footer_bottom_inner .menus .rows_block .col-3 {
    width: 33%;
  }
  .footer_bottom_inner .rows_block .menus {
    width: 60%;
  }
  .footer_bottom_inner .rows_block .soc {
    width: 40%;
  }
  .footer_bottom_inner .rows_block .soc .soc_wrapper {
    float: right;
  }

  .top-h-row .phones {
    padding-left: 20px;
    padding-right: 0px;
  }
  .top-h-row .h-user-block a.icon {
    padding-right: 14px;
  }

  /*personal*/
  .personal_wrapper
    .orders_wrapper
    .sale-order-payment-change-pp-list
    .sale-order-payment-change-pp-company
    .sale-order-payment-change-pp-company-smalltitle {
    font-size: 12px;
  }
}

@media screen and (min-width: 851px) {
  /*basket2*/
  #basket-root .basket-checkout-block-btn {
    padding-left: 60px;
  }
  #basket-root .basket-checkout-container .fastorder {
    padding-left: 18px;
  }
}
@media screen and (max-width: 850px) {
  /*basket2*/
  #basket-root .basket-checkout-section-inner .basket-checkout-block-btn > .btn,
  #basket-root .basket-checkout-section-inner .fastorder > .btn {
    display: block;
    width: 100%;
  }
  #basket-root .basket-checkout-container .basket-checkout-block-btns {
    float: none;
    display: block;
  }
  #basket-root .basket-checkout-container .basket-checkout-block-btns-wrap {
    margin: 0 auto;
  }
}
@media all and (max-width: 800px) {
  .wrapper.has_menu #header .middle-h-row .center_block .middle_phone .phone_text a {
    font-size: 15px;
  }
  .wrapper.has_menu #header .middle-h-row .center_block .middle_phone .order_wrap_btn {
    margin-left: 16px;
  }
  .module_products_list td.price-cell {
    width: 14%;
  }
  .sort_header .sort_filter a {
    margin-right: 10px;
  }
  .wrapper_inner .module-order-history .result-row a.button {
    margin-right: 0px;
  }
  .wrapper_inner .stores .stores_list {
    width: 60%;
  }
  .top-h-row .phones {
    padding-left: 5px;
  }
}

@media all and (min-width: 769px) {
  body .flexslider.flexslider-control-nav .flex-control-nav {
    display: none;
  }
}
@media all and (min-width: 768px) {
  /*breadcrumbs*/
  .breadcrumbs__item--visible-mobile .svg {
    display: none;
  }
  /**/

  body .catalog.horizontal .adaptive_filter {
    display: none !important;
  }
  .bx-core .filter_horizontal:not(.js_filter) .bx_filter.bx_filter_vertical {
    display: block !important;
  }
  .bx_filter .bx_filter_section {
    margin: 0 0px 27px 0;
  }
  .bx_filter_vertical.bx_filter .hidden_values {
    display: block !important;
  }
  .staff.item-views.list .item .image.padding {
    padding: 50px 0px 40px 30px;
  }
  .wrap_md .big {
    padding-top: 22px;
  }

  .wrapper_inner.front .drag-block.container .content_wrapper_block > .maxwidth-theme,
  .wrapper_inner.front .drag-block.container > .maxwidth-theme,
  .wraps .wrapper_inner.front .drag-block.container > .grey_block > .maxwidth-theme {
    padding-left: 30px;
    padding-right: 30px;
  }

  .review_frame.jqmWindow {
    width: 700px !important;
    max-width: none;
  }

  .flexslider.hovers .flex-direction-nav .flex-prev {
    left: -50px;
  }
  .flexslider.hovers:hover .flex-direction-nav .flex-prev {
    left: 0px;
  }
  .flexslider.hovers:hover .flex-direction-nav a.flex-next {
    right: 0px;
  }
  .flexslider.hovers .flex-direction-nav a.flex-next {
    right: -50px;
    text-align: right;
  }
  .flexslider.hovers .flex-direction-nav a {
    opacity: 0;
    visibility: hidden;
  }
  .flexslider.hovers .flex-direction-nav .flex-nav-next {
    right: -26px;
  }

  .detail .detailimage.image-left + .introtext_wrapper {
    margin-left: 33.33333333333333%;
  }
  .detail .detailimage.image-left + .introtext_wrapper > div {
    margin-left: 30px;
  }
  .detail .detailimage.image-right + .introtext_wrapper {
    margin-right: 33.33333333333333%;
  }
  .detail .detailimage.image-right + .introtext_wrapper > div {
    margin-right: 30px;
  }

  .items-services > .row > div {
    margin: 0px 0px -1px -1px;
  }
  footer .address.blocks {
    padding-right: 20px;
  }

  .catalog_detail .offers_table .opener.bottom {
    display: none;
  }

  .contacts-page-map + .contacts.contacts-page-map-overlay {
    left: 0px;
    right: 0px;
    position: absolute;
  }
  #bx-soa-order.orderform--v1 #bx-soa-delivery,
  #bx-soa-order.orderform--v1 #bx-soa-paysystem {
    width: calc(50% - 15px);
    margin-right: 15px;
  }
  #bx-soa-order.orderform--v1 #bx-soa-delivery + #bx-soa-paysystem,
  #bx-soa-order.orderform--v1 #bx-soa-paysystem + #bx-soa-delivery {
    margin-right: 0;
    margin-left: 15px;
  }
  #bx-soa-order.orderform--v1 #bx-soa-delivery .bx-soa-coupon,
  #bx-soa-order.orderform--v1 #bx-soa-paysystem .bx-soa-coupon {
    display: none;
  }
  #bx-soa-order.orderform--v1 #bx-soa-coupon {
    display: block;
  }
  #bx-soa-order.orderform--v1 #bx-soa-pickup {
    order: 2;
  }
  #bx-soa-order.orderform--v1 #bx-soa-delivery + #bx-soa-paysystem {
    order: 1;
  }

  /*video banner*/
  .top_slider_wrapp .box .video + .wrapper_inner {
    display: none;
  }

  /**/
  .owl-carousel.hidden-dots .owl-dots {
    display: none;
  }

  /* filter compact */
  .bx_filter.compact {
    position: relative;
    top: 0 !important;
    padding: 11px 0 11px;
  }
  .bx_filter.compact .bx_filter_section {
    margin: 0;
    background: none;
    border: none;
  }
  .bx_filter.compact .bx_filter_parameters_box.title {
    float: left;
    z-index: 1;
  }
  .bx_filter.compact .bx_filter_parameters_box.title + .bx_filter_parameters {
    padding-left: 28px;
  }
  .bx_filter.compact .bx_filter_parameters_box.prop_type_E .bx_filter_parameters_box_container,
  .bx_filter.compact .bx_filter_parameters_box.prop_type_S .bx_filter_parameters_box_container,
  .bx_filter.compact .bx_filter_parameters_box.prop_type_L .bx_filter_parameters_box_container {
    max-height: 239px;
  }
  .bx_filter.compact .bx_filter_parameters_box {
    margin: 4px 4px 4px;
    position: relative;
    float: left;
    padding: 0;
    border: none;
    user-select: none;
  }
  .bx_filter.compact .bx_filter_parameters_box.title {
    margin: 4px 0;
  }
  .bx_filter.compact .bx_filter_parameters_box_title {
    font-size: 13px;
  }
  .bx_filter.compact .bx_filter_parameters_box_title {
    padding-right: 15px;
  }
  .bx_filter.compact .bx_filter_parameters_box_title.prices:not(.title) {
    margin-bottom: 15px;
    cursor: default;
  }
  .bx_filter.compact .bx_filter_parameters_box_title.prices:not(.title):hover {
    color: #333333;
    color: var(--white_text_black);
  }
  .bx_filter.compact .bx_filter_parameters_box_title.prices:after {
    display: none;
  }
  .bx_filter.compact .bx_filter_block:not(.limited_block) {
    position: absolute;
    padding: 19px 19px 0;
    display: none;
    min-width: 232px;
    z-index: 3;
    border-radius: 3px;
    background: #fff;
    background: var(--card_bg_black);
    -webkit-box-shadow: 0px 5px 25px 0px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0px 5px 25px 0px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 5px 25px 0px rgba(0, 0, 0, 0.1);
  }
  .bx_filter.compact .bx_filter_block.right:not(.limited_block) {
    left: auto;
    right: 0;
  }
  .bx_filter.compact .bx_filter_block.limited_block {
    display: block !important;
    max-height: none;
    overflow: visible;
    margin-bottom: 0;
  }
  .bx_filter.compact .prices .bx_filter_block {
    padding: 0;
  }
  .bx_filter.compact .bx_filter_block .price_block {
    padding: 19px 19px 15px;
    border-top: 1px solid #f2f2f2;
    border-color: var(--stroke_black);
  }
  .bx_filter.compact .bx_filter_block .price_block:first-of-type {
    border-top: none;
  }
  .bx_filter.compact .bx_filter_button_box {
    padding: 0;
  }
  .bx_filter.compact .char_name {
    position: relative;
    padding-bottom: 15px;
    margin-top: -15px;
  }
  .bx_filter.compact .props_list .hint {
    position: static;
  }
  .bx_filter.compact .props_list .hint .icon {
    position: static;
  }
  .bx_filter.compact .props_list .hint .text {
    padding: 0 0 0 8px;
    font-size: 13px;
    color: #888888;
  }
  .bx_filter.compact .props_list .hint .tooltip {
    top: -52px;
    opacity: 1;
  }
  .bx_filter.compact .props_list .hint .tooltip:after {
    bottom: -10px;
    left: 56px;
    top: auto;
    border: 5px solid transparent;
    border-top: 5px solid #fff;
    border-top-color: var(--card_bg_hover_black);
  }
  .bx_filter.compact .bx_filter_parameters_box.active .bx_filter_block {
    z-index: 390;
  }
  .bx_filter.compact .bx_filter_parameters_box.active .bx_filter_block i {
    padding: 0;
    font-size: 11px;
  }
  .bx_filter.compact .bx_filter_parameters_box.active .bx_filter_block .icon:hover i,
  .bx_filter.compact .bx_filter_parameters_box .bx_filter_block .hint.active .icon i {
    color: #fff;
  }
  .bx_filter.compact .bx_filter_parameters_box_title + .bx_filter_block .bx_filter_parameters_box_container {
    margin: 0;
    margin-bottom: 15px;
  }
  .bx_filter.compact label {
    margin: 0;
  }
  .bx_filter.compact .bx_filter_parameters_box:not(.prop_type_L) .label_block {
    margin: 0px 0 11px;
  }
  .bx_filter.compact .label_block:last-of-type {
    margin-bottom: 4px;
  }
  .bx_filter.compact .bx_filter_parameters_box_container {
    margin-top: 0px;
  }
  .bx_filter.compact .filter.label_block input[type="checkbox"] + label:after,
  .bx_filter.compact .filter.label_block input[type="checkbox"] + label:before {
    top: 0;
  }
  .bx_filter.compact .bx_filter_button_box {
    display: none;
    margin: 0 -19px;
    text-align: left;
    border-top: 1px solid #eee;
    border-color: var(--stroke_black);
    background: #fafafa;
    background: var(--darkerblack_bg_black);
  }
  .bx_filter.compact .bx_filter_button_box .btn {
    float: right;
    margin-top: -1px;
    width: 50%;
    padding-top: 18px;
    padding-bottom: 17px;
    border-radius: 0px;
  }
  .bx_filter.compact .bx_filter_button_box .bx_filter_container_modef {
    margin: 12px 5px 14px 18px;
    display: block;
    font-size: 13px;
    color: #666666;
    color: var(--light_basic_text_black);
  }
  .bx_filter.compact .bx_filter_search_button {
    display: none;
  }
  .bx_filter.compact .bx_filter_parameters_box.prices .bx_filter_button_box {
    margin: 0;
  }

  .bx_filter.compact .bx_filter_parameters_box > .bx_filter_parameters_box_title {
    padding: 1px 26px 2px 9px;
    white-space: nowrap;
  }
  .bx_filter.compact .bx_filter_parameters_box .title.bx_filter_parameters_box_title:not(.filter_title) {
    border: 1px solid transparent;
  }
  .bx_filter.compact .bx_filter_parameters_box .bx_filter_parameters_box_title:not(.filter_title):not(:hover) {
    border-color: #ccc;
    border-color: var(--stroke_black);
  }
  .bx_filter.compact .bx_filter_parameters_box > .bx_filter_parameters_box_title:not(.filter_title) > .svg-inline-down {
    position: absolute;
    top: 8px;
    right: 5px;
  }

  .bx_filter.compact .bx_filter_parameters_box .limited_block .bx_filter_parameters_box_title {
    padding: 1px 11px 2px 9px;
    white-space: nowrap;
  }
  .bx_filter.compact .bx_filter_parameters_box .limited_block .bx_filter_parameters_box_title .label_block {
    margin: 0px;
  }
  .bx_filter.compact .bx_filter_parameters_box .limited_block .filter label:before,
  .bx_filter.compact .bx_filter_parameters_box .limited_block .filter label:after {
    display: none;
  }
  .bx_filter.compact .bx_filter_parameters_box .limited_block .filter .bx_filter_param_text {
    margin: 0px;
  }
  .bx_filter.compact .bx_filter_parameters_box.set .limited_block .filter .bx_filter_param_text {
    color: #fff;
  }

  .bx_filter.compact .bx_filter_parameters_box .bx_filter_parameters_box_title.filter_title {
    padding-left: 0;
    padding-right: 16px;
    position: relative;
  }
  .bx_filter.compact .bx_filter_parameters_box .bx_filter_parameters_box_title.filter_title span {
    display: none;
  }
  .bx_filter.compact .bx_filter_parameters_box .bx_filter_parameters_box_title.filter_title .svg-inline-down {
    top: 0px;
  }

  .bx_filter.compact .bx_filter_parameters_box_title:hover .bx_filter_param_text,
  .bx_filter.compact .bx_filter_parameters_box_title:hover {
    color: #333;
    color: var(--white_text_black);
  }
  .bx_filter.compact .set .bx_filter_parameters_box_title:hover {
    box-shadow: none;
  }

  .bx_filter.compact .bx_filter_parameters_box.set .bx_filter_parameters_box_title:after {
    display: none;
  }
  .bx_filter.compact .bx_filter_parameters_box .bx_filter_parameters_box_title .delete_filter {
    display: none;
    position: absolute;
    top: -1px;
    right: -1px;
    bottom: -1px;
    width: 33px;
    cursor: pointer;
    border-radius: 0px 3px 3px 0px;
  }
  .bx_filter.compact .bx_filter_parameters_box .bx_filter_parameters_box_title .delete_filter svg {
    position: absolute;
    right: 12px;
    top: 50%;
    margin-top: -4px;
  }
  .bx_filter.compact .bx_filter_parameters_box .bx_filter_parameters_box_title .delete_filter svg path {
    fill-rule: evenodd;
  }
  .bx_filter.compact .bx_filter_parameters_box .bx_filter_parameters_box_title .delete_filter:before {
    content: "";
    display: block;
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    width: 1px;
    background: rgba(255, 255, 255, 0.1);
  }
  .bx_filter.compact .bx_filter_parameters_box.set .bx_filter_parameters_box_title .delete_filter {
    display: block;
  }
  .bx_filter.compact input[type="checkbox"] + label.dib > span {
    margin: 0;
  }
  .bx_filter.compact .bx_filter_input_checkbox .bx_filter_param_text {
    margin-left: 0px;
  }
  .bx_filter.compact .sku .bx_filter_input_checkbox .bx_filter_param_text {
    margin-left: 0;
  }
  .bx_filter_vertical.compact input[type="checkbox"] + label.nab:not(.sku) {
    padding: 5px 0px 5px 3px;
    margin: 0px;
  }
  .bx_filter.compact .bx_filter_parameters_box.set .bx_filter_parameters_box_title .count_selected {
    margin: 0 0 0 -3px;
  }
  .bx_filter.compact .bx_filter_parameters_box_container.pict_block label {
    margin: 0px 5px 0px 0px !important;
  }
  body .bx_filter.compact .bx_filter_block.limited_block .filter.label_block input + label {
    margin: 0px 0px 0px 3px;
    padding: 0px;
  }
  .bx_filter_vertical.compact .bx_filter_block label:not(.selected),
  .bx_filter_vertical .bx_filter_block label:not(.selected) span {
    color: #333;
    color: var(--white_text_black);
  }

  .bx_filter.compact .bx_filter_parameters_box_container .wrapp_change_inputs {
    width: 220px;
  }
  .bx_filter.compact .btn-link-text {
    padding: 8px 16px;
  }
  .bx_filter.compact .btn-link-text:hover {
    color: var(--white_text_black);
  }
  .bx_filter.compact .btn-link-text:hover svg path {
    fill: var(--white_text_black);
  }

  .bx_filter.compact .bx_filter_parameters_box.set .bx_filter_parameters_box_title.title {
    color: #fff;
    padding-right: 41px;
  }
  .bx_filter.compact .bx_filter_parameters_box.set .bx_filter_parameters_box_title svg path {
    fill: #fff;
  }
  .bx_filter.compact .bx_filter_parameters_box.set .bx_filter_parameters_box_title .svg-inline-down {
    display: none;
  }

  .bx_filter.compact .smartfilter > .mCustomScrollbar > .mCustomScrollBox,
  .bx_filter.compact .smartfilter > .mCustomScrollbar,
  .bx_filter.compact .smartfilter > .mCustomScrollbar > .mCustomScrollBox > .mCSB_container {
    overflow: visible;
  }
  #mobilefilter .bx_filter.bx_filter_vertical.compact,
  #wrapInlineFilter .bx_filter .smartfilter > .mCustomScrollbar > .mCustomScrollBox > .mCSB_scrollTools {
    display: none !important;
  }

  /*basket2*/
  #basket-root .basket-items-list .basket-items-list-item-container > td:first-child {
    padding-left: 31px;
  }
  #basket-root .basket-items-list-header-filter-item {
    margin-left: 20px;
  }
  #basket-root .top_control {
    margin-left: 70px;
  }
  #basket-root .basket-items-list-item-descriptions {
    padding-top: 29px;
    width: auto;
  }
  #basket-root .basket-items-list .basket-item-block-info {
    padding-right: 55px;
  }
  #basket-root .basket-items-list-item-container > td {
    padding-bottom: 33px;
  }
  .sale-products-gift .product-item-label-text.product-item-label-small span,
  .sale-products-gift .product-item-scalable-card.hover .product-item-label-text.product-item-label-small span {
    padding-bottom: 4px;
  }
  #basket-root .basket-checkout-block-total-price {
    padding: 0px 20px 0px 5px;
  }
}

/* XS */
@media screen and (max-width: 768px),
  projection and (max-width: 768px),
  tv and (max-width: 768px),
  handheld and (max-width: 768px) {
  .basket_wrapp .header-cart.fly .clicked > a {
    display: block;
  }
  .catalog_detail .element_detail_text .price_txt > .text {
    padding: 20px 0px 0px;
    clear: both;
  }
  body .list-type-block.item-views .item.wti .body-info .properties {
    max-width: 400px;
  }

  .flex-direction-nav {
    display: none !important;
  }
  .item-views.brands .flex-direction-nav {
    display: block !important;
  }

  body .flexslider .flex-control-paging {
    top: -3px;
  }
  /*body .top_slider_wrapp.view_3 .flexslider .flex-control-paging {
    top: auto;
    position: absolute;
    transform: translateX(-50%);
    bottom: 15px;
  }
  .top_slider_wrapp.view_3 .flexslider .slides li tr {
    display: block !important;
  }*/
}
@media screen and (max-width: 767px),
  projection and (max-width: 767px),
  tv and (max-width: 767px),
  handheld and (max-width: 767px) {

  :root {
    --theme-page-width-padding: 16px;
  }

  /*breadcrumbs*/
  .breadcrumbs__dropdown-wrapper {
    display: none !important;
  }
  .breadcrumbs__item--mobile:not(.breadcrumbs__item--visible-mobile),
  .breadcrumbs__item--mobile + .breadcrumbs__separator {
    display: none;
  }
  .breadcrumbs__item--visible-mobile:not(:hover) .svg path {
    fill: #b5b5b5;
  }
  .breadcrumbs__item--visible-mobile .svg {
    margin: 1px 14px 0px 0px;
  }
  .compact-breadcrumbs-slider .breadcrumbs {
    white-space: nowrap;
    overflow-x: auto;
    margin-right: -16px;
    margin-left: -16px;
    padding-left: 16px;
  }
  /**/

  .topic__inner > div.btn_basket_heading--with_title .title {
    display: none;
  }
  .topic__inner > div.btn_basket_heading--with_title {
    width: 41px;
  }

  /* table sizes */
  .TABLES_SIZE_frame .form table:not(.sizes) tr {
    display: flex;
    flex-direction: column-reverse;
  }
  .TABLES_SIZE_frame .form table:not(.sizes) tr img {
    margin-bottom: 15px;
  }

  .wrapper_inner,
  .maxwidth-theme {
    padding-left: 15px;
    padding-right: 15px;
  }
  .top_inner_block_wrapper.maxwidth-theme {
    padding-left: 0px;
    padding-right: 0px;
  }
  .catalog .adaptive_filter {
    display: block;
  }

  .top_big_banners > .row > div {
    padding-left: 15px;
    padding-right: 15px;
    margin: 0px;
  }
  .top_big_banners > .row > div.col-m-20 {
    padding-left: 0px;
    padding-right: 0px;
  }
  .top_big_banners > .row > div.blocks .item {
    padding-left: 0px;
    padding-right: 0px;
    width: 100%;
  }
  .top_big_banners .wrap_tizer .wrap_outer.title {
    font-size: 14px;
  }
  .top_slider_wrapp .flex-direction-nav {
    display: none;
  }

  .top_slider_wrapp .slides .wraps_buttons .wrap {
    height: 36px;
    width: 36px;
  }

  .top-h-row .phones {
    width: 56%;
    padding-left: 0px;
  }
  .top-h-row .h-user-block {
    text-align: right;
  }
  .wrapper.has_menu .top-h-row ul.menu {
    display: none;
  }
  .wrapper.has_menu .top-h-row .phones {
    display: block;
  }
  .wrapper.has_menu #header .wrapper_inner .middle-h-row .center_block .middle_phone,
  .wrapper.has_menu #header .phones {
    display: none;
  }
  .wrapper.has_menu #header .middle-h-row .center_block {
    padding: 0px 20px 0px 20px;
  }
  body .colored #header .center_block .main-nav {
    border-top: 1px solid #fff;
    margin-top: 20px;
  }
  body .wrapper.has_menu .white #header .center_block .main-nav {
    margin-top: 12px;
  }
  body .colored #header {
    padding-bottom: 20px;
  }
  .wrapper_inner .middle-h-row .search_middle_block.active .middle_form {
    width: 94%;
  }
  .backet_back_wrapp .basket_back {
    display: block;
  }
  .backet_back_wrapp.error .basket_back {
    display: inline-block;
  }
  .wrapper_inner .basket_wrapp .module-cart table.bottom.middle .bottom_btn td .iblock .icon_error_block {
    float: none;
    display: inline-block;
    text-align: left;
    margin: 15px 0px 0px;
  }
  .iblock .icon_error_block:after {
    display: block;
    right: -65px;
    left: initial;
  }
  .wrapper_inner .stores .stores_list {
    width: 55%;
  }
  .stores .flex-control-paging,
  .news_akc_block .flex-control-paging {
    position: absolute;
    bottom: -9px;
    display: none;
    z-index: 12;
    margin: 0px;
  }
  .news_akc_block .flex-control-paging {
    bottom: 4px;
  }
  .news_akc_block .news_slider_wrapp {
    padding: 0px 0px 10px;
  }

  body .catalog_section_list .item_block {
    padding: 0px 15px;
  }

  .items-services.item-views .item {
    height: auto !important;
  }

  .with-text-block-wrapper > .row > div:first-of-type {
    padding-bottom: 25px;
  }

  /* Start page teasers block */
  .wrapper_inner .start_promo .item {
    width: 33%;
  }
  .wrapper_inner .start_promo .item.wide50 {
    width: 33%;
  }
  .wrapper_inner .start_promo .item.wide100 {
    width: 100%;
  }

  .stores .all_map {
    margin: -1px 0px;
  }
  /*.stores{padding-bottom:24px;}*/
  .print .basket_print_desc .store_property {
    width: 47%;
  }
  .print .basket_print_desc .store_property:nth-of-type(3) {
    clear: both;
  }
  .info_item .middle_info .sku_props {
    margin: 0px;
  }
  .info_item .middle_info .prices_block,
  .info_item .middle_info .buy_block {
    width: 100%;
    padding: 0px;
  }
  .info_item .middle_info .prices_block {
    margin: 0px 0px 17px;
  }

  /*.flexslider.shadow .flex-viewport {
    margin-right: -16px;
    margin-left: -16px;
    padding-left: 16px;
    padding-right: 16px;
  }
  .flexslider.shadow .flex-viewport:before,
  .flexslider.shadow .flex-viewport:after {
    width: 16px;
  }*/

  table.colored.offers_table td.price {
    white-space: normal;
  }
  table.colored.offers_table td.price .cost.prices .price {
    font-size: 16px;
  }
  .adaptive.text {
    display: block;
  }
  .catalog_detail .adaptive.text > div {
    display: inline-block;
    margin: 5px 3px 0 0;
    vertical-align: top;
  }
  .catalog_detail .adaptive.text > .count.ablock {
    display: block;
  }
  .catalog_detail .adaptive.text > .wrap_md .buy {
    margin-left: 6px;
  }
  .catalog_detail .tabs_content .prices_tab {
    padding-top: 0px !important;
  }

  .catalog_detail .offers_table thead td,
  .catalog_detail .offers_table td.count,
  .catalog_detail .offers_table td.buy,
  .catalog_detail .offers_table .more_text,
  .catalog_detail .offers_table td.counter_block_wr,
  .catalog_detail .offers_table td.one_click_buy,
  .catalog_detail table.colored td.like_icons,
  .catalog_detail .offers_table td {
    display: block;
    text-align: center;
    width: 100%;
  }
  .catalog_detail .offers_table .opener.top,
  .catalog_detail .offers_table thead {
    display: none;
  }
  .catalog_detail .offers_table,
  .catalog_detail .offers_table tbody,
  .catalog_detail .offers_table tr,
  .catalog_detail .offers_table .opener.bottom {
    display: block;
    width: 100%;
  }
  .catalog_detail table.offers_table td.property.names {
    text-align: center !important;
  }
  .catalog_detail table.offers_table td.price {
    width: 100%;
  }
  .catalog_detail table.offers_table tr.main_item_wrapper {
    width: 100%;
  }
  .catalog_detail table.offers_table tr.offer_stores {
    margin-bottom: 10px;
  }
  .catalog_detail table.offers_table td.like_icons {
    display: block;
    width: 100%;
  }

  .catalog_detail .props_block .char_value {
    font-size: 14px;
    line-height: 18px;
  }

  table.offers_table td.counter_wrapp.counter_block_wr .total_summ {
    position: static;
    margin-bottom: 0px;
  }
  .bx_item_list_you_looked_horizontal .bx_catalog_item .bx_stick {
    left: -16px;
  }

  .col-md-6.share {
    position: absolute;
    right: 0px;
    z-index: 1;
  }
  .share .line_block .share_wrapp {
    min-height: 42px;
  }
  .share .line_block .share_wrapp:not(:hover) {
    padding-left: 0;
    padding-right: 44px;
  }
  .share .line_block .share_wrapp:hover {
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  }
  .share .line_block .share_wrapp .text {
    display: none;
  }

  .wrapper_inner .catalog_detail table.offers_table tr.offer_stores td .stores_block_wrap {
    border-bottom: 1px solid #e7e7e7;
    padding-bottom: 8px;
    padding-bottom: 17px;
    margin-bottom: 18px;
  }

  .slider_navigation.compare .flex-direction-nav {
    display: block !important;
  }
  .wrapper_inner .catalog_detail .adaptive_extended_info .article {
    float: none;
    padding: 10px;
  }
  .wrapper_inner .catalog_detail .adaptive_extended_info .brand {
    float: none;
    padding: 10px;
    text-align: center;
  }
  .specials_slider_wrapp ul.tabs {
    padding-right: 0px;
  }
  .popup {
    min-width: 250px;
  }
  .popup .prompt {
    font-size: 11px;
  }
  .front_slider .item-title a,
  .front_slider .item-title a span {
    line-height: 17px;
  }
  .front_slider li .image img {
    max-width: 125px;
  }
  .item_wrapp img {
    width: 100%;
  }
  .wrapper_inner .catalog_detail .wrapp_docs {
    width: 100%;
  }
  .stores_block_wrap .stores_block .stores_text_wrapp .main_info {
    max-width: 300px;
  }
  .stores_block_wrap .stores_block .stores_text_wrapp .main_info > span {
    display: block;
    padding: 0px;
  }
  body .wrapper_inner .stores_tab .stores_block_wrap .stores_block .item-stock {
    text-align: left;
    padding-left: 23px;
  }

  .breadcrumbs {
    display: block;
    padding-bottom: 5px;
  }
  .module-cart table td.remove-cell {
    padding: 2px;
    width: 10px;
  }
  body .module-cart table td.thumb-cell a {
    height: 40px;
    width: 40px;
  }
  .wrapper_inner .middle .basket_wrapp .module-cart table td.thumb-cell {
    width: 40px;
  }
  .basket_wrapp .module-cart table.bottom.middle td {
    margin-bottom: 2px;
    margin-top: 2px;
    padding: 20px 0 0;
  }
  .basket_wrapp .module-cart table.bottom.middle td.row_titles {
    padding-right: 10px;
  }
  .basket_wrapp .module-cart table.bottom.middle td.row_values {
    padding-left: 10px;
    padding-top: 10px;
    width: 229px;
    margin-top: 20px;
  }
  .basket_wrapp .module-cart table.bottom.middle .bottom_btn td {
    padding: 0 20px 0 0;
    margin-bottom: 18px;
  }
  .basket_wrapp .module-cart table.bottom.middle .bottom_btn td.last_blockk:last-of-type {
    padding-right: 20px !important;
    margin-bottom: 18px !important;
  }
  .basket_wrapp .module-cart table.bottom.middle .bottom_btn td > * {
    float: none !important;
  }
  .bottom.middle .total.item_title {
    margin-top: 10px;
  }

  #basket_form_container .top_control {
    float: none;
    clear: both;
    padding: 15px 0px 0px;
  }
  #basket_form_container .top_control .delete_all {
    display: inline-block;
    float: none;
  }

  ul.tabs li span {
    text-transform: none;
  }
  .catalog_detail .tabs .tab-content {
    padding-top: 0px;
  }

  body .top_slider_wrapp .flexslider .slides > li .wrapper_inner > table {
    background: none !important;
  }
  body #content .left_block + .right_block:not(.catalog) .module-map,
  body .store_map .store_description {
    width: 100%;
    padding: 0;
  }
  body .module_products_list .quantity-cell {
    display: none;
  }
  body .catalog_section_list .section_item {
    width: 100%;
  }
  body .catalog_section_list .section_item_inner {
    margin-right: 0;
    width: 100%;
    overflow: hidden;
  }
  body .module-cart .summ-cell,
  body .module-cart .summ-th,
  body .module-cart .sum-th,
  body .module-cart .name-cell,
  body .module-cart .name-th,
  .print body .module-cart tfoot .delay-cell,
  .print body .module-cart .order_item_props,
  .print body .module-cart .order_item_price_type,
  .print body .module-cart .order_item_weight {
    display: table-cell;
  }
  body .module-cart .name-cell,
  body .module-cart .name-th,
  body .module-cart tfoot .delay-cell,
  body .module-cart .order_item_props,
  body .module-cart .order_item_price_type,
  body .module-cart .order_item_weight {
    display: none;
  }
  body .module-cart .colored tfoot td.extended-cell {
    display: table-cell;
  }
  body .contacts_left,
  body .contacts_right {
    float: none;
    margin: 0;
    position: relative;
    width: 100%;
    padding: 0px;
  }
  body .contacts_left {
    margin-top: 39px;
  }
  body .contacts_left .store_description .store_property {
    width: 50%;
    float: left;
    margin-bottom: 20px;
    padding-right: 20px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
  }
  body .contacts_left .store_description .store_property:nth-child(3) {
    clear: both;
  }
  body .contacts_left .store_description .store_property {
    margin-top: 0;
  }
  body .contacts_right blockquote {
    margin-top: 14px;
    clear: both;
  }
  .wraps > .wrapper_inner {
    padding-top: 0px;
    padding-bottom: 20px;
  }
  .wraps > .wrapper_inner.front {
    padding-top: 24px;
  }
  body .top-h-row ul.menu {
    display: none;
  }
  body .front_slider .preview_text {
    text-overflow: ellipsis;
    text-overflow: -o-ellipsis-lastline;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  body .front_slider_wrapp a.read_more,
  body .btn_big {
    font-size: 12px;
    line-height: 28px;
    height: 27px;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    border-bottom-width: 1px;
  }
  body .front_slider .read_more:hover {
    border-bottom-width: 1px !important;
  }
  body .top_slider_wrapp .banner_text {
    text-overflow: ellipsis;
    text-overflow: -o-ellipsis-lastline;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  body .front_slider .price {
    font-size: 21px;
  }
  /*
  body .top_slider_wrapp .flex-direction-nav li {
    top: 100px;
  }
  body .top_slider_wrapp .flex-direction-nav li,
  body .top_slider_wrapp .flex-direction-nav li a {
    height: 30px;
    width: 30px;
  }
  body .top_slider_wrapp .flexslider .slides li td.text .banner_text,
  body .top_slider_wrapp .flexslider .slides li td.text .banner_title,
  body .top_slider_wrapp .flexslider .slides li td.text .banner_buttons,
  .flexslider .left .text .section {
    margin-left: 10px;
    margin-right: 10px;
  }
  */
  body .index_bottom .banners_column {
    display: none;
  }
  body .index_bottom .info_column,
  body .index_bottom .info_column .about_column {
    padding-left: 0;
  }
  body.news_slider_wrapp .flex-control-nav.flex-control-paging {
    display: block;
  }
  body .index_bottom .info_column .news_column .news_slider_navigation {
    display: none;
  }
  body .specials_slider_wrapp ul.slider_navigation {
    display: none;
  }
  body #footer ul.bottom_main_menu,
  body #footer ul.bottom_submenu {
    width: 100%;
    text-align: justify;
  }
  body .top-h-row .search {
    width: 30%;
  }
  body .wrapper.head_type_2 .top-h-row .search {
    display: block;
  }
  #content .right_block.catalog {
    margin-top: 35px;
  }
  .wrapper_inner #content .right_block .inner_wrapper {
    right: 0px;
    padding: 0px;
  }

  body .wrapper.basket_fly .basket_normal {
    display: block;
  }
  .basket_fly .wrapper_inner .basket_wrapp .wrapp_all_icons {
    width: 207px;
  }
  .basket_fly #header .middle-h-row .basket_wrapp {
    vertical-align: middle;
    padding-left: 4px;
  }
  .basket_fly .main-nav .search_middle_block {
    display: none;
  }
  .wrapper.basket_fly:not(.has_menu) .top-h-row .form_mobile_block .search_middle_block {
    display: block;
  }

  body #content .left_block:not(.catalog) {
    width: 100%;
    float: none;
  }
  body #content .left_block.catalog {
    width: 100%;
    margin: 0px;
  }

  /*filter*/
  .filter_exists .filter-panel .filter-compact.filter-panel__filter {
    display: block;
  }
  .filter-panel__filter .controls-hr {
    display: none;
  }

  .show-normal-sort .filter-panel__filter .controls-hr {
    display: inline-block;
  }
  .show-normal-sort .filter-panel__sort {
    display: block !important;
  }
  .filter-panel__view.controls-view {
    display: none;
  }
  /**/

  .bx_filter.bx_filter_vertical {
    position: absolute;
    margin: 0;
    width: 100%;
    top: 25px;
    box-shadow: 0 0px 10px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 0px 10px rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: 0 0px 10px rgba(0, 0, 0, 0.15);
    z-index: 101;
  }
  .left_block .bx_filter .bx_filter_section {
    margin: 0px;
  }
  body .bx_filter_container #modef {
    top: 4px;
    right: 3px;
  }
  body .bx_filter_vertical .filter_button {
    float: none;
  }

  .bx_filter_vertical input[type="checkbox"] + label.nab:not(.sku) {
    padding: 5px 0px 5px 3px;
    margin: 0px;
  }
  .filter_horizontal {
    overflow: visible;
  }
  body
    .bx_filter_vertical
    .bx_filter_section
    .bx_filter_button_box.active
    .bx_filter_block
    .bx_filter_parameters_box_container {
    width: 100%;
    text-align: left;
  }
  .wrapp_all_inputs.wrap_md .wrapp_change_inputs {
    width: 35%;
    position: relative;
    margin: 0px 0px 4px;
  }
  .wrapp_all_inputs.wrap_md .wrapp_slider {
    width: 65%;
    padding: 10px 0px 0px 20px;
  }
  .smartfilter .bx_ui_slider_track {
    margin-top: 16px;
  }
  .bx_filter .bx_filter_block .bx_filter_popup_result.right#modef_mobile {
    left: 0px;
    background: transparent;
    position: relative;
    color: #888;
    margin: 7px 0 0 2px;
    padding-left: 0px;
    line-height: 20px;
    display: inline-block;
    visibility: visible;
    vertical-align: middle;
  }
  .bx_filter .bx_filter_popup_result.right a,
  .bx_filter_container_modef {
    display: none;
  }

  .filter_horizontal .bx_filter .bx_filter_parameters_box #modef_mobile {
    display: none !important;
  }
  .filter_horizontal {
    padding: 0px;
  }

  div[id^="smartFilterDropDown"] {
    max-width: 690px;
  }

  body .bx_filter_vertical .filter_button.show {
    margin-right: 5px;
  }
  body #content .left_block.catalog > div,
  .bx_filter.bx_filter_vertical {
    display: none !important;
  }
  body .visible_mobile_filter .bx_filter.bx_filter_vertical {
    display: block;
    top: -17px !important;
  }
  body #content .left_block .left_menu li {
    display: inline-block;
    margin-top: 3px;
  }
  body #content .right_block,
  body #content .right_block.catalog {
    padding-left: 0;
  }
  body #content .left_block + .right_block:not(.catalog) {
    margin-top: 20px;
  }
  body .right_side .ask_small_block {
    margin-bottom: 30px;
  }
  body .right_side {
    width: 100%;
    padding-left: 0;
  }
  body .right_side.sections_list {
    display: none;
  }
  body .left_side {
    padding-right: 0;
  }
  body ul.left_menu > li.exit {
    margin-top: 0;
    padding-top: 0;
    border-top: 0;
  }
  body .module-order-history ul.tabs li .triangle {
    margin-top: 0px;
  }
  body .module-order-history ul.tabs li span {
    padding-top: 5px;
  }

  .wrapper_inner .bottom.middle td > .coupon #COUPON {
    margin-bottom: 12px;
  }
  .wrapper_inner .bottom.middle td > .coupon .apply-button {
    display: block;
    clear: both;
  }

  .bx_ordercart_order_sum td.custom_t1 {
    width: 80%;
  }
  .module-order-history .drop-cell .result-row a.button {
    margin: 0px 10px 10px 0px;
  }
  .articles-list.lists_block.faq .item .left-data {
    float: none;
    width: 100%;
    max-width: initial !important;
  }
  .articles-list.lists_block.faq .right-data {
    margin: 20px 0 0;
    padding: 0 0 2px;
    width: 100%;
  }
  .articles-list.lists_block.faq .right-data .preview-text {
    padding-right: 20px;
  }
  #content .ask_big_block .ask_btn_block {
    float: none;
  }
  #content .ask_big_block .description {
    padding: 20px 0px 0px;
  }
  .job.border_block .wrap_md .text {
    width: 50%;
  }
  .job.border_block .wrap_md .phone {
    width: 25%;
  }
  .job.border_block .wrap_md .but {
    width: 25%;
  }
  .basket_normal .popup.card_popup_frame .basket_popup_wrapper .basket_popup_wrapp {
    top: 41px;
  }
  .news_block .info_block .news_items .item {
    width: 100%;
    padding-left: 0px;
  }
  .wrapper_inner .footer_top .wrap_md .phones {
    padding-left: 0px;
  }
  .wrapper_inner .footer_bottom .menu_block,
  .wrapper_inner .footer_bottom .social_block {
    width: 100%;
  }
  .footer_bottom .social_block .social_wrapper {
    padding-left: 0px;
  }

  .wrapper_inner .soc-avt .row input[type="text"] {
    width: 90%;
  }
  body #header .wrapper_inner ul.menu li .child .child_wrapp {
    padding: 25px 20px 17px;
  }
  .wrapper_inner .articles-list.sections .item {
    width: 100%;
  }
  .wrapper_inner .module-order-history .module-orders-list .drop-cell .not-payed {
    padding: 5px 7px;
  }
  .rss_feed_icon + .filter_block,
  .rss_feed_icon + .news_detail_wrapp {
    margin: 35px 0px 0px;
  }

  .form-control.captcha-row {
    display: flex;
    flex-direction: column;
  }
  .form-control.captcha-row .captcha_image {
    order: 2;
    position: relative !important;
    left: 0 !important;
    margin-top: 10px;
  }
  .pk-page .form-control.captcha-row {
    display: block;
  }
  .pk-page .form-control.captcha-row .captcha_image {
    position: absolute !important;
    left: initial !important;
    margin-top: 0;
  }

  .start_promo .item i.title span,
  .wrapper_inner .wrap_tizer .wr_block .title .inner_text {
    font-size: 12px;
  }
  .start_promo .item i.price span,
  .wrapper_inner .wrap_tizer .wr_block.price .inner_text {
    font-size: 14px;
  }

  .basket_wrapp > div > a {
    display: block;
  }
  #header .basket_wrapp .basket_sort,
  #header .middle-h-row form.basket_wrapp {
    display: none;
  }
  body #header .basket_fly {
    width: auto;
    background: none;
    right: 0px !important;
  }

  .bx_filter_vertical.bx_filter .hidden_values {
    display: none;
  }
  .bx_filter_vertical.bx_filter .inner_expand_text {
    float: none;
    clear: both;
  }
  .bx_filter_vertical.bx_filter .expand_block {
    display: inline-block;
    font-size: 12px;
    margin: 5px 0px 0px;
    border-bottom: 1px dotted;
    cursor: pointer;
    line-height: 16px;
  }
  .bx_filter_vertical .bx_filter_block.limited_block {
    max-height: none;
    overflow-y: visible;
  }
  body .bx_filter_vertical .bx_filter_block.limited_block {
    max-height: 250px;
  }
  body
    .bx_filter.bx_filter_vertical
    .bx_filter_parameters_box_title
    + .bx_filter_block.limited_block
    .bx_filter_parameters_box_container {
    max-height: none;
  }
  .wrapper_inner .wrap_tizer {
    text-align: left;
  }
  .wrapper_inner .start_promo .wrap_tizer {
    left: 0px;
  }

  .list-type-block.item-views > .row > div:last-of-type > hr {
    margin-bottom: 25px;
  }
  .bottom_nav {
    padding-bottom: 5px;
  }

  /*map*/
  .wrapper_block.with_title + .contacts_map_list {
    padding-left: 0px;
    padding-right: 0px;
  }
  body .wrapper_block.with_title .block_container,
  body .wrapper_block.with_title .block_container .detail_items {
    position: static;
    width: 100%;
  }
  body .wrapper_block.with_title .block_container,
  .with_title .block_container .detail_items,
  .with_title .block_container .items {
    height: auto;
    max-height: 300px;
  }
  body .wrapper_block.with_title .block_container {
    border-right-width: 1px;
    border-bottom-width: 0px;
  }

  footer .footer_inner {
    text-align: center;
  }
  footer .bottom-under .inner-wrapper > div {
    float: none !important;
    display: block;
    text-align: center;
    margin: 0px 0px 20px;
  }
  footer .bottom-under .inner-wrapper .copy-block > div {
    padding: 0px;
    display: block;
  }
  footer .bottom-under .inner-wrapper .copy-block .copy,
  footer .print-link {
    padding-bottom: 20px;
  }

  #bx-composite-banner {
    text-align: center;
  }
  #bx-composite-banner a.bx-composite-btn {
    /* margin-bottom: 20px; */
  }

  footer .info .blocks {
    display: inline-block;
    margin-left: 20px;
    margin-right: 20px;
  }
  #footer .footer_bottom_inner .phones,
  #footer .footer_bottom_inner .social_wrapper {
    float: none;
    display: block;
  }
  #footer .footer_bottom_inner .phones .phone_block {
    display: inline-block;
  }
  #footer .soc .social {
    padding-left: 0px;
  }
  #footer .soc .social-icons {
    text-align: center;
  }

  /*basket2*/
  #basket-root .basket-items-list-item-container .basket-items-list-item-descriptions {
    padding-bottom: 10px;
  }
  #basket-root .basket-item-block-price {
    padding-top: 9px;
  }
  #basket-root .basket-items-list-item-amount {
    padding-top: 5px;
  }
  .basket-items-search-field + .basket-items-list-header-filter {
    padding: 10px 0px 0px;
    text-align: center;
  }
  .basket-items-search-field + .basket-items-list-header-filter > a {
    white-space: nowrap;
    display: inline-block;
    margin-bottom: 12px;
  }

  /* ORDER */
  #bx-soa-order .bx-soa-pp-desc-container .bx-soa-pp-company-graf-container {
    float: left;
  }
  #bx-soa-order .bx-soa-pp-desc-container .bx-soa-pp-company .bx-soa-pp-company-desc {
    margin-top: 0;
    padding-left: 140px;
  }
  #bx-soa-order .bx-soa-pp-desc-container {
    margin-top: 20px;
    padding-left: 0;
    padding-right: 0;
  }
  #bx-soa-order .bx-soa-pp-list-termin {
    width: auto;
  }
  #bx-soa-order .bx-soa-section .bx-soa-section-title-container .col-sm-9 {
    float: left;
    width: 75%;
  }
  #bx-soa-order .bx-soa-section .bx-soa-section-title-container .col-sm-3 {
    float: left;
    width: 25%;
  }
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-tr .bx-soa-item-td.bx-soa-item-properties {
    width: 50% !important;
    display: inline-block;
    vertical-align: top;
    float: none;
  }
  .bx-soa-item-nth-4p1 {
    display: none;
  }

  /*personal*/
  .sale-order-detail-about-order-inner-container-repeat {
    margin: 0px;
  }
  .personal_wrapper .orders_wrapper .sale-order-detail-payment-options-shipment-composition-map {
    width: 100%;
  }
  .personal_wrapper .orders_wrapper .sale-order-detail-total-payment-container {
    width: 100%;
  }
  .personal_wrapper .col-xs-6 {
    width: 50%;
  }

  header .wrap_menu {
    height: auto;
    padding: 0px;
  }
  header .menu_top_block,
  #header .middle-h-row .center_block {
    display: none;
  }
  .menu.adaptive {
    display: block;
  }
  #header .catalog_menu .wrapper_middle_menu .inc_menu {
    padding-left: 0px;
  }
  header .menu_top_block > li,
  .top-h-row .menu.topest > li {
    float: left;
  }
  .catalog_section_list .section_item .image {
    width: 90px;
  }
  .catalog_section_list .section_item .image img {
    max-width: 100%;
    max-height: 100%;
  }

  .display_list .item .list_item > tbody > tr > td {
    display: block;
    width: 100%;
    text-align: center;
    margin: auto;
  }
  .display_list .item .list_item > tbody > tr > td table {
    margin: auto;
  }
  .display_list .item .list_item td .image_wrapper_block {
    margin: 0px auto;
  }
  .display_list .information_wrapp .information {
    padding-right: 0px;
    padding-left: 0px;
  }

  .item-views.list.image_right .item .image {
    padding-top: 0;
  }
  .item-views.list .item .image {
    margin: auto;
  }

  .contacts-page-map + .contacts.contacts-page-overmap .contacts-wrapper {
    margin: 0px 0px -20px;
    box-shadow: none;
  }
  .contacts.contacts-page-overmap table {
    border: none;
  }
  .contacts .ik_select {
    margin-bottom: 20px;
  }

  .contacts-page-map + .contacts.contacts-page-map-overlay {
    margin: 0px;
  }
  .form .form_left,
  .form .form_right {
    float: none;
    width: 100%;
    position: static;
    padding: 0px;
  }
  .share .catalog_detail .share_wrapp {
    margin: 0px 0px 20px;
  }

  .container .page_not_found td.image,
  .container .page_not_found td.description {
    display: block;
    width: 100%;
  }
  .container .page_not_found td.description {
    padding-top: 40px;
  }

  /*basket*/
  .bx_ordercart .bx_sort_container {
    margin-bottom: 20px;
  }
  .bx_ordercart .bx_ordercart_order_table_container {
    border-top: none;
  }
  .bx_ordercart .bx_ordercart_order_table_container table thead {
    display: none;
  }
  .bx_ordercart .bx_ordercart_order_table_container > table {
    display: block;
  }
  .bx_ordercart .bx_ordercart_order_table_container > table > tbody {
    display: block;
  }
  .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr {
    display: block;
    position: relative;
    display: block;
    padding: 29px 20px 20px 145px;
    border-top: 1px solid #f3f3f3;
  }
  .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr > td {
    display: block;
    padding: 0;
    border: none;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.custom {
    padding: 0;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.custom span {
    display: block;
    font-size: 12px;
    line-height: 20px;
    color: #999;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.item {
    width: auto;
    padding-top: 0;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.item .bx_ordercart_itemtitle {
    padding-right: 79px;
    text-align: left;
  }
  .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr:after {
    content: "";
    display: table;
    clear: both;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.itemphoto {
    float: left;
    margin: -9px 0 0 -125px;
    padding: 0;
    width: 90px;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.price {
    padding-top: 1px;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.price + td.custom > span {
    display: none;
  }
  .bx_ordercart .bx_ordercart_order_table_container tbody td.control {
    position: static;
    padding: 0;
  }
  .bx_ordercart .bx_ordercart_order_table_container table.counter {
    margin-top: -6px;
  }
  .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr > td:not(.itemphoto):not(.margin):not(.item) {
    width: 25%;
    margin: 10px -3px 0 0;
    display: inline-block;
    vertical-align: top;
  }
  .bx_ordercart tbody td.control a:last-of-type {
    position: absolute;
    right: 40px;
    top: 20px;
  }
  .bx_ordercart #basket_items_delayed tbody td.control a:first-of-type {
    position: absolute;
    right: 40px;
    top: 20px;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.custom span {
    display: inline-block;
    vertical-align: middle;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.custom > div {
    display: inline-block;
    vertical-align: middle;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.custom > div[id^="sum_"] {
    display: block;
  }
  .bx_ordercart #basket_items .custom div[id^="discount_value"] {
    margin-top: -3px;
  }
  .bx_ordercart .bx_ordercart_order_table_container tbody td.custom .centered {
    margin-top: 0;
  }

  .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr > td:not(.item):not(.itemphoto):not(.margin) {
    width: 50%;
  }
  .bx_ordercart .bx_ordercart_order_table_container table.counter {
    margin-top: 12px;
  }
  .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr > td.price {
    margin-top: 20px !important;
  }
  .bx_ordercart .bx_ordercart_order_pay > div {
    width: 100%;
    float: none;
  }
  .bx_ordercart_order_pay_right {
    margin-top: 0;
  }
  .bx-touch .bx_ordercart .bx_sort_container a {
    width: 50%;
    margin-bottom: -1px;
  }
  #basket-root .basket-checkout-section-inner .basket-checkout-block-btn,
  #basket-root .basket-checkout-section-inner .fastorder {
    display: block;
    width: 50%;
    text-align: left;
  }
  #basket-root .basket-checkout-block-total-price-inner {
    padding-bottom: 15px;
  }

  /*personal*/
  .bx-sap .sale-acountpay-pp {
    max-width: 100%;
  }
  .sale-order-payment-change-pp,
  .sale-order-payment-change-pp-item-container {
    max-width: 100%;
  }
  .personal_wrapper .orders_wrapper .hidden-xs {
    display: none !important;
  }
  .personal_wrapper .orders_wrapper .visible-xs {
    display: block !important;
  }

  /*video banner*/
  /*
  .top_slider_wrapp .box .video + .wrapper_inner {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
  }
  .top_slider_wrapp .box.wvideo .video + .wrapper_inner tr.main_info {
    opacity: 0;
  }
  .top_slider_wrapp .box.wvideo .video + .wrapper_inner tr.adaptive_info {
    opacity: 0;
  }
*/
  .bx_filter.compact .bx_filter_parameters_box_title .delete_filter {
    display: none;
  }
  .bx_filter.bx_filter_vertical.compact {
    top: 34px;
  }
  .bx_filter.compact .bx_filter_parameters_box .bx_filter_button_box {
    display: none !important;
  }
  .bx_filter.compact .bx_filter_section {
    margin-bottom: 0;
  }
  .bx_filter.compact .bx_filter_input_checkbox .bx_filter_param_text {
    margin-left: 0;
  }
  .bx_filter.compact .filter.label_block input[type="checkbox"] + label:before,
  .bx_filter.compact .filter.label_block input[type="checkbox"] + label:after {
    top: 0;
  }
  .bx_filter.compact .bx_filter_parameters_box.set .bx_filter_parameters_box_title:not(.prices) {
    border: none !important;
    color: #333 !important;
    color: var(--white_text_black) !important;
  }

  .bx_filter.compact .bx_filter_parameters_box_title + .bx_filter_block .bx_filter_parameters_box_container {
    margin-top: 15px;
  }
  .bx_filter.compact .bx_filter_parameters_box.prices .bx_filter_block {
    margin-top: 15px;
  }
  .bx_filter.compact .bx_filter_parameters_box.prices .price_block {
    margin-top: 15px;
  }
  .bx_filter.compact .bx_filter_parameters_box.prices .price_block:first-of-type {
    margin-top: 0;
  }
  .bx_filter.compact .wrapp_all_inputs.wrap_md .wrapp_slider {
    padding-top: 0;
  }
  .bx_filter.compact .bx_filter_button_box.hidden {
    display: block !important;
  }
  .bx_filter.compact .bx_filter_button_box.hidden .bx_filter_block {
    display: block !important;
    opacity: 1 !important;
  }
  .bx_filter.compact .bx_filter_button_box.hidden .bx_filter_block .btn {
    visibility: visible;
  }
  .bx_filter.compact .props_list .hint {
    position: relative;
    right: auto;
  }
  .bx_filter.compact .props_list .hint .icon {
    position: static;
  }
  .bx_filter.compact .props_list .hint .text {
    font-size: 13px;
    padding-left: 8px;
  }
  .bx_filter.compact .props_list .hint .tooltip {
    opacity: 1;
  }
  .bx_filter.compact .hint .tooltip {
    left: -19px;
  }
  .bx_filter.compact .bx_filter_parameters_box .bx_filter_button_box {
    display: none !important;
  }

  .bx_filter .bx_filter_parameters_box_title + .bx_filter_block .bx_filter_parameters_box_container {
    max-height: 250px;
  }

  /*catalog*/
  .fast_view_button {
    display: none;
  }

  /*table catalog*/
  .table-view .item-actions {
    width: 40%;
    flex-direction: column;
  }
  .table-view .table-view__item-wrapper .item-actions > div {
    width: 100%;
    max-width: none;
    padding: 8px 0px 0px;
  }
  .table-view .table-view__item-wrapper .item-actions > div:first-of-type {
    margin-top: -8px;
  }
  .table-view .table-view__item-wrapper .item-icons {
    align-self: normal;
  }

  body #content .wrapper_inner .contents_page .right_block.maxwidth-theme {
    padding-left: 16px;
  }

  .bigdata_recommended_products_items .block-items.flexbox:not(.owl-grab) .owl-dots {
    border-left: 1px solid #fff;
    border-color: var(--black_bg_black);
    padding-top: 6px;
  }

  .wrapper_inner.wide_page .banners_slider_wrap.CONTENT_TOP,
  .wrapper_inner.wide_page
    .banners_slider_wrap.CONTENT_BOTTOM
    .front_page:not(.with_left_block)
    .banners_slider_wrap.CONTENT_TOP,
  .front_page:not(.with_left_block) .banners_slider_wrap.CONTENT_BOTTOM,
  .front_page.with_left_block .banners_slider_wrap.CONTENT_TOP,
  .front_page.with_left_block .banners_slider_wrap.CONTENT_BOTTOM {
    padding-left: 16px;
    padding-right: 16px;
  }
}

@media all and (max-width: 716px) {
  .wrapper_inner .staff.list .item .info {
    margin: 10px 0px 0px 0px;
  }
  .stores_block_wrap .stores_block .stores_text_wrapp.image_block .main_info > span {
    max-width: 200px;
  }
  .rows_block .block_list .item_block {
    width: 50%;
  }
}

@media all and (max-width: 710px) {
  #mobileheader .mobileheader-v2 .right-icons .wrap_basket .basket-link:not(.basket) {
    display: none;
  }
  #mobileheader .mobileheader-v2 .right-icons .wrap_basket .basket-link.basket {
    padding-left: 15px;
  }
}

@media all and (max-width: 650px) {
  .footer_top .sblock .wrap_icon {
    display: none;
  }
  .footer_top .sblock .forms .email_wrap {
    width: 72%;
  }
  .footer_top .wrap_md .phones .order {
    padding: 0px 0px 0px 20px;
  }
  .sort_header .sort_filter a .icon,
  .sort_header.view_table .sort_filter a .icon {
    display: inline-block;
  }
  body .top-h-row .phone .icon {
    display: none;
  }
  body .top-h-row .phone {
    margin-top: 11px;
  }
  body .module-orders-list .order-extra-properties {
    display: inline;
  }
  .filter_block ul {
    float: none;
  }
  .filter_year {
    float: none;
    margin: 5px 0 5px;
  }
  body .module-orders-list.colored thead td,
  body .module-orders-list td.date-cell,
  body .module-orders-list td.count-cell,
  body .module-orders-list td.price-cell,
  body .module-orders-list td.pay-status-cell,
  body .module-orders-list td.order-status-cell,
  body .module-orders-list .drop-container th.price-th,
  body .module-orders-list .drop-container th.count-th,
  body .module-order-history.orderdetail .module-orders-list.goods td {
    display: none;
  }
  body .module-order-history.orderdetail .module-orders-list.goods td.vname,
  body .module-order-history.orderdetail .module-orders-list.goods td.price,
  body .module-order-history.orderdetail .module-orders-list.goods td.vqnt {
    display: table-cell;
  }
  .module-order-history .drop-cell .result-row a.button22 {
    margin-top: 5px;
  }
  body .sort_header .sort_filter a span {
    display: none;
  }
  .basket_sort ul.tabs li span {
    font-size: 14px;
  }

  body .module-cart .summ-cell,
  body .module-cart .summ-th,
  body .module-cart .sum-th {
    display: none;
  }
  .basket_wrapp .module-cart table.bottom.middle td.row_values {
    width: 160px;
    margin-top: 21px;
  }
  .wrapper_inner .middle .basket_wrapp .module-cart table.colored tr td {
    padding: 4px;
  }
  body .module-cart table td.thumb-cell a {
    line-height: 40px;
  }
  .wrapper_inner .middle .basket_wrapp .module-cart table tr td.count-cell {
    padding-top: 16px;
  }
  .wrapper_inner .basket_wrapp .module-cart table.bottom.middle td.row_values .item_title {
    float: left;
    margin: 0 10px 0 0;
    font-weight: bold;
  }
  .wrapper_inner .basket_wrapp .module-cart table.bottom.middle td.row_titles .item_title {
    display: none !important;
  }
  .wrapper_inner .basket_wrapp .module-cart table.bottom.middle td.row_values .wrap_prices {
    float: left;
  }
  .bottom.middle .total.item_title {
    display: none;
  }
  body .module-cart .cost-cell,
  body .module-cart .summ-cell,
  body .module-cart .summ-cell *,
  body .module-cart .row_values .price {
    font-size: 14px;
  }
  .wrapper_inner .phones .order_wrap_btn {
    margin-left: 5px;
  }
  .wrapper_inner .top-h-row .h-user-block .module-enter,
  .wrapper_inner .phones > span .callback_btn {
    font-size: 11px;
  }
  .wrapper_inner .footer_top .sblock,
  .wrapper_inner .footer_top .wrap_md .phones {
    width: 100%;
  }
  .footer_top .wrap_md .phones .phone_block {
    padding-left: 0px;
  }
  body .authorization-cols .col.authorization,
  body .authorization-cols .col.registration {
    width: 100%;
    padding-right: 0;
  }
  .wrapper.has_menu #header .wrapper_inner .middle-h-row .center_block .search {
    display: none;
  }
  .wrapper.has_menu #header .middle-h-row .center_block {
    padding: 0 20px 0 0;
  }
  .wrapper.has_menu .top-h-row .form_mobile_block .search_middle_block {
    display: block;
  }
  .wrapper_inner .forms .text_block,
  .wrapper_inner .forms .form_block {
    width: 100%;
    padding: 0px;
  }
  .basket_print {
    display: inline-block;
  }
  .wrapper_inner .basket_wrapp .module-cart table.bottom.middle .bottom_btn .basket_checkout_wrapp {
    text-align: left;
  }
  .basket_wrapp .module-cart table.bottom.middle .bottom_btn td.last_blockk {
    float: left !important;
  }
  .bx_order_make .bx_block.r1x3 {
    width: 35%;
  }
  .bx_order_make .bx_block.r3x1 {
    width: 65%;
  }
  .job.border_block .wrap_md .text {
    width: 70%;
  }
  .job.border_block .wrap_md .phone {
    width: 30%;
  }
  .job.border_block .wrap_md .but {
    width: 100%;
    display: block;
    text-align: left;
    padding: 20px 0px 0px 0px;
  }
  .wrapper_inner .stores .stores_list {
    width: 51%;
  }
  .authorization-cols .form-block {
    height: auto !important;
  }
  #mobileheader .right-icons .wrap_basket .basket-link:not(.basket) {
    display: none;
  }
  #mobileheader .basket-link {
    padding-left: 15px;
  }

  /*.catalog_section_list .item_block{width:100%;}*/
  .sort_header .sort_filter a i.arr {
    width: 11px;
  }
  .sort_header .sort_filter a i.arr:after {
    top: 0px;
    left: 1px;
    font-size: 14px;
  }
}

@media all and (max-width: 630px) {
  .wrapper_inner .catalog_detail .tabs_content .char,
  .wrapper_inner .catalog_detail .tabs_content .serv {
    width: 100%;
    padding: 0px !important;
  }
  .catalog_detail .tabs_content .descr_div .char_block {
    width: 100%;
    padding-right: 0;
  }
  .module-order-history .module-orders-list td.drop-cell .item-shell td:first-child {
    padding-left: 5px;
  }
  .module-order-history .drop-cell .result-row {
    padding-left: 10px;
    padding-right: 10px;
  }
  .popup.show .popup-intro .pop-up-title {
    font-size: 20px;
    line-height: 18px;
  }

  .bx_compare .bx_sort_container .wrap_remove_button {
    position: static;
    padding: 10px 0px;
    display: block;
  }
  .bx_compare .bx_sort_container ul.tabs-head > li {
    display: block;
    float: none;
    text-align: center;
    margin: 0px;
  }
}

@media (min-width: 601px) and (max-width: 767px) {
  .catalog_section_list .item_block:not(.slide):not(.sm):not(.lg) .section_item tr td {
    display: block;
  }
  .catalog_section_list .item_block:not(.slide):not(.sm):not(.lg) .section_item td.image {
    padding-bottom: 20px;
    margin: auto;
    padding-right: 0;
  }
}

@media all and (min-width: 601px) {
  .detail.staff .detailimage {
    float: left;
    max-width: 200px;
  }
  .detail.staff .detailimage + .post-content {
    padding-left: 220px;
  }
  .top_mobile_region .confirm_region {
    padding-top: 12px;
  }
  .top_mobile_region .confirm_region .title {
    display: inline-block;
    padding-right: 30px;
  }
  .top_mobile_region .confirm_region .buttons {
    display: inline-block;
    padding-top: 11px;
  }
  .top_mobile_region .confirm_region .buttons > span {
    width: auto;
  }
  .top_mobile_region .confirm_region .buttons > span .btn {
    padding-left: 20px;
    padding-right: 20px;
  }
  body .top_mobile_region .confirm_region + .close_popup {
    top: 35px;
  }

  .wrapper1 .ajax_load .display_list,
  .wrapper1 .table-view {
    display: block;
  }

  .top-content-block .item-views.tizers .item-wrapper > .item .pull-left + .inner-text {
    padding-left: 26px;
  }

  .catalog_block .catalog_item.big .item_info--left_block {
    max-width: 65%;
    padding-right: 20px;
  }
  .catalog_block .catalog_item.big .cost.prices {
    margin-top: 0;
  }
  .catalog_block .catalog_item.big .top_info {
    margin-bottom: 0;
  }
  .catalog_block .catalog_item.big .top_info .item-title {
    margin-top: 6px;
  }
  .catalog_block .catalog_item.big > div .item_info {
    margin-top: 25px;
  }
  .catalog_item.big .image_wrapper_block {
    margin-bottom: 0px;
  }
  html.bx-mac .catalog_item .view_sale_block.v2 {
    display: block;
  }

  .top_big_banners .visible_side_mobile {
    display: none;
  }
}

@media all and (max-width: 600px) {
  /*catalog compact list*/
  .compact-catalog .ajax_load .item .catalog_item {
    height: 100%;
  }
  .compact-catalog .ajax_load .item .catalog_item > div {
    padding: 19px 19px 40px;
    height: 100%;
  }
  .compact-catalog .ajax_load .item .catalog-adaptive {
    padding: 19px 19px 69px;
  }

  .compact-catalog .ajax_load.block .catalog_item .footer_button {
    display: block;
    opacity: 1;
    height: auto;
    visibility: visible;
    margin: 0px;
    position: absolute;
    top: auto;
    bottom: 19px;
    left: 19px;
    right: 19px;
    z-index: 60;
    padding: 0;
    box-shadow: none;
  }

  .compact-catalog .ajax_load .view_sale_block_wrapper,
  .compact-catalog .ajax_load .view_sale_block,
  .compact-catalog .ajax_load .cost.prices .more-item-info,
  .compact-catalog .section-gallery-wrapper__item:not(._active),
  .compact-catalog .section-gallery-wrapper__item-nav,
  .compact-catalog .ajax_load .like_icons .wrapp_one_click,
  .compact-catalog .ajax_load .like_icons .fast_view_button,
  .compact-catalog .ajax_load .has-sku .wish_item_button,
  .compact-catalog .item .stickers,
  .catalog_favorit .fast_view_button {
    display: none !important;
  }
  .compact-catalog .ajax_load .cost.prices .more-item-info + .price_matrix_wrapper,
  .compact-catalog .ajax_load .cost.prices .more-item-info + .js-info-block + .price_matrix_wrapper,
  .compact-catalog .ajax_load .cost .with_matrix.pl .prices-wrapper {
    padding-left: 0px;
  }
  .compact-catalog .ajax_load.block .footer_button .counter_wrapp > .button_block {
    width: 100%;
  }
  .compact-catalog .ajax_load .like_icons > div {
    display: inline-block;
  }
  .compact-catalog .ajax_load .like_icons span {
    opacity: 1;
    visibility: visible;
    margin: 0px;
  }
  .compact-catalog .ajax_load .like_icons span:not(:hover),
  .hot-wrapper-items .like_icons span:not(:hover) {
    box-shadow: 0px 0px 0px 1px #ececec inset;
    box-shadow: 0px 0px 0px 1px var(--stroke_black) inset;
  }
  .compact-catalog .ajax_load .like_icons span.added,
  .hot-wrapper-items .like_icons span.added {
    box-shadow: none;
  }
  .compact-catalog .ajax_load .image_wrapper_block,
  .compact-catalog .ajax_load .image_wrapper_block > a,
  .compact-catalog .table-view .item-foto__picture {
    height: auto;
    line-height: normal;
  }
  .compact-catalog .table-view .item-foto__picture > a img {
    margin: auto;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border: 0;
    vertical-align: middle;
    max-width: 100%;
    max-height: 100%;
    pointer-events: none;
  }
  .display_list .list_item .image_wrapper_block {
    padding-top: 100%;
    width: 100%;
  }
  .display_list .list_item .image_wrapper_block > a {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
  }

  .compact-catalog .catalog_block .catalog_item > div .item_info {
    padding-bottom: 24px;
  }
  .compact-catalog .catalog_block .sa_block {
    line-height: 21px;
  }

  .compact-catalog .ajax_load .sale_block .inner-sale {
    display: none;
  }
  .compact-catalog .ajax_load .cost.prices .price:not(.discount) {
    width: 100%;
  }
  .compact-catalog .ajax_load .cost.prices .price.discount {
    font-size: 11px;
    float: left;
  }
  .compact-catalog .ajax_load .cost.prices .sale_block {
    margin-left: 1px;
  }
  .compact-catalog .wrapper_inner .ajax_load .sale_block .value {
    padding-top: 1px;
    padding-left: 3px;
    font-size: 11px;
  }
  .compact-catalog .ajax_load .cost.prices .price.discount > span {
    font-size: 11px;
  }

  .compact-catalog .ajax_load .cost.prices .more-item-info + .price_matrix_wrapper .prices-wrapper,
  .compact-catalog .ajax_load .cost .with_matrix.pl .prices-wrapper {
    padding-left: 0px;
  }
  .compact-catalog .ajax_load .ajax_load_btn {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
  }
  .compact-catalog .ajax_load .big .absolute-full-block {
    display: none !important;
  }

  .compact-catalog .ajax_load .counter_wrapp .button_block .btn,
  body .wrapper1.compact-catalog .ajax_load .offer_buy_block .btn,
  .compact-catalog .ajax_load .item .catalog-adaptive .counter_wrapp.list .btn {
    padding-top: 8px;
    padding-bottom: 7px;
    padding-left: 5px;
    padding-right: 5px;
    border-radius: 3px;
  }

  .catalog_block.owl-carousel .owl-stage,
  .catalog_block.owl-carousel .owl-stage > div {
    display: flex;
  }

  .filter-panel-wrapper .filter-panel__view.controls-view {
    display: none;
  }

  /*catalog compact front block*/
  .compact-catalog .catalog_block .catalog_item.big > div .item_info {
    position: static;
  }
  .compact-catalog .catalog_block .catalog_item.big > div .item_info .item_info--right_block {
    width: 100%;
  }
  .compact-catalog .catalog_block .catalog_item > div .item_info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  body .compact-catalog .sa_block .article_block,
  body .compact-catalog .sa_block .rating {
    display: none;
  }
  .compact-catalog .ajax_load.block .js-info-block {
    z-index: 61;
  }

  body.detail_page .compact-catalog .item-stock:not(.ce_cmp_visible) {
    display: inline-block;
  }
  body .compact-catalog .sa_block .article_block:empty {
    display: none;
  }
  body .compact-catalog .ce_cmp_hidden {
    display: none;
  }
  body .compact-catalog .catalog_block .item-stock.ce_cmp_hidden {
    display: none;
  }
  body .compact-catalog .ce_cmp_visible {
    display: block;
  }
  body .compact-catalog .ce_cmp_visible > a {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
  }
  .catalog_item.big .image_wrapper_block {
    padding-top: 100%;
  }
  .vertical-catalog-img .catalog_item.big .image_wrapper_block {
    padding-top: 142%;
  }
  body .compact-catalog .catalog_block .item-stock.ce_cmp_visible {
    display: inline-block;
  }

  .basket_wrapp .header-cart {
    display: none;
  }
  body .basket_fly_forms {
    display: none;
  }

  .wrapper1:not(.compact-catalog) .catalog_block .catalog_item.big .footer_button {
    position: absolute;
    height: auto;
    bottom: 0;
    padding: 0;
    left: 24px;
    right: 23px;
    height: 56px;
    top: auto;
    box-shadow: none;
  }
  .wrapper1:not(.compact-catalog) .catalog_block .catalog_item.big {
    padding-bottom: 56px;
  }

  .catalog_block .catalog_item.big.product_image > div .item_info {
    position: static;
    align-items: baseline;
  }
  .catalog_block .catalog_item.big > div .item_info {
    padding-bottom: 29px;
  }
  .catalog_block .catalog_item.big .top_info {
    margin-bottom: 0;
  }
  .catalog_block .catalog_item.big .top_info .rating {
    margin-bottom: 6px;
  }
  .catalog_block .catalog_item.big .top_info .sa_block {
    order: 1;
  }

  .stores_block_wrap .stores_block .stores_text_wrapp.image_block .imgs {
    display: none;
  }
  .stores_block_wrap .stores_block .stores_text_wrapp.image_block .main_info {
    margin: 0px;
  }
  .tizers_block .item:nth-child(n + 5) {
    text-align: center;
  }
  .catalog_detail .info_item .middle_info .prices .price.discount {
    margin-top: 3px;
  }

  .top_big_banners .items .item,
  .top_big_banners > .row > div.col-md-3,
  body .top_big_banners .blocks2 {
    width: 100%;
    float: none;
  }

  .basket_bottom_block {
    display: none;
  }

  .catalog_detail .item_main_info .stickers {
    top: 27px;
    left: 24px;
  }

  .wrapper_inner .start_promo .item,
  .wrapper_inner .start_promo .item.wide50,
  .wrapper_inner .start_promo .item.wide100 {
    width: 50%;
    border: 1px solid #fff;
    padding-bottom: 47%;
  }
  .wrapper_inner .start_promo .item span.wrap_main,
  .wrapper_inner .start_promo .item.wide50 span.wrap_main,
  .wrapper_inner .start_promo .item.wide100 span.wrap_main {
    max-width: 500px;
  }
  .wrapper_inner .start_promo {
    margin: 0px;
  }
  .start_promo .item.normal:nth-child(3n) {
    border-right-width: 1px;
  }
  .start_promo .item.normal:nth-child(3n + 1),
  .start_promo .item.s_2.normal,
  .start_promo .item.normal:nth-child(2n + 1) + .item:not(.s_2) {
    border-left-width: 1px;
  }
  .start_promo .item.s_4.normal,
  .start_promo .item.normal:nth-child(3n + 1).s_4 {
    border-left-width: 1px !important;
  }
  .start_promo.normal_view .item.normal:nth-child(2n + 1) {
    border-left-width: 0px !important;
  }
  .start_promo.normal_view .item.normal:nth-child(2n + 2) {
    border-left-width: 2px !important;
  }
  .wrapper_inner1.wides.float_banners {
    margin: 0px 0px 30px;
  }

  .subscribe-form .wrap_bg .top_block,
  .subscribe-form .wrap_bg .sform {
    width: 100%;
  }
  .subscribe-form .wrap_bg .sform {
    margin-top: 22px;
  }

  .adv_bottom_block {
    display: none;
  }

  .md-50.img {
    display: none;
  }
  .md-50.big {
    width: 100%;
    padding: 31px 0px 37px;
  }

  body .title_position_CENTERED .page-top .topic__heading {
    margin-left: 0px;
    margin-right: 0px;
  }
  body .title_position_CENTERED .page-top {
    text-align: left;
  }
  body .title_position_CENTERED .page-top .share + .topic__heading {
    margin-right: 25px;
  }
  .page-top .share + .topic__heading {
    margin-right: 25px;
  }

  .footer_top .sblock .forms .email_wrap {
    width: 70%;
  }
  .footer_top .wrap_md .phones .phone_wrap a {
    font-size: 15px;
  }

  .center_block .search_block {
    display: none;
  }
  .header_wrap #header .middle-h-row .center_block ul.menu,
  .basket_fly #header .middle-h-row .center_block ul.menu {
    width: 100%;
    padding: 0px;
  }
  .search_middle_block {
    display: none;
  }

  .wrapper.m_color_dark #header .catalog_menu ul.menu > li.current {
    background: #3f3f3f;
  }
  body .header_wrap.white .menu > li.current > a span {
    border-bottom-color: transparent;
  }

  body .top_slider_wrapp .flex-direction-nav li {
    top: 85px;
  }

  body .item_main_info .item_slider:not(.flex) {
    z-index: 2;
  }
  body .item_main_info .item_slider:not(.flex) > div {
    display: none !important;
  }
  body .item_main_info .item_slider > div.like_wrapper {
    display: block !important;
  }
  body .item_main_info .item_slider > div.like_wrapper .like_icons {
    top: 1px;
    right: 0px;
    padding: 20px;
  }

  body .catalog_detail .adaptive_extended_info_wrapp {
    display: block;
  }
  body .item_main_info .item_slider.flex {
    display: block;
  }
  body .container .catalog_detail .item_main_info .item_slider {
    padding: 0 !important;
    width: 100%;
    float: none;
  }
  body .container .catalog_detail .item_main_info .right_info {
    padding: 0 !important;
    border-top: 1px solid #e5e5e5;
  }
  body .catalog_detail .extended_info {
    margin-bottom: 0;
  }
  body .catalog_detail .item_main_info .item_slider .thumbs,
  body .catalog_detail .right_info .info_block,
  body .catalog_detail .right_info hr,
  body .catalog_detail .right_info hr.separator {
    display: none;
  }
  body .catalog_detail .item_main_info {
    margin-top: 0;
  }
  .catalog_detail .item_main_info .item_slider:after,
  .catalog_detail .item_main_info .right_info .info_item:before {
    display: none;
  }
  .item_slider .slides {
    height: 260px;
    line-height: 250px;
    line-height: normal;
    padding: 25px 0px 5px 0px;
    overflow: hidden;
    margin: 0px;
    max-width: initial;
  }
  .item_slider.flex .slides > li > a {
    height: 240px;
    display: block;
  }
  .item_slider.flex .slides > li > a img {
    max-height: 100%;
  }

  .catalog_detail .top_info .like_icons span.value span {
    display: none;
  }
  .catalog_detail .top_info .like_icons span.value:before {
    margin-right: -1px;
  }
  .info_item .top_info .brand {
    padding: 0 5% 10px 0;
  }

  body .authorization-cols .auth-title {
    font-size: 18px;
  }

  body .authorization-cols .col.registration {
    margin-top: 30px;
  }
  body .module-cart table td.delay-cell .value {
    display: none;
  }
  body li[item-section="AnDelCanBuy"] .module-cart table td.count-cell {
    padding: 3px 0;
    font-size: 0;
  }
  body .module-cart .counter_block {
    font-size: 0;
  }
  body .module-cart table td {
    padding: 3px;
  }
  body .count-cell div.error {
    position: static;
    white-space: normal;
  }
  body .module-cart .counter_block input[type="text"] {
    width: 30px;
    height: 30px;
    font-size: 13px;
  }
  body .module-cart a.wish_item .icon i {
    margin-right: 0;
  }
  body .module-cart table tfoot td.extended-cell {
    display: none !important;
  }
  body .module-cart table tfoot .basket_fast_order_wrapp {
    text-align: right;
    padding-right: 20px;
  }
  body .module-cart table tfoot .basket_checkout_wrapp {
    padding-left: 20px;
  }
  body .module-cart table td.thumb-cell {
    width: 50px;
  }
  body #order_form_content .module-cart table td.thumb-cell {
    width: 80px;
  }
  body .module-cart table td.thumb-cell a {
    height: 50px;
    width: 50px;
    position: relative;
  }
  body .module-form-block-wr .form-block .r {
    width: 100%;
  }
  body .brands_list li {
    width: 32%;
  }
  body .drop-question .form-block .left-data,
  body .drop-question .form-block .right-data {
    width: 100%;
    margin-left: 0;
    padding-right: 0;
    float: none;
  }
  body .news_detail_wrapp .detail_picture_block {
    margin: 20px auto;
    text-align: center;
    float: none;
  }
  /*
  body .top_slider_wrapp.view_3 .flex-control-paging {
    display: none;
  }
  body .top_slider_wrapp .flex-direction-nav li {
    display: none;
  }
*/
  body .wrapper.head_type_1:not(.front_page) #header {
    margin-bottom: 70px;
  }
  body .wrapper.head_type_1:not(.front_page) #header + .wrapper_inner {
    border-top: 0;
    box-shadow: none;
    -o-box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
  }
  body #header ul.menu.full > li.search_row {
    display: block;
  }
  body .front_slider_wrapp .extended_pagination {
    display: none;
  }
  body .front_slider .info {
    padding-left: 0;
  }

  body .front_slider_wrapp .flex-viewport {
    width: 100%;
  }
  /*
  body .top_slider_wrapp .banner_text {
    margin-top: 10px;
    -webkit-line-clamp: 2;
  }
  body .flexslider table .text .banner_buttons {
    margin-top: 10px;
  }
  body .top_slider_wrapp .slides .banner_title .prices {
    margin-top: 14px;
  }
  */
  body .index_bottom .info_column .news_column .shadow {
    display: block;
  }
  body .index_bottom .info_column .about_column,
  body .index_bottom .info_column .news_column {
    width: 100%;
  }
  body .index_bottom .info_column .about_column {
    margin-bottom: 10px;
  }
  body .index_bottom .info_column .news_column {
    padding-left: 0;
  }
  body .index_bottom .info_column .about_column .about_show_more {
    display: block;
    text-align: center;
  }
  body .brands_slider_wrapp {
    padding: 36px 0px 25px;
  }
  body .top-h-row .search #title-search-input {
    display: none;
  }
  body .top-h-row .search #search-submit-button {
    border-radius: 2px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    position: static;
    margin: 1px auto 0;
  }
  body .top-h-row .search {
    width: 20%;
  }
  body .top-h-row .search form {
    width: 100%;
  }
  body .top-h-row .search {
    text-align: center;
  }
  body #header ul.menu.adaptive {
    display: block;
  }
  body #header ul.menu.adaptive .menu_opener {
    cursor: pointer;
  }
  body #header ul.menu.adaptive > li > a,
  body #header ul.menu.full > li > a {
    font-size: 14px;
    text-transform: uppercase;
    text-decoration: none;
    padding: 0 20px;
    display: inline-block;
    height: 42px;
    line-height: 43px;
  }
  body .wrapper.has_menu .main-nav {
    display: block;
  }
  body #header ul.menu.full {
    border-radius: 0 0 3px 3px;
    -moz-border-radius: 0 0 3px 3px;
    -webkit-border-radius: 0 0 3px 3px;
    display: none;
    box-shadow: 0 3px 18px rgba(44, 44, 44, 0.8);
    -moz-box-shadow: 0 3px 18px rgba(44, 44, 44, 0.8);
    -webkit-box-shadow: 0 3px 18px rgba(44, 44, 44, 0.8);
  }
  body ul.menu li.catalog a i {
    background: url("../images/arrows_small.png") 3px -21px no-repeat;
    right: 15px;
    top: 10px;
  }
  body ul.menu li.catalog:hover a i {
    background-position: -33px -21px;
  }
  body #header ul.menu.full > li:not(.current):not(.search_row) {
    background: #ecf0f3;
    border-top: 1px solid #f2f5f7;
    border-bottom: 1px solid #e4e6e7;
  }
  body #header ul.menu.full > li:not(.current) a {
    color: #000;
  }
  body #header {
    border-bottom: 0 !important;
    box-shadow: none !important;
    -moz-box-shadow: none !important;
    -o-box-shadow: none !important;
    -webkit-box-shadow: none !important;
  }
  body #header .center_block .main-nav {
    margin-top: 15px;
    left: -15px;
    margin-left: 0px;
    padding: 7px 15px;
  }
  body #header ul.menu.full > li {
    display: block;
    width: 100%;
    text-align: left;
  }
  body #header ul.menu.full > li.stretch {
    display: none;
  }
  body #header ul.menu.full.opened li:hover .child,
  body #header ul.menu.full.opened li:hover .space {
    display: none;
  }

  body #header ul.menu.full > li:not(.menu_opener) > a {
    border-left: 0 !important;
    border-right: 0 !important;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width: 100%;
  }
  body .top-h-row .search,
  body .wrapper.has_menu .top-h-row .search,
  .wrapper.has_menu #header .middle-h-row .center_block .search {
    display: none;
  }
  body .display_list .list_item .image {
    width: 33%;
  }
  body .display_list .list_item .image a,
  body .display_list .list_item .image,
  body .display_list .list_item {
    min-height: auto;
  }
  body .sort_header .sort_filter {
    font-size: 0;
  }
  .h_color_colored .header_wrap .menu > li.current > a span {
    border-bottom-width: 0px;
  }
  .h_color_colored .main-nav ul.menu > li > a:hover {
    opacity: 1;
  }
  .m_color_dark #header ul.menu.full.opened > li {
    background: #505050;
    border-top: 1px solid #848484;
    border-bottom: 1px solid #3d3d3d;
  }
  .m_color_dark #header ul.menu.full.opened > li.current {
    background: #2f2f2f;
    border-top: 1px solid #848484;
  }
  .m_color_dark #header ul.menu.full.opened li:not(.search_row):hover,
  body .m_color_dark #header ul.menu.full.opened li:not(.search_row):hover,
  body .wrapper.m_color_dark #header ul.menu.full.opened > li:hover {
    background: #505050;
  }
  .m_color_dark #header ul.menu.full.opened li.current,
  .m_color_dark #header ul.menu.full.opened li:not(.search_row):hover {
    border-bottom: 1px solid #3d3d3d;
    border-top: 1px solid #848484;
  }
  body .h_color_white .center_block .menu.full > li.current > a {
    color: #fff;
  }
  body .h_color_white .center_block .menu.full > li.current > a span {
    border: 0px;
  }

  .wrapper.has_menu .header_wrap #header .catalog_menu {
    display: none;
  }
  body .m_color_dark #header .center_block .main-nav,
  body .m_color_dark.wrapper #header .center_block .main-nav {
    background: #3f3f3f;
    border-top-width: 0px;
  }
  .basket_normal .popup {
    top: -3px !important;
  }
  .basket_wrapp .basket_block .link {
    bottom: -27px;
  }
  .bx_order_make .bx_block.float {
    width: 33%;
  }
  .bx_order_make .bx_block.r3x1,
  .bx_order_make .bx_block.r1x3 {
    width: 100%;
  }
  .module-gallery-list li {
    width: 33.33%;
  }
  .wrapper_inner .articles-list.vertical .item {
    width: 50%;
  }
  .articles-list.lists_block .right-data {
    margin-left: 150px;
  }
  .articles-list.lists_block:not(.vertical) .item .left-data {
    width: 100%;
    float: none;
    max-width: initial;
  }
  .articles-list.lists_block:not(.vertical) .item .left-data a {
    height: auto;
  }
  .articles-list.lists_block:not(.vertical) .item .right-data {
    margin: 20px 0px 0px 0px;
  }
  #content .right_side.wide {
    padding: 0px;
    float: none;
    width: 100%;
  }
  #content .left_side.wide {
    padding: 0px;
  }

  .wrapper.basket_fly:not(.has_menu) .top-h-row .form_mobile_block .search_middle_block,
  .wrapper.has_menu .top-h-row .form_mobile_block .search_middle_block {
    display: none;
  }
  .wrapper_inner .wrap_md .news_wrap,
  .wrapper_inner .wrap_md .subscribe_wrap {
    width: 100%;
    margin: 0px;
  }
  .wrapper_inner .soc-avt .row input[type="text"] {
    width: 90%;
  }

  .wrapper_inner .start_promo .item i.price {
    display: block;
  }
  .wrapper_inner .start_promo .item i.price .tizer_text {
    display: none;
  }

  .wrapper_inner .stores .all_map {
    margin-top: 5px;
  }
  .wrapper_inner .stores .stores_list {
    width: 100%;
    padding: 0px;
  }
  .bx-ie .basket_wrapp .wraps_icon_block .count a,
  .bx-ie .basket_wrapp .wraps_icon_block .count .text {
    line-height: 18px;
  }
  .start_promo .item img {
    max-height: 280px;
    height: 106% !important;
  }
  .item:not(.touch_class):hover .scale_block_animate,
  .touch_class .scale_block_animate {
    transform: none;
  }
  .start_promo .item.wide50 img,
  .item.wide50:hover img.scale_block_animate,
  .start_promo .item.wide100 img,
  .item.wide100:hover img.scale_block_animate {
    transform: scaleX(1.5);
    width: 100%;
  }

  .footer_bottom_inner .rows_block .menus,
  .footer_bottom_inner .rows_block .soc {
    width: 100%;
  }
  .footer_bottom_inner .rows_block .soc .soc_wrapper {
    text-align: center;
    float: none;
    margin: 10px 0px 0px;
  }
  #footer .footer_bottom_inner .links.rows_block {
    max-width: initial;
  }

  .bx_item_list_you_looked_horizontal.col3 .bx_catalog_item {
    width: 47.3333% !important;
  }
  .bx_item_list_you_looked_horizontal.col3 .bx_catalog_item:nth-child(3n + 1) {
    clear: none !important;
  }
  .bx_item_list_you_looked_horizontal.col3 .bx_catalog_item:nth-child(2n + 1) {
    clear: both !important;
  }

  #footer .footer_bottom_inner .phone_block {
    padding-right: 25px;
  }
  #footer .footer_bottom_inner .phones {
    padding-bottom: 20px;
  }
  #footer .footer_bottom_inner {
    padding-bottom: 14px;
  }
  #footer .footer_bottom_inner .phones .order_wrap_btn {
    margin-bottom: 0px;
  }

  .bx_ordercart_order_pay_center .icon_error_wrapper {
    float: none;
    margin: 0px 0px 20px;
  }
  .tracker {
    display: none;
  }

  .icon-text {
    white-space: normal;
  }

  #reviews_content .empty-message .reviews-collapse {
    float: none !important;
    margin-bottom: 20px;
  }

  /*front*/
  body#main .wrapper1 .wrapper_inner.front .drag-block.container .content_wrapper_block > .maxwidth-theme {
    padding-top: 30px;
  }
  body#main .wrapper1 .wrapper_inner.front .drag-block.container .tab_slider_wrapp {
    padding-bottom: 31px;
  }
  body#main .wrapper1 .wrapper_inner.front .drag-block.container.CATALOG_TAB .tab_slider_wrapp {
    padding-bottom: 45px;
  }
  body#main .wrapper1 .wrapper_inner.front .drag-block.container .sections_wrapper:not(.smalls),
  body#main .wrapper1 .wrapper_inner.front .drag-block.container .reviews.item-views {
    padding-bottom: 40px;
  }
  body#main .wrapper1 .wrapper_inner.front .drag-block.container .hot-wrapper-items {
    padding-bottom: 30px;
  }

  /*new mobile front*/
  #main .mobile-overflow {
    -webkit-overflow-scrolling: auto;
    white-space: nowrap;
    display: flex;
    overflow: auto;
    flex-wrap: nowrap;
    justify-content: normal;
  }
  #main .mobile-overflow > div {
    flex-shrink: 0;
    white-space: normal;
  }
  #main .mobile-overflow:before,
  #main .mobile-overflow:after {
    display: block;
    content: none;
  }

  .item-views .swipeignore.mobile-overflow:not(.has-bottom-nav) .bottom_nav.mobile_slider {
    display: none;
  }

  .mobile-overflow.mobile-overflow--visible::-webkit-scrollbar {
    -webkit-appearance: none;
  }
  .mobile-overflow::-webkit-scrollbar:vertical {
    width: 6px;
  }
  .mobile-overflow::-webkit-scrollbar:horizontal {
    height: 6px;
  }
  .mobile-overflow::-webkit-scrollbar-thumb {
    background-color: rgba(153, 153, 153, 1);
    border-radius: 10px;
    border: 2px solid #ffffff;
  }
  .mobile-overflow::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: #ffffff;
  }
  .mobile-margin-16 {
    margin: 0px -16px;
  }
  .mobile-margin-16.mobile-compact {
    padding: 0px 0px 0px 16px;
  }
  .mobile-margin-16.mobile-compact > div {
    padding: 0px 16px 0px 0px;
  }
  .swipeignore .item-width-261 {
    width: 277px;
  }
  .swipeignore .item-width-98 {
    width: 114px;
  }
  .swipeignore .item-width-322 {
    width: 338px;
  }
  .swipeignore .item-width-322.np {
    width: 322px;
  }
  .swipeignore.c_1 .item-width-261,
  .swipeignore.c_1 .item-width-322 {
    width: 100%;
    max-width: 400px;
  }
  .item-wrapper .bottom_nav.mobile_slider {
    height: 100%;
  }

  #main .wrapper1 .wrapper_inner.front .drag-block.container.MAPS .content_wrapper_block.map_type_3 > .maxwidth-theme {
    padding-top: 0px;
  }

  /*bigbanners*/
  body .top_big_banners .items .item {
    width: 50%;
    float: left;
  }
  body .top_big_banners .items .item .item_inner {
    height: auto;
    padding-top: 91%;
  }
  body .top_big_banners .items .item.wide50,
  body .top_big_banners .items .item.wide100 {
    width: 100%;
  }
  body .top_big_banners .items .item.wide50 .item_inner,
  body .top_big_banners .items .item.wide100 .item_inner {
    padding-top: 45%;
  }
  body .big-banners-mobile-slider .top_big_banners .items .item {
    flex-shrink: 0;
    width: 80%;
  }
  body .big-banners-mobile-slider .top_big_banners .items .item .item_inner {
    padding-top: 70%;
  }
  body .big-banners-mobile-slider .top_big_banners .items.c_1 .item {
    width: 100%;
    padding-top: 60%;
  }
  .big-banners-mobile-slider .top_big_banners .item .item_inner .wrap_tizer {
    white-space: normal;
  }
  .top_big_banners .item .item_inner .text .title {
    font-size: 0.933em;
  }
  .top_big_banners .item .item_inner .wrap_tizer .wrapper_inner_tizer {
    padding: 17px 40px 17px 19px;
  }
  /**/

  /*side big banners*/
  body .top_big_banners .side-childs.normal > .item {
    width: 50%;
    float: left;
  }
  body .top_big_banners .side-childs.normal.c_3.combine > .item {
    width: 100%;
  }
  body .top_big_banners .side-childs.normal > .blocks2 {
    width: 50%;
    float: left;
  }
  body .top_big_banners .side-childs.normal > .item .item_inner {
    height: auto;
    padding-top: 91%;
  }
  body .top_big_banners .side-childs.normal.c_3.combine > .item .item_inner {
    padding-top: 45%;
  }
  body .top_big_banners .side-childs.normal > .blocks2 .item_inner {
    height: auto;
    padding-top: 91%;
  }
  body .top_big_banners .side-childs.mobile-overflow > div {
    flex-shrink: 0;
    width: 80%;
  }
  body .top_big_banners .side-childs.mobile-overflow .item .item_inner {
    padding-top: 70%;
    height: auto;
  }
  body .top_big_banners .side-childs.mobile-overflow.c_1 .item {
    width: 100%;
  }
  body .top_big_banners .side-childs.mobile-overflow.c_1 .item .item_inner {
    padding-top: 60%;
  }
  .top_big_banners .hidden_side_mobile {
    display: none;
  }
  .top_big_banners .visible_side_mobile {
    display: block;
  }
  /**/

  /*collections*/
  .COLLECTIONS .item-views.collection.grey_pict .item {
    padding-top: 29px;
    padding-bottom: 11px;
  }
  .COLLECTIONS .item-views.collection .item .top-info {
    font-size: 0.933em;
  }
  .COLLECTIONS .item-views.collection:not(.normal) .image.pattern > .wrap {
    margin-bottom: 23px;
  }

  .COLLECTIONS .items.swipeignore.mobile-overflow .item-wrapper {
    width: 277px;
  }
  .COLLECTIONS .items.swipeignore.mobile-overflow.c_1 .item-wrapper {
    width: 100%;
    max-width: 400px;
  }
  .COLLECTIONS .items.swipeignore.mobile-overflow .item-wrapper .title {
    line-height: 21px;
  }
  .COLLECTIONS .item-views.bg_img .item-wrapper {
    margin-bottom: 25px;
  }
  #main .drag-block.COLLECTIONS .collection.item-views.bg_img {
    padding-bottom: 25px;
  }
  .COLLECTIONS .item-views.bg_img .item-wrapper .item {
    white-space: normal;
  }
  .COLLECTIONS .item-views.normal {
    margin-bottom: 0px;
  }
  .COLLECTIONS .item-views.normal .item-wrapper {
    margin-bottom: 15px;
  }
  .COLLECTIONS .item-views.normal .item-wrapper .image span {
    height: auto;
    padding-top: 67%;
  }
  .COLLECTIONS .item-views.collection.normal .item {
    padding-bottom: 0px;
  }
  .COLLECTIONS .item-views.collection.normal .item .top-info {
    padding-top: 14px;
  }
  /**/
  /**/

  /*personal*/
  .personal_wrapper .orders_wrapper .sale-order-payment-change-pp-list .sale-order-payment-change-pp-company {
    width: 100%;
    padding-right: 0px;
  }
  .personal_wrapper .orders_wrapper .sale-order-list-inner-row .sale-order-list-cancel-container {
    float: none;
  }
  .personal_wrapper
    .orders_wrapper
    .sale-order-detail-payment-options-shipment
    .sale-order-detail-payment-options-shipment-image-container {
    width: 100%;
  }
  .personal_wrapper
    .orders_wrapper
    .sale-order-detail-payment-options-shipment
    .sale-order-detail-payment-options-methods-shipment-list {
    width: 100%;
    padding-top: 10px;
  }
  .sale-order-detail-order-item-td .sale-order-detail-order-item-block,
  .sale-order-detail-order-item-td .sale-order-detail-order-item-block .sale-order-detail-order-item-img-block {
    padding: 0px;
  }
  .personal_wrapper .orders_wrapper .sale-order-detail-payment-options-shipment-composition-map > .row {
    margin-right: -7px;
  }
  .personal_wrapper
    .orders_wrapper
    .sale-order-detail-payment-options-methods-information-block
    .sale-order-detail-payment-options-methods-image-container,
  .personal_wrapper
    .orders_wrapper
    .sale-order-detail-payment-options-methods-information-block
    .sale-order-detail-payment-options-methods-info {
    width: 100%;
    padding-bottom: 10px;
  }
  .personal_wrapper .orders_wrapper div.sale-order-detail-payment-options-methods {
    padding-top: 0px;
  }
  .personal_wrapper .orders_wrapper div.sale-order-detail-about-order-inner-container > .row > div {
    width: 100% !important;
  }
  .sale-order-detail-about-order-inner-container-name-read-more,
  .sale-order-detail-about-order-inner-container-name-read-less {
    margin: 0px 0px 15px;
  }
  .sale-order-detail-order-item-td.sale-order-detail-order-item-properties {
    width: 100%;
    float: left;
  }
  .sale-personal-profile-list-container > tbody > tr > th,
  .sale-personal-profile-list-container > tfoot > tr > th,
  .sale-personal-profile-list-container > thead > tr > td,
  .sale-personal-profile-list-container > tbody > tr > td {
    font-size: 10px;
    padding-left: 2px;
    padding-right: 2px;
  }
  .sale-personal-profile-list-container > tbody > tr > th {
    padding-left: 2px;
    padding-right: 2px;
  }

  /*video*/
  .top_slider_wrapp .box .btn.btn-video.play {
    width: 59px;
    height: 59px;
  }
  .top_slider_wrapp .box .btn.btn-video.play:before {
    width: 59px;
    height: 59px;
    background: url(../images/next.png) -3px -273px no-repeat;
  }

  .col-xxs-12 {
    width: 100%;
  }

  .list-type-block.item-views.wide_img .item > .image,
  .list-type-block.item-views.image_right.wide_img .item > .image {
    float: none;
    width: 100%;
    text-align: center;
  }
  .list-type-block.item-views .item > .body-info,
  .list-type-block.item-views.image_right .item .body-info {
    float: none;
    width: 100%;
    padding-left: 0px !important;
    padding-right: 0px !important;
    padding-top: 20px;
  }

  /*list catalog*/
  body .item .item_info.catalog-adaptive {
    display: block;
    padding: 30px 25px;
    height: 100%;
    bottom: -1px;
    position: relative;
  }
  body .ajax_load .item.box-shadow:hover {
    box-shadow: none;
    border-color: #ececec;
    border-color: var(--stroke_black);
    transform: none;
  }
  body .item .item_info.catalog-adaptive > div {
    width: 100%;
    padding: 15px 0px 0px;
  }
  body .item .item_info.catalog-adaptive > div:first-of-type,
  body .item .item_info.catalog-adaptive > .image_block {
    padding-top: 0px;
  }
  body .item .item_info.catalog-adaptive .adaptive {
    display: block;
  }
  body .item .item_info.catalog-adaptive .adaptive .like_icons span {
    opacity: 1;
    visibility: visible;
  }

  body .display_list .list_item .image_block .fast_view_block,
  body .display_list .list_item .description_wrapp .like_icons,
  body .display_list .list_item .information_wrapp .wrapp-one-click,
  body .display_list .description_wrapp .preview_text,
  body .display_list.TYPE_2 .list_item .description_wrapp .show_props,
  body .item .item_info.catalog-adaptive .counter_wrapp .more_text {
    display: none;
  }

  body .wrapper1:not(.compact-catalog) .display_list .list_item .image_block {
    width: auto;
  }
  body .wrapper1:not(.compact-catalog) .image_wrapper_block,
  body .wrapper1:not(.compact-catalog) .image_wrapper_block > a {
    height: auto;
  }

  body .display_list .description_wrapp .description .wrapp_stockers.with-rating .js-info-block {
    left: -15px;
  }
  .display_list .description_wrapp .item-title a {
    font-size: 0.933em;
  }
  .display_list .description_wrapp .item-title a span {
    font-size: 1em;
  }
  .display_list .description_wrapp .description .wrapp_stockers .article_block,
  body .display_list.TYPE_2 .list_item .description_wrapp:after {
    display: none;
  }
  body .display_list.TYPE_2 .list_item .description_wrapp .description {
    padding-right: 0px;
  }

  body .display_list .list_item .information_wrapp {
    z-index: 4;
    bottom: -1px;
  }
  body .display_list .list_item .information_wrapp > div {
    width: auto;
  }
  body .display_list .list_item .information_wrapp > div > div {
    max-width: 320px;
  }
  body .item .item_info.catalog-adaptive .counter_wrapp.list {
    margin: 0px -26px -30px;
    max-width: none;
  }
  body .item .item_info.catalog-adaptive .counter_wrapp.list > div:not(.total_summ) {
    width: 50%;
    margin: 0px;
    padding: 0px;
  }
  body .item .item_info.catalog-adaptive .counter_wrapp.list > div.wide {
    width: 100%;
  }
  body .item .item_info.catalog-adaptive .counter_wrapp.list .counter_block,
  body .item .item_info.catalog-adaptive .counter_wrapp.list .btn {
    border-radius: 0px;
    margin: 0px;
  }
  body .item .item_info.catalog-adaptive .counter_wrapp.list .counter_block,
  body .item .item_info.catalog-adaptive .counter_block:not(.big) input[type="text"] {
    height: 56px;
  }
  body .item .item_info.catalog-adaptive .counter_wrapp.list .btn {
    padding-top: 20px;
    padding-bottom: 19px;
  }
  body .display_list .list_item .js-info-block {
    left: -15px;
  }
  body .wrapper1 .display_list .list_item .information_wrapp .counter_wrapp > .button_block:first-child {
    width: 100%;
  }
  body .display_list .list_item .information_wrapp .button_block .read_more {
    width: 100%;
  }

  body .item .item_info.catalog-adaptive .counter_wrapp.list > div.total_summ {
    padding: 5px 0px 5px 25px;
    margin: 0px;
    position: static;
  }
  .tab_slider_wrapp .top_block > a + div {
    padding: 0px;
    float: none !important;
  }
  body ul.tabs li {
    margin-right: 15px;
  }
  .js_wrapper_items .tab_slider_wrapp .top_block {
    margin-bottom: 30px;
  }

  /*block catalog*/
  body .catalog_block .counter_wrapp {
    position: relative;
    z-index: 2;
    border-radius: 0px;
  }
  body .catalog_block .counter_wrapp > .counter_block {
    border-left-width: 1px;
    border-bottom-width: 1px;
  }
  body .catalog_item:hover .inner_wrap {
    box-shadow: inset 0px 0px 0px 1px #ececec;
    box-shadow: inset 0px 0px 0px 1px var(--stroke_black);
  }
  .mobile body .catalog_item:hover .footer_button,
  .mobile body .catalog_item.hover .footer_button {
    margin-top: -2px;
  }
  body .ajax_load.block .footer_button .btn {
    border-radius: 0px;
    border-bottom-width: 1px;
    margin: 0px;
  }

  .ajax_load.block .owl-item .btn.in-cart .svg.svg-inline-fw svg {
    display: none;
  }

  /*table catalog*/
  body .table-view .table-view__item {
    padding: 0px;
  }
  .table-view .item-foto__picture {
    width: auto;
    height: 250px;
    line-height: 250px;
  }
  .compact-catalog .table-view .item-foto__picture {
    height: 150px;
    line-height: 150px;
  }
  .table-view .item-foto__picture a:before,
  .table-view .table-view__item-wrapper .item-icons {
    display: none;
  }
  body .table-view .table-view__item .adaptive {
    display: block;
  }
  body .table-view .table-view__item .adaptive .like_icons span {
    opacity: 1;
    visibility: visible;
  }

  .table-view .item-actions {
    z-index: 4;
    bottom: -1px;
  }
  .table-view .table-view__item-wrapper .item-title {
    padding: 0px;
    margin: 0px;
  }
  .compact-catalog .table-view__item:hover {
    z-index: auto;
  }

  .opt-buy:not(.show_on_mobile),
  .with-opt-buy .table-view__item-wrapper .item-check {
    display: none;
  }
  body .with-opt-buy .item .item_info.catalog-adaptive .item-foto {
    padding-top: 0px;
  }

  .catalog_section_list .section_item {
    padding: 22px 22px 17px;
    height: auto !important;
  }
  .catalog_section_list .section_item td.image {
    padding-bottom: 20px;
    margin: auto;
  }
  .catalog_section_list .section_item td.section_info .desc {
    display: none;
  }
  .catalog_section_list .section_item td.section_info {
    height: auto !important;
    vertical-align: middle;
    text-align: center;
  }
  .catalog_section_list .section_item li.name a span {
    font-size: 15px;
  }
  .catalog_section_list .section_item .image {
    width: 60px;
  }

  .catalog_section_list .section_item .desc .desc_wrapp {
    display: none;
  }
  .catalog_section_list .section_item tr td {
    display: block;
  }
  .catalog_section_list .section_item .image {
    width: 100%;
    padding: 0px;
  }

  .catalog_section_list .section_info li.sect a,
  .catalog_section_list .section_info li.name a {
    word-break: break-all;
    word-break: break-word;
  }

  /*catalog compact list*/
  .compact-catalog .ajax_load .item {
    width: 50%;
    height: auto;
  }
  .compact-catalog .block .catalog_block .catalog_item_wrapp:hover,
  .compact-catalog .block .catalog_block .catalog_item_wrapp.hover {
    z-index: 4;
  }

  .compact-catalog .ajax_load .display_list,
  .compact-catalog .ajax_load .table-view {
    flex-wrap: wrap;
    align-items: normal;
  }
  .compact-catalog .item .item_info.catalog-adaptive .counter_wrapp.list > div.total_summ {
    display: none !important;
  }

  .compact-catalog .display_list .item_wrap.item:hover {
    z-index: auto;
  }
  .compact-catalog .item .item_info.catalog-adaptive {
    bottom: -1px;
    position: relative;
    padding-bottom: 69px;
  }
  .compact-catalog .display_list .list_item .information_wrapp {
    position: static;
  }
  .compact-catalog .item .item_info.catalog-adaptive .counter_wrapp.list {
    position: absolute;
    left: 0px;
    right: 0px;
    margin: 0px;
    z-index: 5;
    padding: 0px;
    bottom: 0px;
  }
  .compact-catalog .display_list,
  .compact-catalog .table-view {
    margin-right: -2px;
  }
  .compact-catalog .display_list .item {
    margin-left: -1px;
  }
  .compact-catalog .item .item_info.catalog-adaptive .counter_wrapp.list > div:not(.button_block),
  .compact-catalog .filter-panel__view {
    display: none !important;
  }
  .compact-catalog .item .item_info.catalog-adaptive .counter_wrapp.list > .button_block {
    width: 100%;
  }

  .compact-catalog .ajax_load .item .item-title a {
    -webkit-line-clamp: 3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    font-size: 11px;
    line-height: 1.5em;
    height: 58.5px;
  }
  .compact-catalog .ajax_load .cost.prices .price {
    font-size: 14px;
  }

  .compact-catalog .ajax_load .item.big .item-title a:not(:hover) {
    color: #333;
    color: var(--white_text_black);
  }

  .compact-catalog .table-view .table-view__item {
    margin-left: -1px;
  }
  .compact-catalog .js-info-block {
    width: 225px;
    left: -15px;
  }

  .compact-catalog .product-main .js-info-block {
    width: 250px;
  }
  
  .inline-search-block.corp .search-wrapper {
    margin-top: 20px;
  }
  .inline-search-block.corp {
    align-items: baseline;
  }
  .inline-search-block .search .search-button-div .btn-search {
    /*display: none;*/
  }
  .inline-search-block .search .dropdown-select {
    right: 47px;
  }
  .inline-search-block.corp .search .dropdown-select {
    right: 40px;
    top: 5px;
  }
  .inline-search-block .search .search-input {
    height: 42px;
    font-size: 13px;
    padding-right: 0;
  }
  .inline-search-block.corp .search .search-input {
    padding-right: 40px;
  }
  .inline-search-block .search.search--hastype .search-input {
    padding-right: calc(34px + var(--theme-page-width-padding));
  }
  .inline-search-block.corp .search.search--hastype .search-input {
    padding-right: calc(102px + var(--theme-page-width-padding));
}
  .inline-search-block .search .search-button-div {
    top: -3px;
  }
  .inline-search-block.corp .search .search-button-div .btn-search-corp {
    display: none;
  }
  .inline-search-block .search .close-block .close-icons {
    width: 22px;
    height: 22px;
    min-width: 22px;
    min-height: 22px;
  }


  .inline-search-block.corp .search .close-block {
    top: 24px;
    right: 21px;
  }
  .inline-search-block .search .close-block .close-icons svg {
    width: 14px;
    height: 14px;
  }
  .title-search-result.title-search-input:not(.fixed_type) {
    top: 70px !important;
  }

  .compact-catalog .ajax_load .banner.item {
    width: 100%;
  }
  .popup {
    width: 100%;
  }
}

@media (min-width: 501px) and (max-width: 600px) {
  .catalog_block .catalog_item > div {
    display: flex;
    flex-direction: column;
  }
  .catalog_block .catalog_item > div .item_info {
    flex: 1;
  }
  .catalog_item .image_wrapper_block {
    width: 100%;
  }
  .display_list .list_item .image_wrapper_block,
  .list_item .image_wrapper_block > a {
    width: 100%;
  }
}
@media screen and (max-width: 580px) {
  /*basket2*/
  .basket-items-list-item-amount {
    min-width: auto;
  }
  #basket-root .basket-checkout-section-inner .fastorder {
    padding-left: 0px;
    width: 100%;
  }
  #basket-root .basket-checkout-container .basket-checkout-block-btns-wrap {
    width: initial;
    margin: 0;
  }
  #basket-root .basket-checkout-container .basket-checkout-block-btns-wrap .basket-checkout-block-btn,
  #basket-root .basket-checkout-container .basket-checkout-block-btns-wrap .fastorder {
    padding-left: 0;
  }
  .basket-checkout-block-share {
    margin: 14px auto 0;
  }
  .sale-products-gift .product-item-small-card > .row > div {
    width: 100%;
  }
}
@media all and (max-width: 570px) {
  .wrapper_inner .bx_ordercart_order_sum td.custom_t1 {
    width: 80%;
  }

  /*basket*/
  .bx_ordercart .bx_ordercart_order_pay .bx_ordercart_order_pay_center .catalog_back {
    width: 185px;
    text-align: center;
  }
  .bx_ordercart .bx_ordercart_order_pay .bx_ordercart_order_pay_center .checkout {
    float: left;
    clear: both;
    width: 185px;
    text-align: center;
    margin: 10px 0px 10px;
  }
  .bx_ordercart .bx_ordercart_order_pay_center .oneclickbuy {
    float: right;
    margin: 10px 0px 10px;
  }

  /* ORDER */
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-img-block {
    float: left;
  }
  #bx-soa-order .bx-soa-coupon-item {
    text-align: left;
  }
  #bx-soa-order .bx-soa-section .bx-soa-coupon-item .bx-soa-tooltip {
    float: none;
    margin-left: 0;
  }
  #bx-soa-order .bx-soa-coupon-item .bx-soa-tooltip .tooltip-inner {
    margin-top: 3px;
    text-align: left;
  }
  #bx-soa-order .bx-scu-container {
    padding-top: 0;
  }
  #bx-soa-order .bx-soa-item-td-title,
  #bx-soa-order .bx-soa-item-td-text {
    display: block !important;
  }
  #bx-soa-order .bx-soa-item-td-title,
  #bx-soa-order .bx-soa-item-td-text {
    font-size: 15px;
  }
}
@media all and (min-width: 551px) and (max-width: 991px) {
  hr.bottoms + .row > div {
    float: left;
  }
  hr.bottoms + .row > div.share {
    float: right;
  }
}
@media all and (max-width: 550px) {
  .catalog_detail .offers_table .opener {
    padding: 0px;
  }
  .wrapper_inner table.offers_table td {
    padding: 13px 8px;
  }
  table.offers_table .ablock {
    margin: 0px 0px 10px;
  }
  table.offers_table td.count {
    text-align: left;
    width: 50%;
    height: 40px;
    border-bottom: 0;
  }
  table.offers_table td.counter_block {
    width: 50%;
    height: 40px;
    border-bottom: 0;
  }
  table.offers_table td.buy {
    width: 50%;
    text-align: left;
    clear: left;
  }
  table.offers_table td.one_click_buy {
    width: 50%;
    text-align: center;
  }
  .counter_block_wr.ablock {
    padding: 0px 5px 0px 0px;
  }

  .basket_normal .popup {
    top: -12px !important;
  }
  #basket_line .basket_normal .popup {
    display: none !important;
  }

  body .top_block {
    position: relative;
  }
  body .top_block h3 {
    margin-right: 45px;
  }
  body .top_block a {
    margin-bottom: 10px;
    clear: both;
  }

  body .wrapper1.front_page .top_block a {
    margin-bottom: 10px;
    clear: both;
    position: absolute;
    right: 0;
    top: 17px !important;
    font-size: 0;
    background-repeat: no-repeat;
    background-image: url(../images/svg/catalog/arrow.svg);
    height: 10px;
    width: 12px;
    transform: rotate(90deg);
    opacity: 0.26;
  }

  body .top_block .title_block,
  body .top_block a,
  body .top_block > span {
    margin-right: 0px;
    display: block;
    text-align: left;
    float: none !important;
  }
  body .top_block > span {
    padding: 0px 0px 0px 12px;
  }
  body .top_block > span .svg {
    top: 0px;
  }
  body .top_block > span > span {
    float: none !important;
  }

  body .top_block > span.reviews {
    display: none;
  }

  body .top_block > span.subscribe .svg {
    left: -20px;
  }

  .slider_navigation.compare .flex-direction-nav {
    display: none !important;
  }
  .wrapp_scrollbar .wr_scrollbar {
    margin-left: 0px;
    margin-right: 0px;
  }
  .bx_compare .tabs-head li {
    font-size: 13px;
  }
  .wrapper_inner .bx_sort_container .wrap_remove_button {
    left: 0px;
    top: 70px;
    right: initial;
    z-index: 3;
  }

  .footer_bottom .all_menu_block .submenu_block {
    display: none;
  }
  .footer_bottom .submenu_top .menu_item {
    width: 100%;
  }

  .footer_top .wrap_md .phones .order {
    display: none;
  }
  .footer_top .wrap_md .phones .phone_wrap {
    width: 100%;
  }
  .footer_top .sblock .forms .email_wrap {
    width: 60%;
  }

  .footer_top .sblock .wrap_bg {
    width: 100%;
    padding: 0px 0px 13px;
  }
  .footer_top .sblock .forms {
    width: 100%;
    padding-left: 0px;
  }
  #footer .wrap_md .empty_block {
    display: none;
  }
  .footer_bottom .social_block {
    padding: 0px;
  }
  .footer_top .wrap_md .phones .phone_block,
  .footer_bottom .social_block .social_wrapper {
    width: 100%;
    padding: 0px;
  }
  .pay_system_icons {
    display: none;
  }

  .basket_wrapp .basket_block div.text {
    display: none;
  }

  .h_color_colored .header_wrap .basket_wrapp .empty_cart:not(.bcart) .wraps_icon_block.basket .count span {
    background: #000;
  }

  .list-type-block.item-views.staff .item .image:not(.pagging) {
    margin: 25px auto;
    float: none;
  }
  .list-type-block.item-views.staff .item:not(.wti) .body-info {
    padding-bottom: 20px;
    padding-left: 20px !important;
    padding-right: 20px !important;
  }
  .list-type-block.item-views.staff .item:not(.wti) .body-info .bottom-props {
    padding-bottom: 0px;
  }

  hr.bottoms + .row > div {
    margin-bottom: 20px;
    height: 44px;
  }
  hr.bottoms + .row > div:last-of-type {
    margin-bottom: 0px;
  }

  body .module_products_list td {
    padding: 2px;
  }
  body .search-page form {
    padding: 15px 5px 17px;
  }

  body .catalog_block.block_list .item_block {
    width: 100%;
  }
  body #header .middle-h-row td.logo_wrapp {
    width: 50%;
  }
  #header .middle-h-row td.logo_wrapp img {
    max-width: 100%;
  }
  body #header .middle-h-row td.center_block {
    padding: 0;
    width: 0;
  }
  body #header .middle-h-row .basket_wrapp {
    width: 35%;
  }
  body .basket_normal #header .middle-h-row .basket_wrapp {
    width: 50%;
  }
  body .header-cart-block .cart .summ {
    display: none;
  }
  body .header-cart-block .cart .cart_wrapp {
    padding-left: 32px;
  }
  body .header-cart-block .cart {
    width: 120px;
  }
  body .header-cart-block .cart .cart_wrapp:not(.with_delay) {
    padding-top: 6px;
  }
  body .header-cart-block .cart > span.icon {
    display: none;
  }
  body .header-cart-block .cart > span.icon.small {
    display: inline-block;
  }
  body .header-cart-block .cart .delay_link .icon {
    display: none;
  }
  body .header-cart-block .cart .delay_link {
    position: relative;
    zoom: 1;
    top: -4px;
  }
  body .header-cart-block .cart .cart-call,
  body .header-cart-block .cart .cart-call-empty {
    display: none;
  }
  body .header-cart-block .cart .cart-call.small {
    display: inline;
  }
  body .header-cart-block .cart .cart-call.small + a {
    display: none;
  }
  body .header-cart-block .cart .delay_link .icon {
    position: relative;
    zoom: 1;
    top: 0;
    width: 17px;
  }
  body .header-cart-block .cart .cart-call span {
    border: none;
    text-decoration: underline;
  }
  body .header-cart-block .cart .cart_wrapp.with_delay {
    padding-top: 0;
  }
  body .front_slider .preview_text {
    -webkit-line-clamp: 2;
  }
  body ul.tabs li span {
    font-size: 10px;
    padding-top: 6px;
    line-height: 9px;
  }
  body ul.tabs li {
    padding: 0 4px;
    height: 20px;
    line-height: 20px;
  }

  /*filter*/
  .show-normal-sort .filter-panel__filter .controls-hr,
  .show-normal-sort > .clearfix {
    display: none;
  }
  .show-normal-sort.filter-panel {
    display: flex;
    justify-content: space-between;
  }
  .show-normal-sort .filter-panel__sort {
    display: inline-block !important;
    float: none !important;
  }
  /**/

  body #footer ul.bottom_main_menu li {
    display: block;
    text-align: center;
    width: 100%;
    padding: 0 20%;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
  }
  body #footer ul.bottom_main_menu li a {
    border-bottom: 1px solid #d8d8d9;
    width: 100%;
    display: inline-block;
    padding: 9px 0 5px;
    color: #000;
    text-shadow: none;
    -moz-text-shadow: none;
    -webkit-text-shadow: none;
  }
  body #footer .bottom_submenu li {
    display: none;
  }
  body #footer .bottom_submenu li.copy {
    display: block;
    width: 100%;
    text-align: center;
  }
  body #footer .footer_inner .line {
    background: transparent;
  }

  body .store_map .stores_images.multiple {
    text-align: center;
  }
  body .show_number {
    text-align: center;
  }
  body .authorization-cols .auth-title {
    text-align: center;
  }
  body .search-page form {
    width: 100%;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    text-align: center;
  }

  body #content .left_block .left_menu {
    text-align: center;
  }

  .bx_order_make .bx_block.float {
    width: 50%;
  }
  .wrapper_inner .stores_block_wrap .stores_block .stores_text_wrapp > span {
    display: none;
  }
  .wrapper_inner .confirm .bx_section {
    padding: 18px 23px;
  }
  .confirm .bg_block:before,
  .confirm .bg_block:after {
    display: none;
  }
  .form-block-wr .iblock.label_block,
  .form-block-wr .iblock.text_block {
    width: 100%;
  }
  .form-block-wr .iblock.text_block {
    padding: 10px 0px 0px;
  }
  .basket_wrapp .empty_cart.ecart.bcart .wraps_icon_block.basket .count,
  .basket_wrapp .nitems.ecart .wraps_icon_block.basket .count {
    opacity: 1;
  }
  .wrapper_inner .basket_wrapp .wraps_icon_block.basket .count .items a {
    color: #fff;
  }
  .stores_block_wrap .stores_block.wo_image .stores_text_wrapp .main_info {
    max-width: none;
  }
  #footer .mobile_copy {
    text-align: center;
  }
  .basket_normal .basket_wrapp .wrapp_all_icons {
    width: auto;
    white-space: nowrap;
  }
  .basket_wrapp .icon_block,
  .basket_normal .basket_wrapp .header-cart {
    float: none;
    display: inline-block;
  }
  .basket_wrapp .wraps_icon_block.basket {
    margin-right: 0px;
  }
  .wrapper1:not(.compact-catalog) .specials.tab_slider_wrapp ul.tabs_content li.tab .catalog_block > div.item,
  .rows_block .item_block .catalog_item_wrapp {
    width: 100% !important;
  }

  .contacts-page-map .bx-yandex-view-layout {
    margin-left: 20px;
    margin-right: 20px;
    border: 1px solid #ececec;
  }
  .contacts-page-map .bx-yandex-view-layout .bx-yandex-view-map,
  .contacts-page-map .bx-yandex-view-layout .bx-yandex-map {
    height: 200px !important;
  }

  .page-top > div {
    padding-top: 18px;
  }
  .page-top > div:last-of-type {
    padding: 9px 0px 9px;
  }

  /*personal*/
  .bx-sap .sale-acountpay-block,
  .bx-sap .container-fluid > .row > .col-xs-12 {
    text-align: center;
  }
  body .personal_page #content .wrapper_inner .left_block .left_menu > li.exit {
    width: 100%;
    padding-right: 0px;
  }
}
@media all and (max-height: 520px) {
  .contacts-page-map .bx-yandex-view-layout {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media all and (max-width: 520px) {
  .staff.list .item {
    float: none;
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }
  .middle_phone .phones .phone_text a {
    font-size: 14px;
    line-height: 14px;
    margin: 10px 0;
  }

  .rows_block:not(.slides) .item_block {
    width: 100% !important;
  }
  .md-25.img {
    display: none;
  }
  .md-75.big {
    padding: 0px;
    width: 100%;
  }

  .info_item .top_info .article {
    text-align: left;
  }

  #footer .rows_block .item_block {
    text-align: center;
  }
  #footer .rows_block .soc_icons .item_block {
    line-height: 0px;
    width: auto !important;
  }
  #footer .rows_block .soc_icons .item_block a {
    display: inline-block;
  }

  /*basket*/
  .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr {
    padding-left: 20px;
  }
  .bx_ordercart.bx_blue .bx_ordercart_order_pay_center .catalog_back {
    float: none;
    width: auto;
    display: block;
  }
  .bx_ordercart.bx_blue .bx_ordercart_order_pay_center .oneclickbuy {
    float: none;
    margin: 20px 0 0;
    width: auto;
    display: block;
  }
  .bx_ordercart.bx_blue .bx_ordercart_order_pay_center .checkout {
    float: none;
    margin: 20px 0 0;
    width: auto;
    display: block;
  }
  .bx-touch .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr > td {
    padding: 0;
  }
  .bx-touch .bx_ordercart .bx_ordercart_photo_container {
    padding-top: 0;
  }
  .bx-touch .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr {
    margin-bottom: 0;
    padding: 20px;
    border-bottom: none;
  }
  .bx-touch .bx_ordercart .bx_ordercart_order_table_container table tbody tr td.item {
    padding-bottom: 10px;
    padding-left: 0;
  }
  .bx_ordercart .bx_ordercart_order_table_container .bx_ordercart_photo {
    height: 70px;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.itemphoto {
    float: none;
    margin: 0 0 15px;
    width: 70px;
  }
  .bx_ordercart .bx_ordercart_order_table_container tbody td.itemphoto > div {
    margin: 0;
    width: 70px;
    height: 70px;
    line-height: 70px;
  }
  .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr > td:not(.itemphoto):not(.margin):not(.item) {
    margin-top: 0;
  }
  .bx-touch .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr > td.custom span {
    margin-right: 0;
    font-weight: normal;
  }
  .bx-touch .bx_ordercart .bx_ordercart_order_table_container tbody td.price {
    margin-top: 11px !important;
    text-align: left;
    padding-top: 0;
  }
  .bx-touch .bx_ordercart .bx_ordercart_order_table_container tbody td.price .current_price {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 18px;
    margin-right: 12px;
  }
  .bx-touch .bx_ordercart .bx_ordercart_order_table_container tbody td.price .old_price {
    margin-left: 0px;
  }
  .bx-touch .bx_ordercart .bx_sort_container {
    margin: 0 0 20px;
  }
  .bx-touch .bx_ordercart .bx_sort_container a {
    float: left;
    width: 100%;
    display: block;
    position: relative;
    margin: 0 0 -1px 0;
    padding: 17px 20px 16px;
    background: #fbfbfb !important;
    border: 1px solid #f3f3f3;
    border-bottom: 1px solid #f3f3f3;
    font-size: 15px;
    font-weight: 500;
    color: #777777;
    border-radius: 0;
    line-height: 20px;
    text-decoration: none;
  }
  .bx-touch .bx_ordercart .bx_sort_container a.current,
  .bx-touch .bx_ordercart .bx_sort_container a.current:hover {
    padding-top: 20px;
    margin-top: -3px;
    text-shadow: none;
    color: #333;
    border: 1px solid #f3f3f3;
    box-shadow: none;
    border-radius: 0;
    cursor: default;
    background: #fff !important;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.item .bx_ordercart_itemtitle {
    padding-right: 0;
  }
  .bx-touch .bx_ordercart .bx_ordercart_order_table_container tbody td.price .type_price,
  .bx-touch .bx_ordercart .bx_ordercart_order_table_container tbody td.price .type_price_value {
    display: block;
  }

  /* ORDER */
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-img-block {
    float: none;
    margin: 0 0 15px;
  }
  #bx-soa-order .bx-soa-item-tr {
    padding-left: 20px;
  }

  /* PERSONAL SECTION */
  .sale-personal-section-index-block {
    margin-bottom: 16px;
    height: calc(100% - 16px);
    padding-bottom: 0;
  }
  .sale-personal-section-index-block-link {
    padding: 21px 15px !important;
  }
  .sale-personal-section-index-block-name {
    font-size: 15px;
    line-height: 22px;
    margin: 4px 0 3px;
  }
}
@media (max-width: 991px) and (min-width: 501px) {
  .top_big_banners > .row > div.col-md-3 .item {
    width: 50%;
    float: left;
  }
  .top_big_banners > .row > div.col-md-3.col-m-20,
  .top_big_banners > .row > div.col-md-3.col-m-60 {
    width: 100%;
    float: none;
  }

  .review-detail .reviews.item-views .item .top_wrapper .image + .top-info {
    padding-left: 110px;
  }
}
@media (max-width: 991px) and (min-width: 601px) {
  .catalog_item.big .image_wrapper_block {
    padding-top: 60%;
  }

  .catalog_block .catalog_item.big .footer_button {
    position: absolute;
    padding: 0;
    left: 25px;
    right: 24px;
    top: calc(100% + 2px);
    background-color: transparent;
    -webkit-transition: all 0.1s ease;
    -moz-transition: all 0.1s ease;
    transition: all 0.1s ease;
  }
  .catalog_block .catalog_item.big .footer_button .counter_block.big,
  .catalog_block .catalog_item.big .footer_button .button_block {
    height: 56px;
  }
}

@media (min-width: 601px) {
  body .compact-catalog .ce_cmp_visible {
    display: none;
  }
  .compact-catalog .catalog_item.big:not(.product_image) .item-title a,
  .compact-catalog .catalog_item.big:not(.product_image) .item-title a span {
    color: #fff;
  }
  .compact-catalog .catalog_block .catalog_item.big:not(.product_image) .cost.prices .price,
  .compact-catalog .catalog_block .catalog_item.big:not(.product_image) .cost.prices .price span {
    color: #fff;
  }

  .mobile .wrapper1.compact-catalog .catalog_block .catalog_item.big .footer_button {
    position: absolute;
    height: auto;
    bottom: 1px;
    top: auto;
    padding: 0;
    left: 24px;
    right: 23px;
  }
  .mobile .wrapper1.compact-catalog .catalog_block .catalog_item.big {
    padding-bottom: 56px;
  }
}

@media all and (max-width: 500px) {
  .col-12--500 {
    width: 100%;
  }

  #mobileheader .wrap_icon {
    padding-right: 10px;
    padding-left: 10px;
  }

  #mobileheader .wrap_icon.wrap_phones{
    display: none;
  }

  #mobileheader .basket-link,
  #mobileheader .mobileheader-v2 .right-icons .wrap_basket .basket-link.basket {
    padding-left: 7px;
    padding-right: 7px;
  }
  body #mobileheader .wrap_icon.wrap_basket {
    padding-left: 3px;
  }
  #mobileheader .right-icons {
    padding-right: 7px;
  }
  #mobileheader .mobileheader-v4 .logo-block {
    width: calc(100% - 135px);
  }

  body #mobileheader .basket-link .js-basket-block .count {
    top: 25px;
    left: 13px;
    min-width: 16px;
    height: 15px;
    line-height: 16px;
    font-size: 0.667em;
    padding: 0 4px;
    right: unset;
    width: unset;
  }
  body #mobileheader .basket-link .js-basket-block {
    padding: 0;
  }

  .stores_block_wrap .stores_block .stores_text_wrapp .main_info {
    max-width: 200px;
  }
  .module_products_list .counter_wrapp .counter_block {
    display: none;
  }
  body .catalog_block .catalog_item_wrapp,
  .wrapper_inner #content .catalog_block .catalog_item_wrapp:nth-child(3n),
  .wrapper_inner #content .catalog_block .catalog_item_wrapp:nth-child(4n),
  body .right_block.catalog .catalog_block .catalog_item_wrapp {
    width: 100%;
  }
  .wrapper_inner .catalog_block .image_wrapper_block {
    margin: 0px auto 12px;
  }
  body .wrapper1.compact-catalog .catalog_block .item_info {
    /*margin:auto;*/
    width: auto;
  }
  .info_item .top_info .article + .brand {
    padding-left: 0px;
    display: block;
  }
  .wrapper_inner .footer_bottom .submenu_top .menu_item {
    width: 100%;
  }
  .wrapper_inner .articles-list .item .right-data .preview-text {
    padding-right: 17px;
  }
  .basket_fly .wrapper_inner .basket_wrapp .wrapp_all_icons {
    width: 143px;
    overflow: hidden;
  }
  .wrapper_inner .basket_wrapp .header-compare-block {
    margin-left: 3px;
  }
  #header .wrapper_inner .middle-h-row .logo_wrapp .logo {
    padding-right: 0px;
  }
  .catalog_block .catalog_item {
    margin-right: 0px;
  }
  .item-name-cell .item-stock,
  .item-name-cell .rating {
    display: none;
  }
  .wrapper_inner .cost.prices .price {
    font-size: 16px;
  }
  .wrapper_inner .cost.prices .js-info-block .price {
    font-size: 13px;
  }
  .adaptive.more_text {
    display: block;
  }
  .wrapper_inner .data-table.top .left_blocks {
    width: 100%;
  }
  .wrapper_inner .data-table.top .right_blocks {
    display: none;
  }
  .subscribe-edit .more_text .more_text_small {
    padding: 0px 0px 10px;
  }
  td.note {
    padding: 0px 0px 0px 10px;
  }
  td.text_info {
    display: none;
  }

  .wrapper1:not(.compact-catalog)
    .wrapper_inner
    .catalog_block
    .item.big
    .catalog_item.big:not(.product_image)
    .image_wrapper_block {
    margin-bottom: 50px;
  }
  .reviews-reply-field-captcha,
  .blog-comment-fields .captcha-row {
    max-width: initial;
  }
  .reviews-reply-field-captcha > div,
  .reviews-reply-field-captcha > div + div,
  .blog-comment-fields .captcha-row > div,
  .blog-comment-fields .captcha-row > div + div {
    width: 100%;
  }
  .reviews.item-views .item .image {
    float: none !important;
  }
  .review-detail .reviews.item-views .item .header-block,
  .review-detail .reviews.item-views .item .bottom-block {
    padding-left: 20px;
    padding-right: 20px;
  }

  .wrapper_inner .module-order-history .module-orders-list .drop-cell .not-payed .text,
  .wrapper_inner .module-order-history .module-orders-list .drop-cell .not-payed .pays {
    width: 100%;
    text-align: left;
  }
  .wrapper_inner .module-order-history.orderdetail .result-row a.button {
    margin-bottom: 10px;
  }
  .module-order-history.orderdetail .module-orders-list.result td.custom_t2 {
    padding-right: 0px;
  }
  .lk-page .iblock.label_block,
  .lk-page .iblock.text_block {
    width: 100%;
    padding-left: 0px;
  }

  .bx_compare .data_table_props tr td:first-of-type {
    width: 100px;
    min-width: 100px;
    font-size: 10px;
  }
  .bx_compare .frame.props .wraps {
    margin-left: -100px;
  }
  .prop_title_table {
    width: 101px;
  }
  .bx_compare .frame {
    margin-left: 100px;
  }
  .wrapp_scrollbar {
    margin-left: 30px;
    margin-right: 30px;
  }
  .slider_navigation.compare .flex-direction-nav {
    display: block !important;
  }
  ul.slider_navigation.compare .flex-nav-prev {
    left: -30px;
  }
  ul.slider_navigation.compare .flex-nav-next {
    right: -30px;
  }

  .top_big_banners > .row > div {
    width: 100%;
  }
  .top_big_banners .col-m-20 .item {
    display: block;
    width: 100%;
  }

  .middle_phone .phone_wrap .icons {
    display: none;
  }
  .middle_phone .phone_wrap .phone_text {
    padding-left: 20px;
  }
  .middle_phone .phones .phone_text a {
    font-size: 14px;
  }
  .basket_fly #header .middle-h-row .basket_wrapp .middle_phone {
    min-width: 100%;
  }

  .rows_block .block_list .item_block {
    width: 100%;
  }

  .blog_wrapper.blog .items > .row > div {
    width: 100%;
    float: none;
  }

  .item-stock.js-show-stores .value {
    border-bottom-color: transparent;
  }

  /* tizers block */
  .tizers_block {
    text-align: center;
    padding: 0px;
  }
  .tizers_block .row > div {
    vertical-align: top;
  }
  .wrapper_inner .tizers_block .item {
    margin: 0 0 20px 0;
  }
  .wrapper_inner .tizers_block .item:last-child .title {
    width: auto;
  }
  .tizers_block .item .img,
  .tizers_block .item .title {
    display: block;
    text-align: center;
  }
  .tizers_block .item .img {
    margin: 0 auto 10px;
    height: 80px;
    vertical-align: middle;
    text-align: center;
    padding: 0;
    width: 80px;
    line-height: 77px;
    max-width: none;
  }
  .tizers_block .item .title {
    width: auto;
    padding: 0px;
  }
  .projects .tizers_block .item .img {
    height: auto;
    line-height: normal;
  }

  /*personal*/
  .personal_wrapper .orders_wrapper .sale-order-list-inner-row .sale-order-list-inner-row-body > div {
    width: 100%;
    float: none;
    text-align: left;
  }
  .personal_wrapper .orders_wrapper .sale-order-list-button,
  .personal_wrapper .orders_wrapper .sale-order-detail-about-order-inner-container-repeat > a,
  .sale-order-detail-payment-options-methods-button-element,
  .sale-order-detail-payment-options-methods-button-element-new-window {
    float: none;
  }

  /*responsive table*/
  .responsive tr td {
    padding-left: 18px;
    padding-right: 18px;
  }

  .compact-catalog .ajax_load .cost.prices .price {
    white-space: nowrap;
  }
  .compact-catalog .ajax_load .icons-basket-wrapper + div,
  .compact-catalog .ajax_load .icons-basket-wrapper + div + div {
    padding: 0px;
  }
  .bx_item_list_you_looked_horizontal.detail .image_wrapper_block .like_icons span {
    opacity: 1;
    visibility: visible;
  }

  /*order*/
  body #bx-soa-order-form .bx-soa-pickup-list-item .bx-soa-pickup-l-item-detail {
    padding-right: 0px;
  }
  body #bx-soa-order-form .bx-soa-pickup-list-item.bx-selected .bx-soa-pickup-l-item-btn {
    position: static;
    padding-left: 0px;
  }
}
@media all and (max-width: 470px) {

  .rating-mobile-price-total {
    display: block;
    height: 20px;
  }

  .like_icons.block {
    bottom: -36px;
    top: auto;
    right: -8px;
  }

  .rating-mobile-prices {
    display: flex;
    flex-direction: column;
    row-gap: 5px;
  }

  .rating-mobile-price-weight {
    font-family: Montserrat;
    font-weight: 400;
    font-size: 12px;
    line-height: 100%;
    color: #333333;
  }

  .rating-mobile-price-weight.min-text-price {
    font-family: Montserrat;
    font-weight: 500;
    font-size: 12px;
    line-height: 100%;
    text-decoration: line-through;
    color: #828282;
  }

  .rating-mobile-price-weight-min {
    font-family: Montserrat;
    font-weight: 400;
    font-size: 14px;
    line-height: 100%;
    color: #DB3636;
  }

  .min-text-price-red span{
    font-family: Montserrat;
    font-weight: 700;
    font-size: 14px;
    line-height: 100%;
  }

  .weight-min-bold-text {
    font-family: Montserrat;
    font-weight: 700;
    font-size: 14px;
    color: #333333;
  }

  .rating-mobile-star {
    position: absolute;
    bottom: 181px;
    left: 24px;
    display: flex;
    align-items: center;
    padding: 3.5px;
    background: #FFFFFF;
    border-radius: 4px;
    column-gap: 5px;
    height: 24px;
  }

  .rating-mobile-star svg {
    width: 12px;
    height: 12px;
  }

  .compact-catalog .ajax_load .rating {
    display: none;
  }

  .logo svg {
    max-width: 100%;
  }

  .phones .order_wrap_btn {
    display: none;
  }
  .top-h-row .phones {
    width: 48%;
  }
  .button.video::before {
    display: none;
  }
  .wrapp_all_inputs.wrap_md .iblock,
  .filter_horizontal .wrapp_all_inputs.wrap_md .wrapp_change_inputs,
  .filter_horizontal .wrapp_all_inputs.wrap_md .wrapp_slider {
    width: 100%;
  }
  .wrapp_all_inputs.wrap_md .wrapp_slider,
  .filter_horizontal .wrapp_all_inputs.wrap_md .wrapp_slider {
    padding: 0px;
  }
  .filter_horizontal .wrapp_all_inputs.wrap_md .wrapp_slider {
    padding-top: 15px;
  }
  .smartfilter .bx_ui_slider_track {
    margin-top: 33px;
  }
  .top-h-row .phones .order_wrap_btn {
    display: none;
  }
  .jobs_wrapp .item .name .title .salary {
    display: block;
  }
  .jobs_wrapp .item .name .salary_wrapp .salary {
    display: none;
  }
  .job.border_block .wrap_md .text {
    width: 100%;
    display: block;
    text-align: left;
    padding: 0px 0px 0px 0px;
  }
  .job.border_block .wrap_md .phone {
    width: 100%;
    display: block;
    text-align: left;
    padding: 20px 0px 0px 0px;
  }
  .popup.show .forgot {
    float: none;
    display: block;
    margin-left: 0px;
  }
  .wrapper_inner .module-form-block-wr.order_cancel .form-block {
    padding: 0px;
  }
  .top_slider_wrapp.view_1 .slides .banner_title .sale_block {
    display: block;
  }
  .top_slider_wrapp.view_1 .slides .wraps_buttons .wrap {
    height: 32px;
    width: 32px;
  }
  body .top_slider_wrapp.view_1 .banner_title .prices {
    margin-top: 12px;
  }
  .cart_empty .text > *:not(.title):not(.button) {
    display: none;
  }
}
@media all and (max-width: 460px) {
  .shops.list .item .title_metro,
  .shops.list .item .schedule_phone_email {
    width: 100%;
    margin-top: 0;
  }
  .shops.list .item .schedule_phone_email {
    text-align: left;
  }
  .shops.list .item .rubber > div {
    padding-top: 0;
  }
  .button.faq_button {
    float: none;
  }
  .faq_desc {
    margin: 23px 0 0 0;
  }
}
@media all and (max-width: 450px) {
  .product-container .content_wrapper_block.front_tizers > .maxwidth-theme .item-views.tizers .item {
    flex-wrap: wrap;
  }
  .product-container .item-views.tizers .items.small-block .item .image + .inner-text,
  .product-container .item-views.tizers .items.tops .item .image + .inner-text {
    padding-left: 0;
    width: 100%;
  }
  ul.tabs li {
    padding: 5px 0 0 0;
  }
  .stores_block_wrap .stores_block .item-stock {
    float: none;
    margin: 20px 0px 0px;
  }
  .stores_block_wrap .stores_block.wo_image .item-stock {
    position: initial;
    margin: 10px 0px 0px;
  }
  .stores_block_wrap .stores_block .stores_text_wrapp {
    display: block;
  }
  .stores_block_wrap .stores_block.w_image:before {
    display: none;
  }

  .right_info table.buttons_block .counter_block {
    padding: 8px 0 0 0 !important;
    display: block;
    text-align: center;
  }
  .right_info table.buttons_block .counter_block select {
    font-size: 24px;
    padding: 4px 20px;
    height: 39px;
    margin: 0 auto;
  }
  .right_info table.buttons_block .buy_buttons_wrapp {
    display: block;
    text-align: center;
  }

  /* banner adaptive */
  .top_slider_wrapp.view_1 .text .banner_title .head-title {
    font-size: 19px;
    line-height: 22px;
  }
  .top_slider_wrapp.view_2 .wrapper_video {
    height: 250px;
  }
  .top_slider_wrapp.view_2 .slides .banner_buttons.with_actions > .btn:last-of-type {
    margin: 12px;
  }

  body .top-h-row .phone span.phone_text a {
    font-size: 15px;
  }
  body .front_slider .preview_text {
    display: none;
  }
  body .filter_opener span {
    display: none;
  }
  .filter_opener i {
    margin: 0px;
  }
  .adaptive_filter {
    padding-right: 10px;
  }
  body .filter_opener {
    margin-right: 0px;
  }
  body .module_products_list .availability-row {
    display: none;
  }

  .popup .label_block_capcha {
    width: 100%;
  }
  .popup .img_block_capcha {
    width: 100%;
    padding-left: 0px;
  }
  .popup .img_block_capcha img {
    float: none;
  }
  .module-gallery-list li {
    width: 50%;
  }
  .wrapper_inner .specials_slider_wrapp ul.tabs > li span,
  .wrapper_inner .tab_slider_wrapp ul.tabs > li span {
    font-size: 12px;
  }
  .wrapper_inner .search-page-wrap .form-control {
    float: none;
    width: 100%;
    margin: 0px 0px 20px 0px;
  }
  .wrapper_inner .basket_wrapp .module-cart table.bottom.middle .bottom_btn .back_btn > div {
    margin: 20px 0px 0px;
  }
  .wrapper_inner .basket_wrapp .module-cart table.bottom.middle .bottom_btn .back_btn > div:first-child {
    margin-top: 0px;
  }
  body .wrapper_inner .module-cart table tfoot .backet_back_wrapp .back_btn > div {
    display: block !important;
  }
  .basket_wrapp .module-cart table.bottom.middle td.row_titles {
    margin-bottom: 0 !important;
  }
  .wrapper_inner .basket_wrapp .module-cart table.bottom.middle .top_total_row td.row_values {
    margin-bottom: 19px;
    padding: 0;
    margin-top: 0;
  }
  .wrapper_inner .staff.list .item {
    width: 100%;
    padding: 0px;
    text-align: center;
  }
  .wrapper_inner .staff.list .item .image {
    margin: auto;
  }
  .wrapper_inner .bottom.middle td > .coupon {
    width: 100%;
  }
  .wrapper_inner .bottom.middle td > .coupon #COUPON,
  .wrapper_inner .bottom.middle td > .coupon .coupon-t {
    width: 100%;
    float: none;
    text-align: left;
    display: block;
  }
  .bottom.middle td > .coupon #COUPON {
    max-width: 100%;
  }
  .wrapper_inner .bottom.middle td > .coupon .coupon-t {
    margin: 0px 0px 10px 0px;
  }
  .wrapper_inner .bottom.middle td > .coupon .coupon_wrap {
    display: block;
  }
  .coupon .coupons_list {
    padding: 0px;
  }
  .iblock .icon_error_block:after {
    display: none;
  }
  .filter_opener {
    padding: 11px 9px 2px;
    margin-top: 0px;
  }

  .basket_wrapp .header-compare-block,
  .basket_wrapp .wraps_icon_block {
    margin: 0px 30px 0px 0px;
  }

  .module-cart table.bottom td {
    display: block;
    float: none !important;
    margin-bottom: 18px !important;
    overflow: hidden;
    padding: 0 !important;
    text-align: left !important;
    width: 100% !important;
  }
  .module-cart table.bottom td .description {
    display: none;
  }

  .module_products_list td.price-cell,
  .module_products_list td.item-name-cell,
  .module_products_list td.foto-cell {
    display: block;
    width: 100%;
    text-align: center;
    margin: auto;
  }
  .module_products_list .image_wrapper_block {
    margin: auto;
  }

  .section_block .sections_wrapper .list .row > div {
    width: 100%;
    float: none;
  }

  body .sections_wrapper .list .item.section_item .img {
    float: none;
  }
  body .sections_wrapper .list .item.section_item .section_info {
    padding: 0px;
  }

  .col-450xs {
    width: 100%;
  }

  .bx_item_list_you_looked_horizontal.col3 .bx_catalog_item {
    width: 97.3333% !important;
  }
  .bx_item_list_you_looked_horizontal.col3 .bx_catalog_item:nth-child(2n + 1) {
    clear: none !important;
  }

  /* ORDER */
  #bx-soa-order .bx-soa-pp-desc-container .bx-soa-pp-company-graf-container {
    float: none;
  }
  #bx-soa-order .bx-soa-pp-desc-container .bx-soa-pp-company .bx-soa-pp-company-desc {
    margin-top: 15px;
    padding-left: 0;
  }
  #bx-soa-order .bx-soa-coupon-label {
    width: auto;
    display: block;
    margin-top: 0;
    margin-bottom: 4px;
  }
  #bx-soa-order .bx-soa-coupon label {
    width: auto;
  }
  #bx-soa-order .bx-soa-coupon-block {
    display: block;
  }
  #bx-soa-order .bx-soa-coupon-input {
    width: auto;
  }
  #bx-soa-order .bx-soa-section .bx-soa-section-title {
    padding-right: 0;
  }
  #bx-soa-order .bx-soa-section .bx-soa-section-title-container .text-right {
    padding: 0;
    text-align: left;
  }
  #bx-soa-order .bx-soa-section .bx-soa-section-title-container .col-sm-9 {
    width: 100%;
    float: none;
    text-align: left;
  }
  #bx-soa-order .bx-soa-section .bx-soa-section-title-container .col-sm-3 {
    width: 100%;
    float: none;
  }
  #bx-soa-order .bx-soa-editstep {
    margin-top: 5px;
  }
  #bx-soa-order #bx-soa-basket .bx-soa-item-table .bx-soa-item-tr .bx-soa-item-td.bx-soa-item-properties {
    width: 100% !important;
  }
  #bx-soa-order .form {
    text-align: left;
  }

  body .bx-soa-pickup-l-item-btn {
    margin-top: 11px;
    position: static;
  }
  body .bx-soa-pickup-list-item.bx-selected .bx-soa-pickup-l-item-btn {
    padding-left: 0px;
  }

  /*personal*/
  .sale-acountpay-block .sale-acountpay-pp div .sale-acountpay-pp-company > div {
    display: block;
    margin: 0px auto 10px;
    padding: 0px;
    text-align: center;
  }
  .personal_wrapper .row > .col-sm-12 {
    width: 100%;
  }
  .personal_wrapper .row.sale-personal-section-row-flex > .col-xs-6 {
    width: 50%;
  }
}
@media all and (max-width: 400px) {
  #bx-soa-order #bx-soa-auth .filter .forgot {
    float: none !important;
    margin: 10px 0 -10px;
    display: block;
  }
  .wrapper_inner .stores_wrapp .stores_block_wrap .stores_block .stores_text_wrapp {
    font-size: 11px;
  }
  .wrapper_inner .stores_wrapp .stores_block_wrap .stores_block .item-stock {
    padding: 0px 5px 0px 20px;
  }
  .wrapper_inner .info_item .middle_info .buy_block .counter_wrapp {
    white-space: nowrap;
  }
  .wrapper_inner .cost.prices .price,
  .module_products_list td.price-cell .cost.prices .price {
    font-size: 14px;
  }
  .wrapper_inner .cost .price.discount strike {
    font-size: 12px;
  }

  .footer_bottom_inner .rows_block .menus .submenu,
  .footer_bottom_inner .rows_block .menus .rows_block {
    display: none;
  }
  .footer_bottom_inner .rows_block .menus .submenu_top.rows_block {
    display: block;
  }
  .footer_bottom_inner .menus .rows_block .col-3 {
    width: 100%;
    text-align: center;
  }

  .wrapper_inner .phones .order_wrap_btn {
    display: none;
  }
  .basket_fly #header .middle-h-row .basket_wrapp .middle_phone {
    top: initial;
  }

  .top-h-row .h-user-block a.icon {
    padding: 0px 7px 0px 20px;
  }
  .top-h-row .h-user-block {
    margin-top: 8px;
  }
  .top-h-row .phones {
    width: auto;
  }

  .top-h-row .phone_block .phone_wrap .icons {
    display: none;
  }
  .top-h-row .phone_block .phone_wrap .phone_text {
    padding-left: 0px;
  }
  .top-h-row .bg_user {
    display: none;
  }
  body #header .middle-h-row td.logo_wrapp {
    width: 47%;
  }
  .wrapper_inner .basket_wrapp .header-compare-block {
    margin-right: 20px;
  }

  .top_slider_wrapp.hidden_narrow {
    display: none;
  }
  #mobileheader .logo,
  #mobileheader .logo svg {
    max-width: 112px;
  }

  .sections_wrapper .list .item:not(.section_item) .img {
    width: auto;
  }
  .sort_display a {
    width: 33px;
    margin-left: 5px;
  }
  .list_item .image_wrapper_block,
  .list_item .image_wrapper_block a {
    width: auto;
  }

  .news_akc_block .items .item_block {
    width: 100%;
    float: none;
  }
  body
    .bx_filter_vertical
    .bx_filter_section
    .bx_filter_button_box.active
    .bx_filter_block
    .bx_filter_parameters_box_container {
    text-align: center;
    white-space: nowrap;
  }
  body
    .bx_filter_vertical
    .bx_filter_section
    .bx_filter_button_box.active
    .bx_filter_block
    .bx_filter_parameters_box_container
    > * {
    margin-top: 10px;
  }
  .popup .form .form_head {
    padding-right: 50px;
    padding-left: 30px;
  }
  .popup .form .form_body,
  .popup .form .form_footer {
    padding-right: 30px;
    padding-left: 30px;
  }

  .catalog_item.big .icons-basket-wrapper {
    display: none;
  }
  .catalog_item.big .icons-basket-wrapper + div + div {
    padding: 0px;
  }

  /*scrollbar*/
  body .horizontal-scrolling .mCSB_scrollTools .mCSB_buttonLeft {
    left: -16px;
  }
  body .horizontal-scrolling .mCSB_scrollTools .mCSB_buttonRight {
    right: -14px;
  }

  /*basket fly*/
  .basket_fly .header_wrap {
    position: relative;
  }
  .basket_fly #header .middle-h-row {
    position: static;
  }
  .basket_fly .header_wrap #header > .wrapper_inner {
    padding-top: 60px;
  }
  .basket_page.basket_fly .header_wrap #header > .wrapper_inner,
  .order_page.basket_fly .header_wrap #header > .wrapper_inner {
    padding-top: 16px;
  }
  body #header .basket_fly {
    top: 0px;
    height: 46px;
    width: 100%;
    left: 0px;
    right: auto;
    position: absolute;
    box-shadow: none;
    min-height: inherit;
  }
  #header .basket_fly .opener {
    width: 100%;
    left: 0px;
    border-radius: 0px;
    position: static;
  }
  #header .basket_wrapp .basket_sort,
  .basket_fly #header .middle-h-row #basket_form {
    display: none;
  }
  #header .basket_fly .opener > div {
    width: 25%;
    float: right;
    height: 46px;
  }
  .basket_fly .basket_wrapp .basket_fly .wraps_icon_block {
    top: 32%;
    margin: 0px;
  }
  .basket_wrapp .wraps_icon_block.compare:before {
    top: -4px;
  }
  .basket_wrapp .wraps_icon_block.delay:before {
    top: -1px;
  }
  .basket_wrapp .wraps_icon_block.basket:before {
    top: -1px;
  }
  .basket_wrapp .wraps_icon_block.user_reg:before {
    top: -3px;
  }
  #header .basket_fly .opener > div:after {
    width: 1px;
    height: 100%;
  }
  .basket_wrapp .wraps_icon_block .count {
    top: -9px;
  }
  .basket_wrapp .wraps_icon_block.user_auth.w_img {
    top: 7px !important;
  }
  .basket_wrapp .wraps_icon_block.user_auth.no_img {
    top: 18px !important;
  }

  /*basket*/
  .bx_order_list .bx_order_list_table_order > tbody > tr > td:not(.img):not(.name) {
    width: 100%;
    float: none;
  }
  .bx_ordercart .bx_ordercart_coupon span {
    float: none;
    width: auto;
    margin: 0 0 6px;
  }
  .bx_ordercart.bx_blue .bx_bt_button {
    top: 21px;
  }
  .bx_ordercart .bx_ordercart_order_table_container table tbody td.item .bx_ordercart_itemtitle {
    padding-right: 0;
  }
  .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr > td:not(.item):not(.itemphoto):not(.margin) {
    margin-top: 10px !important;
    margin-right: 0;
    width: 100%;
  }
  .bx_ordercart .bx_ordercart_order_table_container > table > tbody > tr > td.control {
    position: absolute;
    top: 0;
    right: 0;
  }
  .bx_ordercart .bx_ordercart_coupon #coupon {
    float: none;
  }
  .bx-touch .bx_ordercart .bx_sort_container {
    margin: 0 0 20px;
  }
  .bx-touch .bx_ordercart .bx_ordercart_order_table_container table tbody tr td.item {
    padding-bottom: 0;
  }
  .bx_ordercart .bx_ordercart_coupon .input {
    padding: 0px;
  }
  .bx_ordercart .bx_ordercart_coupon:not(:first-of-type) {
    margin-left: 0px;
  }
  .bx_ordercart .bx_ordercart_order_sum {
    max-width: 295px;
  }
  #basket-root .basket-checkout-block-btn .icon_error_block {
    padding-right: 0px;
    line-height: 20px;
    margin-top: 22px;
  }

  /*ORDER*/
  #bx-soa-order .bx-soa-pp-item-container .bx-soa-pp-company-graf-container {
    display: block;
    margin: 0px 0px 10px;
    height: auto;
  }
  #bx-soa-order .bx-soa-pp-item-container .bx-soa-pp-company-smalltitle {
    display: block;
    padding: 0px;
  }
  #bx-soa-pickup .bx-soa-section-content .bx-soa-pickup-preview-img {
    float: none;
    float: none;
    display: block;
    margin: 0px 0px 10px;
  }
  .bx-soa-pp-company-graf-container .bx-soa-pp-delivery-cost {
    position: static;
  }

  /*personal*/
  .personal_wrapper
    .orders_wrapper
    .sale-order-payment-change-pp-list
    .sale-order-payment-change-pp-company
    .sale-order-payment-change-pp-company-smalltitle {
    font-size: 13px;
  }

  /*catalog compact list*/
  .compact-catalog .display_list .list_item .show_props,
  .compact-catalog .js-info-block {
    display: none;
  }
  .compact-catalog .ajax_load .item .catalog_item > div {
    padding-bottom: 34px;
  }
  .compact-catalog .ajax_load .item .catalog-adaptive {
    padding-bottom: 55px;
  }

  .compact-catalog .catalog_block .catalog_item > div .item_info {
    padding-bottom: 30px;
  }
}
@media all and (max-width: 380px) {
  .pk-page .form-control.captcha-row {
    display: flex;
  }
  .pk-page .captcha-row .captcha_input {
    left: initial;
    width: 100%;
  }
  .pk-page .form-control.captcha-row .captcha_image {
    position: relative !important;
    width: 100%;
    left: initial;
    right: initial;
    max-width: initial;
    margin-top: 10px;
  }
  .pk-page .captcha-row .captcha_input label.error {
    right: 0;
  }
  /* .auth-page .prompt.remember {
    float: none !important;
    width: 100%;
  }
  .auth-page .forgot {
    float: none !important;
    margin: 10px 0 0 0;
    display: inline-block;
  } */

  /*.top-h-row .phone_wrap .icons, */
  .top-h-row .h-user-block .have-user a.reg:before {
    display: none;
  }
  .wrapper_inner .articles-list .item .right-data {
    margin: 0px;
  }
  .wrapper_inner .articles-list .item .left-data {
    float: none;
    margin: 0px auto 20px;
  }
  .wrapper_inner .sale_block .text,
  .wrapper_inner .sale_block .value,
  .bx_ordercart table.colored thead td {
    font-size: 10px;
  }
  .wrapper_inner #order_form_content .module-cart table td.thumb-cell {
    padding-left: 5px;
  }
  .basket_wrapp .module-cart .cost.prices .sale_block {
    display: none;
  }
  .basket_wrapp .module-cart .cost-cell .cost.prices {
    white-space: normal;
  }

  /* stat promo float banners */
  .wrapper_inner .start_promo .wrap_tizer {
    bottom: 8px;
  }
  .wrapper_inner .start_promo .wrap_tizer .wrapper_inner_tizer {
    padding: 0 20px 0 8px !important;
  }
  .wrapper_inner .wrap_tizer .wr_block .title .inner_text {
    font-size: 13px;
    font-weight: 600;
    top: 2px;
  }
  .wrapper_inner .wrap_tizer .wr_block.price .inner_text {
    font-size: 12px;
  }
  .wrap_tizer .wrap_outer {
    padding: 0px 4px 5px;
  }
  .wrapper_inner .wrap_tizer .wrap_outer .outer_text {
    left: 6px;
    padding: 0px 4px 5px 0px;
  }
  .wrapper_inner .wrap_tizer .wrap_outer .inner_text {
    left: -3px;
  }

  .wrapper_inner .wrap_tizer .wr_block.price .wrap_outer,
  .wrapper_inner .wrap_tizer .wr_block.price .wrap_outer .inner_text {
    padding: 1px 0px 5px;
  }
  .wrapper_inner .wrap_tizer .wr_block.price .wrap_outer .outer_text {
    padding-top: 1px;
  }

  .wrap_tizer .wr_block {
    line-height: 18px;
  }
  .wrap_tizer .wrap_outer.title {
    font-size: 15px;
  }

  .bx-firefox #header .middle-h-row td.logo_wrapp .logo,
  .bx-ie #header .middle-h-row td.logo_wrapp .logo {
    display: inline;
  }
  .bx-firefox #header .middle-h-row td.logo_wrapp {
    width: 53%;
  }
  .bx-ie #header .middle-h-row td.logo_wrapp {
    width: 48%;
  }

  .tabs-body {
    padding-top: 30px;
  }

  .catalog_detail .element_detail_text .price_txt {
    display: none;
  }
  .catalog_detail .element_detail_text {
    margin: 0px 0px 10px;
  }

  body .top_mobile_region {
    text-align: center;
    background: var(--black_bg_black);
    box-shadow: inset 0 -1px 0 0 #f5f5f5;
  }
  body .top_mobile_region .confirm_wrapper {
    display: inline-block;
    margin: 0px auto;
    position: relative;
  }

  body .top_mobile_region .confirm_region {
    padding-left: 15px;
    padding-right: 15px;
    text-align: left;
    background: none;
    box-shadow: none;
  }
  body .top_mobile_region .confirm_region .title {
    max-width: 240px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  body .top_mobile_region .confirm_region .buttons {
    margin: 0px -4px;
  }
  body .top_mobile_region .confirm_region .buttons .btn {
    padding-left: 15px;
    padding-right: 15px;
  }
  .top_mobile_region .confirm_region + .close_popup {
    right: 16px;
  }
}
@media all and (max-width: 360px) {
  /* .auth-page .form_footer .btn {
    float: none !important;
  } */

  #mobileheader .mobileheader-v2 .wrap_icon {
    padding-right: 3px;
    padding-left: 3px;
  }
  #mobileheader .mobileheader-v2 .right-icons .wrap_basket .basket-link.basket {
    padding-left: 3px;
    padding-right: 3px;
  }

  .wrapper_inner .bx_sort_container .wrap_remove_button {
    top: 110px;
  }
  .catalog_detail .w_icons.button.to-cart {
    padding-left: 20px;
  }
  .catalog_detail .w_icons.button.to-cart:before {
    display: none;
  }
  .catalog_detail .item_main_info .right_info .info_item {
    padding-right: 12px;
  }
  .container > h1 {
    padding-right: 0px;
  }

  /*filter*/
  .show-normal-sort .dropdown-select .dropdown-select__title {
    white-space: nowrap;
    overflow: hidden;
    max-width: 170px;
    text-overflow: ellipsis;
    position: relative;
    padding-right: 10px;
  }
  .show-normal-sort .dropdown-select .dropdown-select__title .svg {
    position: absolute;
    right: 0px;
    top: 50%;
    margin-top: -1px;
  }
  .show-normal-sort .dropdown-select .dropdown-menu-wrapper {
    left: auto;
    right: 0px;
  }
  /**/

  /*personal*/
  .personal_wrapper
    .sale-order-detail-order-item-td
    .sale-order-detail-order-item-block
    .sale-order-detail-order-item-content {
    padding-left: 10px;
    padding-right: 10px;
  }
}
@media all and (max-width: 340px) {
  #mobileheader .wrap_icon {
    padding-right: 5px;
    padding-left: 5px;
  }
  #mobileheader .basket-link {
    padding-left: 5px;
    padding-right: 5px;
  }
}
@media all and (max-width: 320px) {
  #mobileheader .wrap_icon {
    padding-right: 3px;
    padding-left: 3px;
  }
  #mobileheader .basket-link {
    padding-left: 3px;
    padding-right: 3px;
  }

  .shops.list .item .image {
    display: none;
  }
  .shops.list .item .rubber {
    margin: 0;
  }

  .module_products_list .adaptive_button {
    display: block;
  }
  body .module_products_list td.but-cell {
    display: none;
  }
  .basket_fly #header .middle-h-row .basket_wrapp {
    padding-top: 0px;
  }
  #header .wrapper_inner .middle-h-row .logo_wrapp .logo {
    min-height: 24px;
    line-height: 24px;
    display: table-cell;
  }
  #header .middle-h-row .logo_wrapp .logo a {
    display: block;
  }
  .basket_wrapp .header-compare-block,
  .basket_wrapp .wraps_icon_block {
    margin: 0px 25px 0px 0px;
  }
}

@media all and (min-width: 320px) {
  .search .dropdown-menu-wrapper {
    min-width: 232px;
  }
}

@media all and (min-width: 1180px) {
  .specials_slider_wrapp ul.tabs_content,
  .tab_slider_wrapp ul.tabs_content {
    margin: 0px;
    padding: 0px;
  }
  .specials_slider_wrapp ul.tabs_content li.tab,
  .tab_slider_wrapp ul.tabs_content li.tab {
    margin: 0px;
    padding: 0px;
  }

  /* news slider front page */
  .news_slider_wrapp {
    margin: -13px -13px 0;
    padding: 13px 13px 0;
  }
  .news_slider_wrapp .flex-viewport {
    margin: -13px -13px 0;
    padding: 13px 13px 0;
  }
  .news_slider_wrapp .news_slider li:hover {
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
    -o-box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
  }
}

/*order*/
@media only screen and (max-width: 1174px) {
  #bx-soa-order .bx-soa-pp-desc-container .bx-soa-pp-company-graf-container {
    float: none;
  }
  #bx-soa-order .bx-soa-pp-desc-container .bx-soa-pp-company .bx-soa-pp-company-desc {
    margin-top: 15px;
    padding-left: 0;
    margin-bottom: 10px;
  }
}
