.bonus-system-block,
.bonus_item_cart,
.lt_cart_bonus_all {
  font-size: 0.8rem;
  line-height: 1rem;
  position: relative;
  padding: 4px 0 0 14px;
  width: max-content;
}

.bonus-system-block:empty,
.bonus_item_cart:empty,
.lt_cart_bonus_all:empty {
  display: none;
}

.bonus-system-block:after,
.bonus_item_cart:after,
.lt_cart_bonus_all:after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 3px;
  bottom: 0;
  width: 11px;
  background: url('../images/svg/bonus_icon.svg') center no-repeat;
}

.catalog_item.big .bonus-system-block {
  color: #fff;
}

.catalog_item.big .bonus-system-block:not(:empty):after {
  background: url('../images/svg/bonus_icon_white.svg') center no-repeat;
}

.services_buy_block .bonus-system-block {
  margin-left: 9px;
}

.body-info__bottom .sale_block~.bonus-system-block {
  margin-top: 5px;
}

.prices-services-detail .bonus-system-block {
  margin-left: 8px;
}

.bonus_item_cart {
  margin-bottom: 5px;
}

.lt_cart_bonus_all,
.basket_fly .items_wrap .bonus-system-block,
.basket_hover_block  .bonus-system-block{
  margin-left: auto;
}

#bx-soa-order .bx-soa-cart-total .bx-soa-cart-total-line [id^="bonus"] > span {
  background: var(--card_bg_black);
}
.bx-soa-section-content.lt_bonus_cont_success{
  background-color: var(--black_bg_black);
  padding-top: 26px;
}
.bonus_comment_min_max{
  color: var(--white_text_black);
}
#bonus_payment_block .bx-soa-coupon-input.lt_no_arrow:before {
	display:none;
}
#bonus_payment_block #bonus_payfield_block .bx-soa-coupon-input{
  display: inline-block;
  vertical-align: baseline;
  overflow: visible;
}
#bonus_payment_block #bonus_payfield_block .bx-soa-coupon-input input{
  margin-bottom: 10px;
}
/* @media (max-width: 500px) {
  .compact-catalog .bonus-system-block {
    font-size: 0.7333rem;
    line-height: 0.9333rem;
  }
} */