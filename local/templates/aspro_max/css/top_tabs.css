.tab_slider_wrapp .top_block {
  margin-bottom: 36px;
  display: flex;
  justify-content: space-between;
}
.tab_slider_wrapp .top_block h3 {
  flex-shrink: 0;
}
.tab_slider_wrapp .top_block .right_block_wrapper {
  white-space: nowrap;
  margin-bottom: -7px;
  padding-top: 7px;
}
.tab_slider_wrapp .top_block .right_block_wrapper > a {
  top: 5px;
  display: inline-block;
  vertical-align: top;
}
.tab_slider_wrapp .top_block .right_block_wrapper > .with_link {
  margin-right: 65px;
}
.tab_slider_wrapp .top_block .right_block_wrapper .tabs_wrapper {
  display: inline-block;
}
.tab_slider_wrapp .top_block .tabs {
  position: relative;
  z-index: 1;
  top: -3px;
}

.top_block ul.tabs li {
  margin: 0 26px 0 0;
  display: inline-block;
}
.top_block ul.tabs li:not(.cur) {
  cursor: pointer;
}
.top_block ul.tabs li:last-of-type {
  margin-right: 0;
}

@media (max-width: 767px) {
  .tab_slider_wrapp .top_block h3 {
    flex-shrink: 1;
  }
  .tab_slider_wrapp .top_block > a + div {
    padding: 0px;
    float: none !important;
  }
  .top_block ul.tabs li {
    margin-right: 5px;
  }

  .tab_slider_wrapp .top_block {
    margin-bottom: 0px;
  }
  /*.best_block.tab_slider_wrapp ul.tabs_content{margin-top:0px;}*/

  .tab_slider_wrapp .top_block {
    flex-wrap: wrap;
  }
  .tab_slider_wrapp .top_block .right_block_wrapper {
    width: 100%;
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
  }
  .tab_slider_wrapp .top_block .right_block_wrapper > a {
    top: 20px;
    position: absolute;
    right: 0;
  }
  .tab_slider_wrapp .top_block .right_block_wrapper > .with_link {
    margin-right: 0;
    display: block;
    width: 100%;
  }
}
@media (max-width: 550px) {
  .tab_slider_wrapp ul.tabs > li span {
    font-size: 12px;
  }
}
