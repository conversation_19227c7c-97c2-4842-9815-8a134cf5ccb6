InitTabsScroll=function(){$(".arrow_scroll:not(.arrow_scroll_init)").scrollTab()},ResizeScrollTabs=function(){var scrollTabs=$(".arrow_scroll_init");scrollTabs.length&&scrollTabs.each((function(i,scrollTab){var _scrollTab;$(scrollTab).data("scrollTabOptions").resize()}))},$(document).ready((function(){InitTabsScroll()})),$(window).on("resize",(function(){void 0!==window.scrollTabsTimeout&&clearTimeout(window.scrollTabsTimeout),window.scrollTabsTimeout=setTimeout(ResizeScrollTabs,20)})),$.fn.scrollTab=function(options){function _scrollTab(element,options){var _scrollTab=$(element),tabs_wrapper=_scrollTab.find(options.tabs_wrapper);if(void 0===tabs_wrapper||!tabs_wrapper.length)return!1;var tabs=tabs_wrapper.find("> li");if(!tabs.length)return!1;var arrow_svg='<svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8"><rect width="12" height="8" fill="#333" fill-opacity="0" /><path d="M1015.69,507.693a0.986,0.986,0,0,1-1.4,0l-4.31-4.316-4.3,4.316a0.993,0.993,0,0,1-1.4-1.408l4.99-5.009a1.026,1.026,0,0,1,1.43,0l4.99,5.009A0.993,0.993,0,0,1,1015.69,507.693Z" fill="#333" transform="translate(-1004 -501)"/></svg>',arrows_wrapper,arrows=$('<div class="arrows_wrapper"><div class="arrow arrow_left colored_theme_hover_text">'+arrow_svg+'</div><div class="arrow arrow_right colored_theme_hover_text">'+arrow_svg+"</div></div>").insertAfter(tabs_wrapper),arrow_left=arrows.find(".arrow_left"),arrow_right=arrows.find(".arrow_right"),thisOoptions=$.extend({},options);thisOoptions.scrollTab=_scrollTab,thisOoptions.wrapper=tabs_wrapper,thisOoptions.tabs=tabs,thisOoptions.arrows={},thisOoptions.arrows.wrapper=arrows,thisOoptions.arrows.arrow_left=arrow_left,thisOoptions.arrows.arrow_right=arrow_right,void 0!==thisOoptions.linked_tabs&&thisOoptions.linked_tabs.length&&void 0!==thisOoptions.linked_tabs.data("scrollTabOptions")&&(thisOoptions.linked_options=thisOoptions.linked_tabs.data("scrollTabOptions"),thisOoptions.linked_options.linked_options=thisOoptions),options.arrows_css&&(thisOoptions.arrows.arrow_left.css(options.arrows_css),thisOoptions.arrows.arrow_right.css(options.arrows_css)),thisOoptions.initTabs=function(){thisOoptions.wrapperBounds=thisOoptions.wrapper[0].getBoundingClientRect(),thisOoptions.scrollBounds=thisOoptions.scrollTab[0].getBoundingClientRect(),elements=$(thisOoptions.tabs),elements.length&&(thisOoptions.firstTabBound=elements[0].getBoundingClientRect().left,thisOoptions.lastTabBound=elements[elements.length-1].getBoundingClientRect().right),thisOoptions.minTranslate=Math.round(thisOoptions.wrapperBounds.right)<Math.round(thisOoptions.lastTabBound)?Math.round(thisOoptions.wrapperBounds.right)-Math.round(thisOoptions.lastTabBound)-2:0,thisOoptions.disabled=Math.round(thisOoptions.lastTabBound)-Math.round(thisOoptions.wrapperBounds.left)<=Math.round(thisOoptions.scrollBounds.width)},thisOoptions.wrapper.css({"white-space":"nowrap","min-width":"auto",overflow:"visible","z-index":"1"}),thisOoptions.scrollTab.css({overflow:"hidden",position:"relative"}),thisOoptions.checkArrows=function(translate){void 0===translate&&(translate=thisOoptions.translate),thisOoptions.disabled?(thisOoptions.arrows.arrow_left.addClass("disabled"),thisOoptions.arrows.arrow_right.addClass("disabled")):translate>=thisOoptions.maxTranslate?(thisOoptions.arrows.arrow_left.addClass("disabled"),thisOoptions.arrows.arrow_right.removeClass("disabled")):translate<=thisOoptions.minTranslate?(thisOoptions.arrows.arrow_right.addClass("disabled"),thisOoptions.arrows.arrow_left.removeClass("disabled")):(thisOoptions.arrows.arrow_left.removeClass("disabled"),thisOoptions.arrows.arrow_right.removeClass("disabled"))},thisOoptions.directScroll=function(distance,delay,step){void 0===delay&&(delay=5),clearInterval(thisOoptions.timerMoveDirect);var newTranslate=thisOoptions.translate+distance;newTranslate>thisOoptions.maxTranslate?newTranslate=thisOoptions.maxTranslate:newTranslate<thisOoptions.minTranslate&&(newTranslate=thisOoptions.minTranslate);var tmpTranslate=thisOoptions.translate;thisOoptions.translate=newTranslate,0==delay?(thisOoptions.translate=newTranslate,thisOoptions.wrapper.css({transform:"translateX("+thisOoptions.translate+"px)"})):(void 0===step&&(step=1),thisOoptions.timerMoveDirect=setInterval((function(){thisOoptions.wrapper.css({transform:"translateX("+tmpTranslate+"px)"}),(distance<0&&tmpTranslate<=newTranslate||distance>0&&tmpTranslate>=newTranslate)&&(thisOoptions.initTabs(),clearInterval(thisOoptions.timerMoveDirect)),tmpTranslate<newTranslate?tmpTranslate+=step:tmpTranslate-=step}),delay)),thisOoptions.checkArrows(newTranslate)},thisOoptions.addArrowsEvents=function(){thisOoptions.arrows.arrow_right.on("mouseenter",(function(){thisOoptions.arrows.arrow_left.removeClass("disabled"),thisOoptions.timerMoveLeft=setInterval((function(){thisOoptions.translate<thisOoptions.minTranslate?(clearInterval(thisOoptions.timerMoveLeft),thisOoptions.arrows.arrow_right.addClass("disabled")):(thisOoptions.translate-=thisOoptions.translateSpeed,thisOoptions.wrapper.css({transform:"translateX("+thisOoptions.translate+"px)"}))}),10)})),thisOoptions.arrows.arrow_right.on("mouseleave",(function(){clearInterval(thisOoptions.timerMoveLeft)})),thisOoptions.arrows.arrow_right.on("click",(function(){thisOoptions.directScroll(-thisOoptions.directTranslate),thisOoptions.arrows.arrow_left.removeClass("disabled")})),thisOoptions.arrows.arrow_right.on("touchend",(function(){setTimeout((function(){clearInterval(thisOoptions.timerMoveLeft)}),1)})),thisOoptions.arrows.arrow_left.on("mouseenter",(function(){thisOoptions.arrows.arrow_right.removeClass("disabled"),thisOoptions.timerMoveRight=setInterval((function(){thisOoptions.translate>=thisOoptions.maxTranslate?(clearInterval(thisOoptions.timerMoveRight),thisOoptions.arrows.arrow_left.addClass("disabled")):(thisOoptions.translate+=thisOoptions.translateSpeed,thisOoptions.wrapper.css({transform:"translateX("+thisOoptions.translate+"px)"}))}),10)})),thisOoptions.arrows.arrow_left.on("mouseleave",(function(){clearInterval(thisOoptions.timerMoveRight)})),thisOoptions.arrows.arrow_left.on("click",(function(){thisOoptions.directScroll(thisOoptions.directTranslate),thisOoptions.arrows.arrow_right.removeClass("disabled")})),thisOoptions.arrows.arrow_left.on("touchend",(function(){setTimeout((function(){clearInterval(thisOoptions.timerMoveRight)}),1)}))},thisOoptions.addTabsEvents=function(){thisOoptions.tabs.on("click",(function(){var leftScrollBound=thisOoptions.scrollBounds.left,rightScrollBound=thisOoptions.scrollBounds.right,tabBounds=this.getBoundingClientRect(),leftTabScrollBound=tabBounds.left-thisOoptions.arrows.arrow_width,rightTabScrollBound=tabBounds.right+thisOoptions.arrows.arrow_width;if(leftTabScrollBound<leftScrollBound?thisOoptions.directScroll(leftScrollBound-leftTabScrollBound,1,2):rightTabScrollBound>rightScrollBound&&thisOoptions.directScroll(rightScrollBound-rightTabScrollBound,1,2),thisOoptions.activeTab=$(this),void 0!==thisOoptions.linked_options){var this_index=$(this).index(),linked_tab=$(thisOoptions.linked_options.tabs[this_index]),linked_tabs={leftScrollBound:thisOoptions.linked_options.scrollBounds.left,rightScrollBound:thisOoptions.linked_options.scrollBounds.right,tabBounds:linked_tab[0].getBoundingClientRect()};linked_tabs.tabBounds.left<linked_tabs.leftScrollBound?thisOoptions.linked_options.directScroll(linked_tabs.leftScrollBound-linked_tabs.tabBounds.left+thisOoptions.linked_options.arrows.arrow_width+1,0):linked_tabs.tabBounds.right>linked_tabs.rightScrollBound&&thisOoptions.linked_options.directScroll(linked_tabs.rightScrollBound-linked_tabs.tabBounds.right-thisOoptions.linked_options.arrows.arrow_width-1,0),thisOoptions.linked_options.activeTab=linked_tab}}))},thisOoptions.addWrapperEvents=function(){thisOoptions.wrapper.on("touchstart",(function(event){thisOoptions.touch.posPrev=event.originalEvent.changedTouches[0].pageX,clearInterval(thisOoptions.timerMoveRight),clearInterval(thisOoptions.timerMoveLeft),clearInterval(thisOoptions.timerMoveDirect)})),thisOoptions.wrapper.on("touchmove",(function(event){thisOoptions.touch.posCurrent=event.originalEvent.changedTouches[0].pageX-thisOoptions.touch.posPrev,thisOoptions.directScroll(thisOoptions.touch.posCurrent,0),thisOoptions.touch.posPrev=event.originalEvent.changedTouches[0].pageX}))},thisOoptions.resize=function(){if(thisOoptions.onBeforeResize&&"function"==typeof thisOoptions.onBeforeResize&&thisOoptions.onBeforeResize(thisOoptions),thisOoptions.onResize&&"function"==typeof thisOoptions.onResize&&thisOoptions.onResize(thisOoptions),thisOoptions.translate<thisOoptions.minTranslate?thisOoptions.directScroll(thisOoptions.minTranslate-thisOoptions.translate):thisOoptions.translate>thisOoptions.maxTranslate&&thisOoptions.directScroll(thisOoptions.maxTranslate-thisOoptions.translate),void 0!==thisOoptions.activeTab&&thisOoptions.activeTab.length){var activeTabBounds=thisOoptions.activeTab[0].getBoundingClientRect();activeTabBounds.left<thisOoptions.scrollBounds.left?thisOoptions.directScroll(thisOoptions.scrollBounds.left-activeTabBounds.left):activeTabBounds.right>thisOoptions.scrollBounds.right&&thisOoptions.directScroll(thisOoptions.scrollBounds.right-activeTabBounds.right)}thisOoptions.initTabs(),thisOoptions.checkArrows(),thisOoptions.onAfterResize&&"function"==typeof thisOoptions.onAfterResize&&thisOoptions.onAfterResize(thisOoptions)},_scrollTab.data("scrollTabOptions",thisOoptions),_scrollTab.data("scrollTabOptions").addArrowsEvents(),_scrollTab.data("scrollTabOptions").addWrapperEvents(),_scrollTab.addClass("arrow_scroll_init").addClass("swipeignore"),void 0!==arrow_right&&arrow_right.length&&(thisOoptions.arrows.arrow_width=thisOoptions.arrows.arrow_right[0].getBoundingClientRect().width),_scrollTab.data("scrollTabOptions").resize(),delete thisOoptions}var options=$.extend({translate:1,translateSpeed:2,directTranslate:150,maxTranslate:1,touch:{},arrows_css:!1,tabs_wrapper:".nav-tabs",onResize:!1,width_grow:9},options),el=$(this);if(el.hasClass("arrow_scroll_init"))return!1;el.each((function(i,scrollTab){_scrollTab(scrollTab,options)}))};