function updateProgressBar(){let allProgressBar="nodelist"===typeofExt(window.asproAllProgressBar)&&window.asproAllProgressBar;if(allProgressBar&&allProgressBar.length){let scrollHeight=Math.max(document.body.scrollHeight,document.documentElement.scrollHeight,document.body.offsetHeight,document.documentElement.offsetHeight,document.body.clientHeight,document.documentElement.clientHeight),scrollTop=window.pageYOffset||document.documentElement.scrollTop,clientHeight=document.documentElement.clientHeight,scrollProcent=Math.round(scrollTop/(scrollHeight-clientHeight)*100);if(allProgressBar.length)for(let item=0;item<allProgressBar.length;item++)allProgressBar[item].style.width=scrollProcent+"%"}}InitMenuNavigationAim=function(){var $block=$(".menu-navigation__sections-wrapper .menu-navigation__sections:not(.aim-init)");$block.length&&($block.addClass("aim-init"),BX.loadScript(arAsproOptions.SITE_TEMPLATE_PATH+"/vendor/js/jquery.menu-aim.js",(function(){$block.menuAim({firstActiveRow:!0,rowSelector:"> .menu-navigation__sections-item",activate:function activate(a){var _this=$(a),index=_this.index(),items=_this.closest(".menu-navigation__sections-wrapper").next(),link=_this.find("> a");_this.siblings().find("> a").addClass("dark_link"),link.addClass("colored_theme_text").removeClass("dark_link"),items.find(".parent-items").siblings().hide(),items.find(".parent-items").eq(index).show()},deactivate:function deactivate(a){var _this=$(a),index=_this.index(),items=_this.closest(".menu-navigation__sections-wrapper").next(),link;_this.find("> a").removeClass("colored_theme_text").addClass("dark_link"),items.find(".parent-items").siblings().hide()}})})))},readyDOM((function(){window.asproAllProgressBar=document.querySelectorAll(".header-progress-bar__inner")})),$(document).ready((function(){$(document).on("click",".dropdown-select .dropdown-select__title",(function(){var _this=$(this),menu=_this.parent().find("> .dropdown-select__list"),bVisibleMeu,animate=menu.is(":visible")?"fadeOut":"transition.slideUpIn";_this.hasClass("clicked")||(_this.addClass("clicked"),menu.velocity("stop").velocity(animate,{duration:300,begin:function(){_this.toggleClass("opened")},complete:function(){_this.removeClass("clicked")}}))})),$("html, body").on("mousedown",(function(e){"string"==typeof e.target.className&&e.target.className.indexOf("adm")<0&&(e.stopPropagation(),$(e.target).closest(".dropdown-select").length||$(".dropdown-select .dropdown-select__title.opened").click())})),$(document).on("click",".slide-block .slide-block__head",(function(e){var _this=$(this),menu=_this.siblings(".slide-block__body"),bVisibleMeu=menu.is(":visible"),animate=bVisibleMeu?"slideUp":"slideDown";if(!_this.hasClass("clicked")&&menu.length&&!_this.hasClass("ignore")&&!$(e.target).attr("href")){var type=_this.data("id");_this.addClass("clicked"),bVisibleMeu?$.cookie(type+"_CLOSED","Y"):$.removeCookie(type+"_CLOSED"),menu.velocity("stop").velocity(animate,{duration:150,begin:function(){_this.toggleClass("closed")},complete:function(){_this.removeClass("clicked"),void 0!==window.stickySidebar&&window.stickySidebar.updateSticky()}})}})),"SelectOfferProp"in window||"function"==typeof window.SelectOfferProp||(SelectOfferProp=function(){var _this=$(this),obParams={},obSelect={},objUrl=parseUrlQuery(),add_url="",selectMode=!!_this.hasClass("list_values_wrapper"),container=_this.closest(".bx_catalog_item_scu"),img=_this.closest(".item-parent").find(".thumb img"),bDetail=_this.closest(".product-main").length;obParams={PARAMS:bDetail?$(".js-sku-config").data("params"):_this.closest(".js_wrapper_items").data("params"),ID:container.data("offer_id"),SITE_ID:container.data("site_id"),LINK_ID:container.data("id")+"_"+(bDetail?"detail":_this.closest(".cur").data("code")),IBLOCK_ID:container.data("offer_iblockid"),IBLOCK_ID_PARENT:container.data("iblockid"),PROPERTY_ID:container.data("propertyid"),DEPTH:_this.closest(".item_wrapper").index(),VALUE:selectMode?_this.find("option:selected").data("onevalue"):_this.data("onevalue"),CLASS:"inner_content",PICTURE:img.data("src")?img.data("src"):img.attr("src"),ARTICLE_NAME:_this.closest(".item-parent").find(".article_block").data("name"),ARTICLE_VALUE:_this.closest(".item-parent").find(".article_block").data("value")},"clear_cache"in objUrl&&"Y"==objUrl.clear_cache&&(add_url+="?clear_cache=Y");let isActiveContainer=container.hasClass("js-selected");for($(".bx_catalog_item_scu").removeClass("js-selected"),container.addClass("js-selected"),i=0;i<obParams.DEPTH+1;i++)strName="PROP_"+container.find(".item_wrapper:eq("+i+") > div").data("id"),container.find(".item_wrapper:eq("+i+") select").length?(obSelect[strName]=container.find(".item_wrapper:eq("+i+") select option:selected").data("onevalue"),obParams[strName]=container.find(".item_wrapper:eq("+i+") select option:selected").data("onevalue")):(obSelect[strName]=container.find(".item_wrapper:eq("+i+") li.item.active").data("onevalue"),obParams[strName]=container.find(".item_wrapper:eq("+i+") li.item.active").data("onevalue"));if(selectMode||(_this.siblings().removeClass("active"),_this.addClass("active")),_this.attr("title")){var skuVal=_this.attr("title").split(":")[1];_this.closest(".item_wrapper").find(".show_class .val").text(skuVal)}else{var img_row=_this.find(" > i");if(img_row.length&&img_row.attr("title")){var skuVal=img_row.attr("title").split(":")[1];_this.closest(".item_wrapper").find(".show_class .val").text(skuVal)}}function finalActionSKUInfo(th){$(".counter_wrapp.list")&&$(".counter_wrapp.list .counter_block.big").removeClass("big");var sku_props=th.closest(".sku_props").find(".item_wrapper .item.active, .item_wrapper .list_values_wrapper");$.each(sku_props,(function(index,value){var skuVal="";if((value=$(value)).attr("title"))skuVal=value.attr("title").split(":")[1];else if(void 0!==value.val())skuVal=value.val();else{var img_row=value.find(" > i");img_row.length&&img_row.attr("title")&&(skuVal=img_row.attr("title").split(":")[1])}""!=skuVal&&value.closest(".item_wrapper").find(".show_class .val").text(skuVal)}))}window.obOffers&&"array"===typeofExt(obOffers)&&isActiveContainer?(selectedValues=obSelect,strPropValue=obParams.VALUE,depth=obParams.DEPTH,arFilter={},tmpFilter=[],UpdateSKUInfoByProps(),finalActionSKUInfo(_this)):$.ajax({url:arMaxOptions.SITE_DIR+"ajax/js_item_detail.php"+add_url,type:"POST",data:obParams}).success((function(html){var ob=BX.processHTML(html);BX.ajax.processScripts(ob.SCRIPT),finalActionSKUInfo(_this)}))},$(document).on("click",".ajax_load .bx_catalog_item_scu li.item",SelectOfferProp),$(document).on("change",".ajax_load .bx_catalog_item_scu select.list_values_wrapper",SelectOfferProp),$(document).on("click",".bx_catalog_item_scu.sku_in_detail li.item",SelectOfferProp),$(document).on("change",".bx_catalog_item_scu.sku_in_detail select.list_values_wrapper",SelectOfferProp)),$(".switch-item-block .switch-item-block__icons").on("click",(function(){var $this=$(this),animationTime=200;$this.hasClass("switch-item-block__icons--small")&&!$this.hasClass("active")?($this.addClass("active"),$this.siblings(".switch-item-block__icons--big").removeClass("active"),$this.closest(".switch-item-block").find(".switch-item-block__count-wrapper--big").fadeOut(200,(function(){$this.closest(".switch-item-block").find(".switch-item-block__count-wrapper--small").fadeIn(200)})),$this.closest(".switch-item-block").siblings(".big-gallery-block").fadeOut(200,(function(){$this.closest(".switch-item-block").siblings(".small-gallery-block").fadeIn(200)}))):$this.hasClass("switch-item-block__icons--big")&&!$this.hasClass("active")&&($this.addClass("active"),$this.siblings(".switch-item-block__icons--small").removeClass("active"),$this.closest(".switch-item-block").find(".switch-item-block__count-wrapper--small").fadeOut(200,(function(){$this.closest(".switch-item-block").find(".switch-item-block__count-wrapper--big").fadeIn(200)})),$this.closest(".switch-item-block").siblings(".small-gallery-block").fadeOut(200,(function(){$this.closest(".switch-item-block").siblings(".big-gallery-block").fadeIn(200)}))),setTimeout((function(){InitLazyLoad()}),300)})),window.addEventListener("scroll",throttle(updateProgressBar,200)),$(".form-control:not(.eye-password-ignore) [type=password]").each((function(item){let passBlock=$(this).closest(".form-control"),labelBlock=passBlock.find(".label_block");labelBlock.length?labelBlock.addClass("eye-password"):passBlock.addClass("eye-password")})),$(document).on("click",".eye-password:not(.eye-password-ignore)",(function(event){let input=this.querySelector("input"),eyeWidth=56;this.clientWidth-56<event.offsetX&&("password"==input.type?(input.type="text",this.classList.add("password-show")):"text"==input.type&&(input.type="password",this.classList.remove("password-show")))}))}));
//# sourceMappingURL=blocks.min.js.map