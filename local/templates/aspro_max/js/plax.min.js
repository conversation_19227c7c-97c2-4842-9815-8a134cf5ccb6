!function(r){function t(r){var t=[0,0,0],n=r.css("-webkit-transform")||r.css("-moz-transform")||r.css("-ms-transform")||r.css("-o-transform")||r.css("transform");if("none"!==n){var o=n.split("(")[1].split(")")[0].split(","),e=0,a=0,i=0;16==o.length?(e=parseFloat(o[o.length-4]),a=parseFloat(o[o.length-3]),i=parseFloat(o[o.length-2])):(e=parseFloat(o[o.length-2]),a=parseFloat(o[o.length-1]),i=0),t=[e,a,i]}return t}function n(r){if(0===r.offsetWidth||0===r.offsetHeight)return!1;for(var t=document.documentElement.clientHeight,n=r.getClientRects(),o=0,e=n.length;e>o;o++){var a=n[o],i=a.top>0?a.top<=t:a.bottom>0&&a.bottom<=t;if(i)return!0}return!1}function o(){var r,t=document.createElement("p"),n={webkitTransform:"-webkit-transform",OTransform:"-o-transform",msTransform:"-ms-transform",MozTransform:"-moz-transform",transform:"transform"};document.body.insertBefore(t,null);for(var o in n)void 0!==t.style[o]&&(t.style[o]="translate3d(1px,1px,1px)",r=window.getComputedStyle(t).getPropertyValue(n[o]));return document.body.removeChild(t),void 0!==r&&r.length>0&&"none"!==r}function e(){return b===!0?!1:void 0!==window.DeviceOrientationEvent}function a(r){if(x=r.gamma,y=r.beta,90===Math.abs(window.orientation)){var t=x;x=y,y=t}return window.orientation<0&&(x=-x,y=-y),p=null===p?x:p,v=null===v?y:v,{x:x-p,y:y-v}}function i(r){if(!((new Date).getTime()<l+f)){l=(new Date).getTime();var t=null!=m.offset()?m.offset().left:0,o=null!=m.offset()?m.offset().top:0,i=r.pageX-t,s=r.pageY-o;if(n(g[0].obj[0].parentNode)){if(e()){if(void 0===r.gamma)return void(b=!0);values=a(r),i=values.x/c,s=values.y/c,i=d>i?d:i>u?u:i,s=d>s?d:s>u?u:s,i=(i+1)/2,s=(s+1)/2}var p,y,v=i/(e()===!0?u:m.width()),x=s/(e()===!0?u:m.height());for(y=g.length;y--;)p=g[y],h.useTransform&&!p.background?(newX=p.transformStartX+p.inversionFactor*(p.xRange*v),newY=p.transformStartY+p.inversionFactor*(p.yRange*x),newZ=p.transformStartZ,p.obj.css({transform:"translate3d("+newX+"px,"+newY+"px,"+newZ+"px)"})):(newX=p.startX+p.inversionFactor*(p.xRange*v),newY=p.startY+p.inversionFactor*(p.yRange*x),p.background?p.obj.css("background-position",newX+"px "+newY+"px"):p.obj.css("left",newX).css("top",newY))}}}var s=25,f=1/s*1e3,l=(new Date).getTime(),g=[],m={},c=30,u=1,d=-1,p=null,v=null,b=!1,h=null,w={useTransform:!0};r.fn.plaxify=function(n){return h=r.extend({},w,n),h.useTransform=h.useTransform?o():!1,this.each(function(){for(var o=-1,e={xRange:r(this).data("xrange")||0,yRange:r(this).data("yrange")||0,zRange:r(this).data("zrange")||0,invert:r(this).data("invert")||!1,background:r(this).data("background")||!1},a=0;a<g.length;a++)this===g[a].obj.get(0)&&(o=a);for(var i in n)0==e[i]&&(e[i]=n[i]);if(e.inversionFactor=e.invert?-1:1,e.obj=r(this),e.background){if(pos=(e.obj.css("background-position")||"0px 0px").split(/ /),2!=pos.length)return;if(x=pos[0].match(/^((-?\d+)\s*px|0+\s*%|left)$/),y=pos[1].match(/^((-?\d+)\s*px|0+\s*%|top)$/),!x||!y)return;e.originX=e.startX=x[2]||0,e.originY=e.startY=y[2]||0,e.transformOriginX=e.transformStartX=0,e.transformOriginY=e.transformStartY=0,e.transformOriginZ=e.transformStartZ=0}else{var s=e.obj.position(),f=t(e.obj);e.obj.css({transform:f.join()+"px",top:s.top,left:s.left,right:"",bottom:""}),e.originX=e.startX=s.left,e.originY=e.startY=s.top,e.transformOriginX=e.transformStartX=f[0],e.transformOriginY=e.transformStartY=f[1],e.transformOriginZ=e.transformStartZ=f[2]}e.startX-=e.inversionFactor*Math.floor(e.xRange/2),e.startY-=e.inversionFactor*Math.floor(e.yRange/2),e.transformStartX-=e.inversionFactor*Math.floor(e.xRange/2),e.transformStartY-=e.inversionFactor*Math.floor(e.yRange/2),e.transformStartZ-=e.inversionFactor*Math.floor(e.zRange/2),o>=0?g.splice(o,1,e):g.push(e)})},r.plax={enable:function(t){t?(t.activityTarget&&(m=t.activityTarget||r(document.body)),"number"==typeof t.gyroRange&&t.gyroRange>0&&(c=t.gyroRange)):m=r(document.body),m.bind("mousemove.plax",function(r){i(r)}),e()&&(window.ondeviceorientation=function(r){i(r)})},disable:function(t){if(r(document).unbind("mousemove.plax"),window.ondeviceorientation=void 0,t&&"boolean"==typeof t.restorePositions&&t.restorePositions)for(var n=g.length;n--;)layer=g[n],h.useTransform&&!layer.background?layer.obj.css("transform","translate3d("+layer.transformOriginX+"px,"+layer.transformOriginY+"px,"+layer.transformOriginZ+"px)").css("top",layer.originY):g[n].background?layer.obj.css("background-position",layer.originX+"px "+layer.originY+"px"):layer.obj.css("left",layer.originX).css("top",layer.originY);t&&"boolean"==typeof t.clearLayers&&t.clearLayers&&(g=[])}},"undefined"!=typeof ender&&r.ender(r.fn,!0)}(function(){return"undefined"!=typeof jQuery?jQuery:ender}());