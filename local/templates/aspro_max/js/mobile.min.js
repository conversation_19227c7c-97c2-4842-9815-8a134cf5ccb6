$(document).ready((function(){var $mobileMenu=$("#mobilemenu");if($mobileMenu.length){if($mobileMenu.isLeftSide=$mobileMenu.hasClass("leftside"),$mobileMenu.isOpen=$mobileMenu.hasClass("show"),$mobileMenu.isDowndrop=$mobileMenu.find(">.scroller").hasClass("downdrop"),$mobileMenuNlo=$mobileMenu.find("[data-nlo]"),$("#mobileheader .burger").click((function(){SwipeMobileMenu()})),$mobileMenu.isLeftSide){$mobileMenu.parent().append('<div id="mobilemenu-overlay"></div>');var $mobileMenuOverlay=$("#mobilemenu-overlay");$mobileMenuOverlay.click((function(){$mobileMenu.isOpen&&(CloseMobileMenu(),$mobileMenu.find(".expanded").removeClass("expanded"))})),$(document).swiperight((function(e){if(!$(e.target).closest(".flexslider").length&&!$(e.target).closest(".swipeignore").length&&!$(e.target).closest(".jqmWindow.popup").length){var partWindowWidth=document.documentElement.clientWidth/3,swipeStart;e.swipestart.coords[0]<=partWindowWidth&&OpenMobileMenu()}})),$(document).swipeleft((function(e){$(e.target).closest(".flexslider").length||$(e.target).closest(".swipeignore").length||$(e.target).closest(".jqmWindow.popup").length||CloseMobileMenu()}))}else $(document).on("click","#mobileheader",(function(e){$(e.target).closest("#mobilemenu").length||$(e.target).closest(".burger").length||!$mobileMenu.isOpen||CloseMobileMenu()}));function MoveMobileMenuWrapNext(){if(!$mobileMenu.isDowndrop){var $scroller=$mobileMenu.find(".scroller").first(),$wrap=$mobileMenu.find(".wrap").first();if($wrap.length){var params=$wrap.data("params"),$dropdownNext=$mobileMenu.find(".expanded>.dropdown").eq(params.depth);if($dropdownNext.length){params.scroll[params.depth]=parseInt($mobileMenu.scrollTop()),params.height[params.depth+1]=Math.max($dropdownNext.height(),params.depth?$mobileMenu.find(".expanded>.dropdown").eq(params.depth-1).height():$wrap.height()),$scroller.css("height",params.height[params.depth+1]+"px"),++params.depth,$wrap.css("transform","translateX("+-100*params.depth+"%)"),setTimeout((function(){$mobileMenu.animate({scrollTop:0},200)}),100);var h=$dropdownNext.height();setTimeout((function(){h?$scroller.css("height",h+"px"):$scroller.css("height","")}),200)}$wrap.data("params",params)}}}function MoveMobileMenuWrapPrev(){if(!$mobileMenu.isDowndrop){var $scroller=$mobileMenu.find(".scroller").first(),$wrap=$mobileMenu.find(".wrap").first();if($wrap.length){var params=$wrap.data("params"),$dropdown;if(params.depth>0)if($mobileMenu.find(".expanded>.dropdown").eq(params.depth-1).length){$scroller.css("height",params.height[params.depth]+"px"),--params.depth,$wrap.css("transform","translateX("+-100*params.depth+"%)"),setTimeout((function(){$mobileMenu.animate({scrollTop:params.scroll[params.depth]},200)}),100);var h=!!params.depth&&$mobileMenu.find(".expanded>.dropdown").eq(params.depth-1).height();setTimeout((function(){h?$scroller.css("height",h+"px"):$scroller.css("height","")}),200)}$wrap.data("params",params)}}}$(document).on("click","#mobilemenu .menu a,#mobilemenu .social-icons a",(function(e){var $this=$(this);if($this.hasClass("parent"))e.preventDefault(),$mobileMenu.isDowndrop?$this.closest("li").hasClass("expanded")?$this.closest("li").removeClass("expanded"):$this.closest("li").addClass("expanded"):($this.closest("li").addClass("expanded"),MoveMobileMenuWrapNext());else{if($this.closest("li").hasClass("counters")){var href=$this.attr("href");if(void 0!==href){if("javascript:void(0)"===href)return;window.location.href=href,window.location.reload()}}$this.closest(".menu_back").length||CloseMobileMenu()}})),$(document).on("click","#mobilemenu .dropdown .menu_back",(function(e){e.preventDefault();var $this=$(this);MoveMobileMenuWrapPrev(),setTimeout((function(){$this.closest(".expanded").removeClass("expanded")}),400)})),OpenMobileMenu=function(){if(CloseMobilePhone(),!$mobileMenu.isOpen){if($(".style-switcher").hasClass("active")&&$(".style-switcher .switch").trigger("click"),$(".style-switcher .switch").hide(),$mobileMenu.isLeftSide?setTimeout((function(){$mobileMenuOverlay.fadeIn("fast")}),100):($("body").scrollTop(0).css({position:"fixed"}),$mobileMenu.css({top:+($("#mobileheader").height()+$("#mobileheader").offset().top)+"px"}),$("#mobileheader .burger").addClass("c")),$mobileMenu.addClass("show"),$mobileMenu.isOpen=!0,!$mobileMenu.isDowndrop){var $wrap=$mobileMenu.find(".wrap").first(),params=$wrap.data("params");void 0===params&&(params={depth:0,scroll:{},height:{}}),$wrap.data("params",params)}$mobileMenuNlo.length&&($mobileMenuNlo.hasClass("nlo-loadings")||($mobileMenuNlo.addClass("nlo-loadings"),setTimeout((function(){$.ajax({data:{nlo:$mobileMenuNlo.attr("data-nlo")},success:function(response){$mobileMenuNlo[0].insertAdjacentHTML("beforebegin",$.trim(response)),$mobileMenuNlo.remove()},error:function(){$mobileMenuNlo.removeClass("nlo-loadings")}})}),300)))}},CloseMobileMenu=function(){$mobileMenu.isOpen&&($mobileMenu.removeClass("show"),$mobileMenu.isOpen=!1,$(".style-switcher .switch").show(),$mobileMenu.isLeftSide?setTimeout((function(){$mobileMenuOverlay.fadeOut("fast")}),100):($("#mobileheader .burger").removeClass("c"),$("body").css({position:""})),$mobileMenu.isDowndrop||setTimeout((function(){var $scroller=$mobileMenu.find(".scroller").first(),$wrap=$mobileMenu.find(".wrap").first(),params=$wrap.data("params");params.depth=0,$wrap.data("params",params).attr("style",""),$mobileMenu.scrollTop(0),$scroller.css("height","")}),400))},SwipeMobileMenu=function(){$mobileMenu.isOpen?CloseMobileMenu():OpenMobileMenu()}}var $mobileHeader=$("#mobileheader"),$simpleHeader=$("body.simple_basket_mode #header"),$mobilePhone=$("#mobilePhone");$mobilePhone.isOpen=!1,$mobilePhone.length&&($mobilePhone.isOpen=$mobilePhone.hasClass("show"),$(document).on("click",".wrap_phones .svg-inline-phone",(function(e){SwipeMobilePhone(),e.stopPropagation()})),$(document).on("click",".wrap_phones .svg-inline-close",(function(e){CloseMobilePhone(),e.stopPropagation()}))),SwipeMobilePhone=function(){$mobilePhone.isOpen?CloseMobilePhone():OpenMobilePhone()},OpenMobilePhone=function(){if(!$mobilePhone.isOpen){CloseMobileMenu();var isSimple,positionOffset=Boolean($simpleHeader.length)?$simpleHeader[0].getBoundingClientRect():$mobileHeader[0].getBoundingClientRect(),position=positionOffset.top+positionOffset.height+pageYOffset;console.log(positionOffset),console.log(position),$('<div class="jqmOverlay mobp" style="top:'+position+'px;position:absolute"></div>').appendTo("body"),setTimeout((function(){$mobilePhone.slideDown("fast",(function(){$mobilePhone.addClass("show"),$mobilePhone.isOpen=!0}))}),100)}},CloseMobilePhone=function(){$mobilePhone.isOpen&&setTimeout((function(){$mobilePhone.slideUp("fast",(function(){$mobilePhone.removeClass("show"),$mobilePhone.isOpen=!1,$(".jqmOverlay.mobp").remove()}))}),100)},checkMobilePhone=function(){window.matchMedia("(max-width: 991px)").matches||CloseMobilePhone()},$(document).on("click","body.simple_basket_mode .back-mobile-arrow .arrow-back",(function(){document.referrer&&document.referrer!=location.href?window.history.back():location.href="/"}));var $mobilefilter=$("#mobilefilter");if($mobilefilter.length){$mobilefilter.isOpen=$mobileMenu.hasClass("show"),$mobilefilter.isAppendLeft=!1,$mobilefilter.isWrapFilter=!1,$mobilefilter.isHorizontalOrCompact=$(".filter_horizontal").length||$(".bx_filter_vertical.compact").length,$mobilefilter.close='<i class="svg svg-close close-icons"></i>',$(document).on("click",".bx-filter-title",(function(){OpenMobileFilter()})),$(document).on("click","#mobilefilter .svg-close.close-icons",(function(){CloseMobileFilter()})),$(document).on("click",".bx_filter_select_block",(function(e){var bx_filter_select_container=$(e.target).parents(".bx_filter_select_container");if(bx_filter_select_container.length){var prop_id=bx_filter_select_container.closest(".bx_filter_parameters_box").attr("data-property_id");$("#smartFilterDropDown"+prop_id).length&&$("#smartFilterDropDown"+prop_id).css({"max-width":bx_filter_select_container.width(),"z-index":"3020"})}})),$(document).on("mouseup",".bx_filter_section",(function(e){$(e.target).hasClass("bx_filter_search_button")&&CloseMobileFilter()})),$(document).on("mouseup",".bx_filter_parameters_box_title",(function(e){$("[id^='smartFilterDropDown']").hide(),$(e.target).hasClass("close-icons")&&CloseMobileFilter()})),$mobilefilter.parent().append('<div id="mobilefilter-overlay"></div>');var $mobilefilterOverlay=$("#mobilefilter-overlay");$mobilefilterOverlay.click((function(){$mobilefilter.isOpen&&CloseMobileFilter()})),mobileFilterNum=function(num,def){if(def)$(".bx_filter_search_button").text(num.data("f"));else{var str="",$prosLeng=$(".bx_filter_parameters_box > span");str+=$prosLeng.data("f")+" "+num+" "+declOfNumFilter(num,[$prosLeng.data("fi"),$prosLeng.data("fr"),$prosLeng.data("frm")]),$(".bx_filter_search_button").text(str)}},declOfNumFilter=function(number,titles){return cases=[2,0,1,1,1,2],titles[number%100>4&&number%100<20?2:cases[number%10<5?number%10:5]]},OpenMobileFilter=function(){if(!$mobilefilter.isOpen){if($("body").addClass("jqm-initied wf"),$(".bx_filter_vertical .slide-block__head.filter_title").removeClass("closed"),$(".bx_filter_vertical .slide-block__head.filter_title + .slide-block__body").show(),!$mobilefilter.isAppendLeft){$mobilefilter.isWrapFilter||($(".bx_filter").wrap("<div id='wrapInlineFilter'></div>"),$mobilefilter.isWrapFilter=!0),$(".bx_filter").appendTo($("#mobilefilter"));var helper=$("#filter-helper");helper.length&&helper.prependTo($("#mobilefilter .bx_filter_parameters")),$mobilefilter.isAppendLeft=!0}var init;"function"==typeof checkFilterLandgings&&checkFilterLandgings(),$("#mobilefilter .bx_filter_parameters").addClass("scrollbar"),$("#mobilefilter .slide-block .filter_title").addClass("ignore"),$("#mobilefilter .bx_filter_parameters .bx_filter_parameters_box_title").addClass("colored_theme_hover_bg-block"),$(".bx_filter_button_box.ajax-btns").addClass("colored_theme_bg"),$(".bx_filter_button_box.ajax-btns .filter-bnt-wrapper").removeClass("hidden"),InitCustomScrollBar(),setTimeout((function(){$mobilefilterOverlay.fadeIn("fast")}),100),$("body").css({overflow:"hidden",height:"100vh"}),$mobilefilter.addClass("show"),$mobilefilter.find(".bx_filter").css({display:"block"}),$mobilefilter.isOpen=!0,$("#mobilefilter .bx_filter_button_box.btns.ajax-btns").removeClass("hidden"),void 0===$mobilefilter.data("init")&&($mobilefilter.scroll((function(){$(".bx_filter_section .bx_filter_select_container").each((function(){var prop_id=$(this).closest(".bx_filter_parameters_box").attr("data-property_id");$("#smartFilterDropDown"+prop_id).length&&$("#smartFilterDropDown"+prop_id).hide()}))})),$mobilefilter.data("init","Y"))}},CloseMobileFilter=function(append){if($mobilefilter.find(".bx_filter_parameters").removeClass("scrollbar"),$("#mobilefilter .bx_filter_parameters").length&&$("body").removeClass("jqm-initied wf"),$("#mobilefilter .bx_filter_parameters .bx_filter_parameters_box_title").removeClass("colored_theme_hover_bg-block"),$(".slide-block .filter_title").removeClass("ignore"),$(".bx_filter_button_box.ajax-btns").removeClass("colored_theme_bg"),$(".bx_filter:not(.n-ajax) .bx_filter_button_box.ajax-btns .filter-bnt-wrapper").addClass("hidden"),$mobilefilter.isOpen&&($mobilefilter.find(".bx_filter_parameters").scrollTop(0),$("body").css({overflow:"",height:""}),setTimeout((function(){$mobilefilterOverlay.fadeOut("fast")}),100),$mobilefilter.removeClass("show"),$mobilefilter.isOpen=!1),append&&$mobilefilter.isAppendLeft){$(".bx_filter").appendTo($("#wrapInlineFilter")).show();var helper=$("#filter-helper");helper.length&&helper.appendTo($("#filter-helper-wrapper")),$mobilefilter.isAppendLeft=!1,$mobilefilter.removeData("init"),mobileFilterNum($("#modef_num_mobile"),!0)}},checkMobileFilter=function(){(!window.matchMedia("(max-width: 991px)").matches&&!$mobilefilter.isHorizontalOrCompact||!window.matchMedia("(max-width: 767px)").matches&&$mobilefilter.isHorizontalOrCompact)&&CloseMobileFilter(!0)}}else checkTopFilter(),$(document).on("click",".bx-filter-title",(function(){$(this).toggleClass("opened"),$(".visible_mobile_filter").length?($(".visible_mobile_filter").show(),$(".bx_filter_vertical, .bx_filter").slideToggle(333)):($(".bx_filter_vertical").closest("div[id^=bx_incl]").show(),$(".bx_filter_vertical, .bx_filter").slideToggle(333))}))}));