function detect_old_ie(){if(!/MSIE (\d+\.\d+);/.test(navigator.userAgent))return!1;var o=new Number(RegExp.$1);return!(9<=o)&&(8<=o||(7<=o||(6<=o||(5<=o||void 0))))}window.requestAnimFrame=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(o){window.setTimeout(o,20)};var timerMove=!1;!function(Oo){function n(n,o){this.xzoom=!0;var s,a,p,r,l,d,c,h,f,u,v,g,m,w,x,b,z,y,k,C,M,O,X,Y,A,H,S,W,F,I,T,E,R,q,L,Z,D,_,j,t,N,e,i,Q,$,B,<PERSON>,<PERSON>,<PERSON>,<PERSON>,U=this,V={},oo=(new Array,new Array),to=0,eo=0,io=0,so=0,no=0,ao=0,po=0,ro=0,lo=0,co=0,ho=0,fo=0,uo=0,vo=detect_old_ie(),go=/MSIE (\d+\.\d+);/.test(navigator.userAgent),mo="";function wo(){var o=document.documentElement;return{left:(window.pageXOffset||o.scrollLeft)-(o.clientLeft||0),top:(window.pageYOffset||o.scrollTop)-(o.clientTop||0)}}function xo(){if("circle"==U.options.lensShape&&"lens"==U.options.position){var o=((X=Y=Math.max(X,Y))+2*Math.max(W,S))/2;M.css({"-moz-border-radius":o,"-webkit-border-radius":o,"border-radius":o})}}function bo(o,t,e,i){"lens"==U.options.position?(C.css({top:-(t-c)*I+Y/2,left:-(o-h)*F+X/2}),U.options.bg&&(M.css({"background-image":"url("+C.attr("src")+")","background-repeat":"no-repeat","background-position":-(o-h)*F+X/2+"px "+(-(t-c)*I+Y/2)+"px"}),e&&i&&M.css({"background-size":e+"px "+i+"px"}))):C.css({top:-H*I,left:-A*F})}function zo(o,t){if(io<-1&&(io=-1),1<io&&(io=1),T<E)var e=(i=r*(T-(T-1)*io))/R;else var i=(e=l*(E-(E-1)*io))*R;P&&Z?(so=o,no=t,ao=i,po=e):(Z||(ro=ao=i,lo=po=e),X=r/(F=i/a),Y=l/(I=e/p),xo(),ko(o,t),C.width(i),C.height(e),M.width(X),M.height(Y),M.css({top:H-W,left:A-S}),O.css({top:-H,left:-A}),bo(o,t,i,e))}function yo(){var o=co,t=ho,e=fo,i=uo,s=ro,n=lo;o+=(so-o)/U.options.smoothLensMove,t+=(no-t)/U.options.smoothLensMove,e+=(so-e)/U.options.smoothZoomMove,i+=(no-i)/U.options.smoothZoomMove,s+=(ao-s)/U.options.smoothScale,n+=(po-n)/U.options.smoothScale,X=r/(F=s/a),Y=l/(I=n/p),xo(),ko(o,t),C.width(s),C.height(n),M.width(X),M.height(Y),M.css({top:H-W,left:A-S}),O.css({top:-H,left:-A}),ko(e,i),bo(o,t,s,n),co=o,ho=t,fo=e,uo=i,ro=s,lo=n,Z&&requestAnimFrame(yo)}function ko(o,t){A=(o-=h)-X/2,H=(t-=c)-Y/2,"lens"!=U.options.position&&(A<0&&(A=0),a-X<A&&(A=a-X),H<0&&(H=0),p-Y<H&&(H=p-Y))}function Co(){void 0!==m&&m.remove(),void 0!==x&&x.remove(),void 0!==N&&N.remove(),Oo(".xzoom-source").length&&Oo(".xzoom-source").remove(),Oo(".xzoom-preview").length&&Oo(".xzoom-preview").remove()}function Mo(o){var t=o.attr("title"),e=o.attr("xtitle");return e||(t||"")}this.adaptive=function(){0!=Q&&0!=$||(n.css("width",""),n.css("height",""),Q=n.width(),$=n.height()),Co(),e=Oo(window).width(),i=Oo(window).height(),B=n.width(),G=n.height();var o=!1;(e<Q||i<$)&&(o=!0),Q<B&&(B=Q),$<G&&(G=$),o?n.width("100%"):0!=Q&&n.width(Q),"fullscreen"!=J&&(!function(){var o=n.offset();r="auto"==U.options.zoomWidth?B:B/Q*U.options.zoomWidth;l="auto"==U.options.zoomHeight?G:B/$*U.options.zoomHeight;"#"==U.options.position.substr(0,1)?V=Oo(U.options.position):V.length=0;if(0!=V.length)return!0;switch(J){case"lens":case"inside":return!0;case"top":c=o.top,h=o.left,f=c-G,u=h;break;case"left":c=o.top,h=o.left,f=c,u=h-B;break;case"bottom":c=o.top,h=o.left,f=c+G,u=h;break;case"right":default:c=o.top,h=o.left,f=c,u=h+B}return!(e<u+r||u<0)}()?U.options.position=U.options.mposition:U.options.position=J),U.options.lensReverse||(K=U.options.adaptiveReverse&&U.options.position==U.options.mposition)},this.xscroll=function(o){if(o.preventDefault(),o.xscale){var t=o.pageX||o.originalEvent.pageX,e=o.pageY||o.originalEvent.pageY;io=o.xscale,zo(t,e)}else{var i=-o.originalEvent.detail||o.originalEvent.wheelDelta||o.xdelta;t=o.pageX||o.originalEvent.pageX,e=o.pageY||o.originalEvent.pageY;vo&&(t=D,e=_),io+=i=0<i?-.05:.05,zo(t,e)}},this.openzoom=function(i){switch(U.options.adaptive&&U.adaptive(),io=U.options.defaultScale,Z=!1,m=Oo("<div></div>"),""!=U.options.sourceClass&&m.addClass(U.options.sourceClass),m.css("position","absolute"),b=Oo("<div></div>"),""!=U.options.loadingClass&&b.addClass(U.options.loadingClass),b.css("position","absolute"),w=Oo('<div style="position: absolute; top: 0; left: 0;"></div>'),m.append(b),x=Oo("<div></div>"),""!=U.options.zoomClass&&"fullscreen"!=U.options.position&&x.addClass(U.options.zoomClass),x.css({position:"absolute",overflow:"hidden",opacity:1}),U.options.title&&""!=mo&&(N=Oo("<div></div>"),t=Oo("<div></div>"),N.css({position:"absolute",opacity:1}),U.options.titleClass&&t.addClass(U.options.titleClass),t.html("<span>"+mo+"</span>"),N.append(t),U.options.fadeIn&&N.css({opacity:0})),M=Oo("<div></div>"),""!=U.options.lensClass&&M.addClass(U.options.lensClass),M.css({position:"absolute",overflow:"hidden"}),U.options.lens&&(lenstint=Oo("<div></div>"),lenstint.css({position:"absolute",background:U.options.lens,opacity:U.options.lensOpacity,width:"100%",height:"100%",top:0,left:0,"z-index":9999}),M.append(lenstint)),"inside"!=U.options.position&&"fullscreen"!=U.options.position?((U.options.tint||vo)&&m.append(w),U.options.fadeIn&&(w.css({opacity:0}),M.css({opacity:0}),x.css({opacity:0}))):U.options.fadeIn&&x.css({opacity:0}),s.append(m),U.eventleave(m),function(o,t){switch("fullscreen"==U.options.position?(a=Oo(window).width(),p=Oo(window).height()):(a=n.width(),p=n.height()),b.css({top:p/2-b.height()/2,left:a/2-b.width()/2}),(d=U.options.rootOutput||"fullscreen"==U.options.position?n.offset():n.position()).top=Math.round(d.top),d.left=Math.round(d.left),U.options.position){case"fullscreen":c=wo().top,h=wo().left,u=f=0;break;case"inside":c=d.top,h=d.left,u=f=0;break;case"top":c=d.top,h=d.left,f=c-p,u=h;break;case"left":c=d.top,h=d.left,f=c,u=h-a;break;case"bottom":c=d.top,h=d.left,f=c+p,u=h;break;case"right":default:c=d.top,h=d.left,f=c,u=h+a}c-=m.outerHeight()/2,h-=m.outerWidth()/2,"#"==U.options.position.substr(0,1)?V=Oo(U.options.position):V.length=0,0==V.length&&"inside"!=U.options.position&&"fullscreen"!=U.options.position?(U.options.adaptive&&Q&&$||(Q=a,$=p),r="auto"==U.options.zoomWidth?a:U.options.zoomWidth,l="auto"==U.options.zoomHeight?p:U.options.zoomHeight,f+=U.options.Yoffset,u+=U.options.Xoffset,x.css({width:r+"px",height:l+"px",top:f,left:u}),"lens"!=U.options.position&&s.append(x)):"inside"==U.options.position||"fullscreen"==U.options.position?(r=a,l=p,x.css({width:r+"px",height:l+"px"}),m.append(x)):(r=V.width(),l=V.height(),U.options.rootOutput?(f=V.offset().top,u=V.offset().left,s.append(x)):(f=V.position().top,u=V.position().left,V.parent().append(x)),f+=(V.outerHeight()-l-x.outerHeight())/2,u+=(V.outerWidth()-r-x.outerWidth())/2,x.css({width:r+"px",height:l+"px",top:f,left:u})),U.options.title&&""!=mo&&("inside"==U.options.position||"lens"==U.options.position||"fullscreen"==U.options.position?(v=f,g=u,m.append(N)):(v=f+(x.outerHeight()-l)/2,g=u+(x.outerWidth()-r)/2,s.append(N)),N.css({width:r+"px",height:l+"px",top:v,left:g})),m.css({width:a+"px",height:p+"px",top:c,left:h}),w.css({width:a+"px",height:p+"px"}),U.options.tint&&"inside"!=U.options.position&&"fullscreen"!=U.options.position?w.css("background-color",U.options.tint):vo&&w.css({"background-image":"url("+n.attr("src")+")","background-color":"#fff"}),co=so=o,ho=no=t,k=new Image;var e="";switch(go&&(e="?r="+(new Date).getTime()),n.attr("xoriginal")?k.src=n.attr("xoriginal")+e:k.src=n.attr("data-xoriginal")+e,(C=Oo(k)).css("position","absolute"),(k=new Image).src=n.attr("src"),(O=Oo(k)).css("position","absolute"),O.width(a),U.options.position){case"fullscreen":case"inside":x.append(C);break;case"lens":M.append(C),U.options.bg&&C.css({display:"none"});break;default:x.append(C),M.append(O)}}(i.pageX,i.pageY),U.options.position){case"inside":f-=(x.outerHeight()-x.height())/2,u-=(x.outerWidth()-x.width())/2;break;case"top":f-=x.outerHeight()-x.height(),u-=(x.outerWidth()-x.width())/2;break;case"left":f-=(x.outerHeight()-x.height())/2,u-=x.outerWidth()-x.width();break;case"bottom":u-=(x.outerWidth()-x.width())/2;break;case"right":f-=(x.outerHeight()-x.height())/2}x.css({top:f,left:u}),C.xon("load",function(){b.remove(),U.options.scroll&&U.eventscroll(m),"inside"!=U.options.position&&"fullscreen"!=U.options.position?(m.append(M),U.options.fadeIn?(w.fadeTo(300,U.options.tintOpacity),M.fadeTo(300,1),x.fadeTo(300,1)):(w.css({opacity:U.options.tintOpacity}),M.css({opacity:1}),x.css({opacity:1}))):U.options.fadeIn?x.fadeTo(300,1):x.css({opacity:1}),U.options.title&&""!=mo&&(U.options.fadeIn?N.fadeTo(300,1):N.css({opacity:1})),q=C.width(),L=C.height(),U.options.adaptive&&(a<Q||p<$)&&(O.width(a),O.height(p),q*=a/Q,L*=p/$,C.width(q),C.height(L)),ro=ao=q,lo=po=L,R=q/L,T=q/r,E=L/l;var o,t=["padding-","border-"];W=S=0;for(var e=0;e<t.length;e++)o=parseFloat(M.css(t[e]+"top-width")),W+=o!=o?0:o,o=parseFloat(M.css(t[e]+"bottom-width")),W+=o!=o?0:o,o=parseFloat(M.css(t[e]+"left-width")),S+=o!=o?0:o,o=parseFloat(M.css(t[e]+"right-width")),S+=o!=o?0:o;W/=2,S/=2,zo(i.pageX,i.pageY),P&&!U.options.bg&&(Z=!0,requestAnimFrame(yo)),U.eventmove(m),U.eventclick(m)})},this.movezoom=function(o){vo&&(D=o.pageX,_=o.pageY);var t=o.pageX-h,e=o.pageY-c;K&&(o.pageX-=2*(t-a/2),o.pageY-=2*(e-p/2)),(t<0||a<t||e<0||p<e)&&m.trigger("mouseleave"),P&&!U.options.bg?(so=o.pageX,no=o.pageY):(xo(),ko(o.pageX,o.pageY),M.css({top:H-W,left:A-S}),O.css({top:-H,left:-A}),bo(o.pageX,o.pageY,0,0))},this.eventdefault=function(){U.eventopen=function(o){o.xon("mouseenter",U.openzoom)},U.eventleave=function(o){o.xon("mouseleave",U.closezoom)},U.eventmove=function(o){o.xon("mousemove",U.movezoom)},U.eventscroll=function(o){o.xon("mousewheel DOMMouseScroll",U.xscroll)},U.eventclick=function(o){o.xon("click",function(o){n.trigger("click")})}},this.eventunbind=function(){n.xoff("mouseenter"),U.eventopen=function(o){},U.eventleave=function(o){},U.eventmove=function(o){},U.eventscroll=function(o){},U.eventclick=function(o){}},this.init=function(o){U.options=Oo.extend({},Oo.fn.xzoom.defaults,o),s=U.options.rootOutput?Oo("body"):n.parent(),J=U.options.position,K=U.options.lensReverse&&"inside"==U.options.position,U.options.smoothZoomMove<0&&(U.options.smoothZoomMove=0),U.options.smoothLensMove<0&&(U.options.smoothLensMove=0),U.options.smoothScale<0&&(U.options.smoothScale=0),P=U.options.smoothZoomMove&&U.options.smoothLensMove&&U.options.smoothScale,U.options.adaptive&&Oo(window).xon("load",function(){Q=n.width(),$=n.height(),U.adaptive(),Oo(window).resize(U.adaptive)}),U.eventdefault(),U.eventopen(n)},this.destroy=function(){U.eventunbind(),delete U},this.closezoom=function(){Z=!1,U.options.fadeOut?(U.options.title&&""!=mo&&N.fadeOut(299),("inside"!=U.options.position||"fullscreen"!=U.options.position)&&x.fadeOut(299),m.fadeOut(300,function(){Co()})):Co()},this.gallery=function(){var o,t=new Array,e=0;for(o=eo;o<oo.length;o++)t[e]=oo[o],e++;for(o=0;o<eo;o++)t[e]=oo[o],e++;return{index:eo,ogallery:oo,cgallery:t}},this.xappend=function(i){var s=i.parent();function o(o){Co(),o.preventDefault(),U.options.activeClass&&(j.removeClass(U.options.activeClass),(j=i).addClass(U.options.activeClass)),eo=Oo(this).data("xindex"),U.options.fadeTrans&&((y=new Image).src=n.attr("src"),(z=Oo(y)).css({position:"absolute",top:n.offset().top,left:n.offset().left,width:n.width(),height:n.height()}),Oo(document.body).append(z),z.fadeOut(200,function(){z.remove()}));var t,e=s.attr("href");t=i.attr("xpreview")?i.attr("xpreview"):i.attr("data-xpreview")?i.attr("data-xpreview"):i.attr("src"),mo=Mo(i),i.attr("title")&&n.attr("title",i.attr("title")),n.attr("data-xoriginal",e),n.attr("xoriginal",e),n.attr("src",t)}oo[to]=s.attr("href"),s.data("xindex",to),0==to&&U.options.activeClass&&(j=i).addClass(U.options.activeClass),0==to&&U.options.title&&(mo=Mo(i)),to++,U.options.hover&&s.xon("mouseenter",s,o),s.xon("click",s,o)},this.init(o)}Oo.fn.xon=Oo.fn.on||Oo.fn.bind,Oo.fn.xoff=Oo.fn.off||Oo.fn.bind,Oo.fn.xzoom=function(t){var e,i;if(this.selector){var s=this.selector.split(",");for(var o in s)s[o]=Oo.trim(s[o]);this.each(function(o){if(1==s.length)if(0==o){if(void 0!==(e=Oo(this)).data("xzoom"))return e.data("xzoom");e.x=new n(e,t)}else void 0!==e.x&&(i=Oo(this),e.x.xappend(i));else if(Oo(this).is(s[0])&&0==o){if(void 0!==(e=Oo(this)).data("xzoom"))return e.data("xzoom");e.x=new n(e,t)}else void 0===e.x||Oo(this).is(s[0])||(i=Oo(this),e.x.xappend(i))})}else this.each(function(o){if(0==o){if(void 0!==(e=Oo(this)).data("xzoom"))return e.data("xzoom");e.x=new n(e,t)}else void 0!==e.x&&(i=Oo(this),e.x.xappend(i))});return void 0!==e&&(e.data("xzoom",e.x),Oo(e).trigger("xzoom_ready"),e.x)},Oo.fn.xzoom.defaults={position:"right",mposition:"inside",rootOutput:!0,Xoffset:0,Yoffset:0,fadeIn:!0,fadeTrans:!0,fadeOut:!1,smoothZoomMove:3,smoothLensMove:1,smoothScale:6,defaultScale:0,scroll:!0,tint:!1,tintOpacity:.5,lens:!1,lensOpacity:.5,lensShape:"box",zoomWidth:"auto",zoomHeight:"auto",sourceClass:"xzoom-source",loadingClass:"xzoom-loading",lensClass:"xzoom-lens",zoomClass:"xzoom-preview",activeClass:"xactive",hover:!1,adaptive:!0,lensReverse:!1,adaptiveReverse:!1,title:!1,titleClass:"xzoom-caption",bg:!1}}(jQuery);