if(!DG)var DG={};DG.switches={},DG.OnOffSwitchProperties=["textOn","textOff","width","height","heightTrack","trackColorOn","trackColorOff","textColorOn","textColorOff","listener","trackBorderColor","textSizeRatio"],DG.OnOffSwitch=function(t){if(null!=t.el){this.inputEl=$(t.el),this.name=this.inputEl.attr("name"),DG.switches[this.name]=this;var i=(DG.switches["#"+this.inputEl.attr("id")]=this).inputEl.attr("type");this.isCheckbox=i&&"checkbox"==i.toLowerCase(),this.isCheckbox?this.checked=this.inputEl.is(":checked"):this.checked=1==this.inputEl.val()}for(var h=DG.OnOffSwitchProperties,e=0;e<h.length;e++)null!=t[h[e]]&&(this[h[e]]=t[h[e]]);this.render()},$.extend(DG.OnOffSwitch.prototype,{inputEl:void 0,listener:void 0,trackBorderColor:void 0,checked:!1,width:0,height:30,heightTrack:12,trackBorderWidth:1,textSizeRatio:.4,trackColorOn:void 0,trackColorOff:"#EEE",textColorOn:void 0,textColorOff:void 0,el:void 0,track:void 0,thumb:void 0,thumbColor:void 0,onTextEl:void 0,offTextEl:void 0,onOffTrackContainer:void 0,textOn:"",textOff:"",minX:0,maxX:0,trackOn:void 0,trackOff:void 0,innerTrackWidth:0,name:void 0,dragCurrentX:0,borderSize:0,isCheckbox:!1,render:function(){if(0==this.width){var t=this.textSizeRatio/2,i=2+Math.max(this.textOff.length*t,this.textOn.length*t);this.width=this.height*i}this.inputEl.css("display","none"),this.el=$('<div class="on-off-switch" style="width:'+this.width+"px;height:"+this.height+'px"></div>'),this.inputEl.after(this.el),this.inputEl.on("change",this.listenToClickEvent.bind(this)),this.renderTrack(),this.renderThumb(),this.applyStyles(),this.track.on("click",this.toggle.bind(this)),this.track.on("touchend",this.toggle.bind(this)),this.addEvents()},listenToClickEvent:function(){this.inputEl.is(":checked")?this.checked||this.toggle():this.checked&&this.toggle()},addEvents:function(){this.thumb.on("mousedown",this.startDragging.bind(this)),this.thumb.on("touchstart",this.startDragging.bind(this)),this.thumb.on("mouseenter",this.enterThumb.bind(this)),this.thumb.on("mouseleave",this.leaveThumb.bind(this)),$(document.documentElement).on("touchmove",this.drag.bind(this)),$(document.documentElement).on("mousemove",this.drag.bind(this)),$(document.documentElement).on("mouseup",this.endDrag.bind(this)),$(document.documentElement).on("touchend",this.endDrag.bind(this))},enterThumb:function(){this.thumbColor.addClass("on-off-switch-thumb-over")},leaveThumb:function(){this.thumbColor.removeClass("on-off-switch-thumb-over")},renderTrack:function(){var t=this.width-2*this.trackBorderWidth,i=t-this.height/2;this.innerTrackWidth=t;var h=this.height-2*this.trackBorderWidth,e=this.heightTrack,s=this.height/2;this.track=$('<div class="on-off-switch-track" style="border-radius:'+s+"px;border-width:"+this.trackBorderWidth+"px;width:"+t+"px;margin-top:"+-(e/2+1)+"px;height:"+e+'px"></div>'),this.trackBorderColor&&this.track.css("border-color",this.trackBorderColor),this.el.append(this.track),this.onOffTrackContainer=$('<div style="position:absolute;height:'+h+"px;width:"+2*i+'px"></div>'),this.track.append(this.onOffTrackContainer),this.trackOn=$('<div class="on-off-switch-track-on" style="border-radius:'+s+"px;border-width:"+this.trackBorderWidth+"px;width:"+i+"px;height:"+e+'px"><div class="track-on-gradient"></div></div>'),this.onOffTrackContainer.append(this.trackOn),this.onTextEl=$('<div class="on-off-switch-text on-off-switch-text-on">'+this.textOn+"</div>"),this.trackOn.append(this.onTextEl),this.textColorOn&&this.onTextEl.css("color",this.textColorOn),this.trackOff=$('<div class="on-off-switch-track-off" style="overflow:hidden;left:'+(i-this.height/2)+"px;border-radius:"+s+"px;border-width:"+this.trackBorderWidth+"px;width:"+this.width+"px;height:"+e+'px"><div class="track-off-gradient"></div></div>'),this.offTextEl=$('<div class="on-off-switch-text on-off-switch-text-off">'+this.textOff+"</div>"),this.onOffTrackContainer.append(this.trackOff),this.trackOff.append(this.offTextEl),this.textColorOff&&this.offTextEl.css("color",this.textColorOff),this.styleText(this.onTextEl),this.styleText(this.offTextEl);var o=this.height/2,r=o/2,n=r/2,a=this.width-2*n,c=$('<div class="on-off-switch-track-white" style="left:'+n+"px;width:"+a+"px;height:"+o+"px;border-radius:"+r+'px"></div>'),d=$('<div class="on-off-switch-track-white" style="left:'+n+"px;width:"+a+"px;height:"+o+"px;border-radius:"+r+'px"></div>');c.css("top",this.height/2),d.css("top",this.height/2),this.trackOn.append(c),this.trackOff.append(d),this.maxX=this.width-this.height},styleText:function(t){var i=Math.round(this.height*this.textSizeRatio),h=Math.round(this.width-this.height);t.css("line-height",this.height-2*this.trackBorderWidth+"px"),t.css("font-size",i+"px"),t.css("left",this.height/2+"px"),t.css("width",h+"px")},renderThumb:function(){var t=this.getBorderSize(),i=this.height-2*t,h=(this.height-this.height%2)/2;this.thumb=$('<div class="on-off-switch-thumb" style="width:'+this.height+"px;height:"+this.height+'px"></div>');var e=$('<div class="on-off-switch-thumb-shadow" style="border-radius:'+h+"px;width:"+i+"px;height:"+i+"px;border-width:"+t+'px;"></div>');this.thumb.append(e),this.thumbColor=$('<div class="on-off-switch-thumb-color" style="border-radius:'+h+"px;width:"+i+"px;height:"+i+"px;left:"+t+"px;top:"+t+'px"></div>'),this.thumb.append(this.thumbColor),this.trackColorOff&&this.trackOff.css("background-color",this.trackColorOff),this.trackColorOn&&this.trackOn.css("background-color",this.trackColorOn),this.el.append(this.thumb)},getBorderSize:function(){return 0==this.borderSize&&(this.borderSize=Math.round(this.height/40)),this.borderSize},applyStyles:function(){this.thumbColor.removeClass("on-off-switch-thumb-on"),this.thumbColor.removeClass("on-off-switch-thumb-off"),this.thumbColor.removeClass("on-off-switch-thumb-over"),this.checked?(this.thumbColor.addClass("on-off-switch-thumb-on"),this.thumb.css("left",this.width-this.height),this.onOffTrackContainer.css("left",-1)):(this.onOffTrackContainer.css("left",this.getTrackPosUnchecked()),this.thumbColor.addClass("on-off-switch-thumb-off"),this.thumb.css("left",-1)),this.isCheckbox?this.inputEl.prop("checked",this.checked):this.inputEl.val(this.checked?1:0)},isDragging:!1,hasBeenDragged:!1,startDragging:function(t){this.isDragging=!0,this.hasBeenDragged=!1;var i=this.thumb.position();return!(this.startCoordinates={x:this.getX(t),elX:i.left})},drag:function(t){if(!this.isDragging)return!0;this.hasBeenDragged=!0;var i=this.startCoordinates.elX+this.getX(t)-this.startCoordinates.x;return i<this.minX&&(i=this.minX),i>this.maxX&&(i=this.maxX),this.onOffTrackContainer.css("left",i-this.width+this.height),i||(i=-1),this.thumb.css("left",i),!1},getX:function(t){var i=t.pageX;return!t.type||"touchstart"!=t.type&&"touchmove"!=t.type||(i=t.originalEvent.touches[0].pageX),this.dragCurrentX=i},endDrag:function(){if(!this.isDragging)return!0;if(this.hasBeenDragged){var t=this.width/2-this.height/2;this.startCoordinates.elX+this.dragCurrentX-this.startCoordinates.x<t?this.animateLeft():this.animateRight()}else this.toggle();this.isDragging=!1},getTrackPosUnchecked:function(){return 0-this.width+this.height},animateLeft:function(){this.onOffTrackContainer.animate({left:this.getTrackPosUnchecked()},100),this.thumb.animate({left:-1},100,"swing",this.uncheck.bind(this))},animateRight:function(){this.onOffTrackContainer.animate({left:0},100),this.thumb.animate({left:this.maxX},100,"swing",this.check.bind(this))},check:function(){this.checked||(this.checked=!0,this.notifyListeners()),this.applyStyles()},uncheck:function(){this.checked&&(this.checked=!1,this.notifyListeners()),this.applyStyles()},toggle:function(){!1!==this.notifyListeners()&&(this.checked?(this.checked=!1,this.animateLeft()):(this.checked=!0,this.animateRight()))},notifyListeners:function(){if(null!=this.listener)var t=this.listener.call(this,this.name,!this.checked);return t},getValue:function(){return this.checked}}),DG.OnOffSwitchAuto=function(t){var i=DG.OnOffSwitchProperties;if(t.cls)for(var h=$(t.cls),e=0,s=0,o=h.length;s<o;s++){var r=jQuery.extend({},t),n=$(h[s]);h[s].id||(h[s].id="dg-switch-"+e,e++),r.el="#"+h[s].id;for(var a=0;a<i.length;a++){var c="data-"+i[a],d=n.attr(c);d&&("height"==i[a]&&(d=parseInt(d)),r[i[a]]=d)}new DG.OnOffSwitch(r)}};