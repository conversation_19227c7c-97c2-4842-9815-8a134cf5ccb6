$.fn.phoneOrLogin=function(callback){function init(input,callback){var $input=$(input),keyTimeOut=!1;void 0===$input.data("phonecode")&&($input.data({phonecode:""}),$input.data("code",""),$input.bind({keydown:function(e){if(255!=e.which){var that=this;keyTimeOut&&clearTimeout(keyTimeOut),keyTimeOut=setTimeout((function(){markPhoneOrLogin(that,callback)}),50)}},blur:function(e){var bEmpty=!$input.val().length,a;arAsproOptions.THEME.PHONE_MASK.length&&(bEmpty|=arAsproOptions.THEME.PHONE_MASK.replace(/9/g,"_")==$input.val());bEmpty&&($input.hasClass("required")&&$input.parent().find("label.error").html(BX.message("JS_REQUIRED")),$input.inputmask("mask",{mask:""}),$input.data("code","")),markPhoneOrLogin(this,callback)},paste:function(e){var that=this;keyTimeOut&&clearTimeout(keyTimeOut),keyTimeOut=setTimeout((function(){markPhoneOrLogin(that,callback)}),50)},cut:function(e){var that=this;keyTimeOut&&clearTimeout(keyTimeOut),keyTimeOut=setTimeout((function(){markPhoneOrLogin(that,callback)}),50)}}),markPhoneOrLogin(input,callback))}function testPhoneOrLogin(val,code){var clearPhone=val.replace(/[\s-_()]/g,""),clearLogin=val.replace(/[\s()]/g,""),bPossiblePhone=!1,bPossibleEmail=!1;return clearPhone.length&&((bPossiblePhone=!!clearPhone.match(/^\+?\d*$/))||(bPossibleEmail=-1!==clearLogin.indexOf("@"))),{value:val,clearPhone:clearPhone,bPossiblePhone:bPossiblePhone,clearLogin:clearLogin,bPossibleEmail:bPossibleEmail}}function markPhoneOrLogin(input,callback){var $input=$(input),$parent=$input.closest(".phone_or_login"),val=$input.val(),code=$input.data("code"),test=testPhoneOrLogin(val,code),phone_code_mask;if(val.length)if(test.bPossiblePhone){if($parent.hasClass("phone_or_login-phone")){if(arAsproOptions.THEME.PHONE_MASK.length&&code.length&&(phone_code_mask=arAsproOptions.THEME.PHONE_MASK.replace(/\\9/g,"#").replace(/[^0-8+#]/g,"").replace(/#/g,"9")).length){for(var pattern="[+?]",val_=code+$input.inputmask("unmaskedvalue"),newcode="",i=0;i<val_.length;++i){var char;if("+"!==(char=val_.charAt(i))||i){var tmp=pattern+("8"!==char||i&&(1!=i||"+"!==newcode)?char:"[78]"),reg=new RegExp("^"+tmp);if(!phone_code_mask.match(reg))break;pattern=tmp,newcode+=char}else newcode+=char}if($input.data("code",newcode),code!=newcode){$input.val(val_);var now_is_phone_but_login_possible_mask=arAsproOptions.THEME.PHONE_MASK.replace(/\\9/g,"#").replace(/9/g,"*").replace(/#/g,"\\9");$input.inputmask("mask",{mask:now_is_phone_but_login_possible_mask})}}}else if($input.removeClass("email").addClass("phone"),$parent.removeClass("phone_or_login-login").removeClass("phone_or_login-email").addClass("phone_or_login-phone"),arAsproOptions.THEME.PHONE_MASK.length){var phone_code_mask;if((phone_code_mask=arAsproOptions.THEME.PHONE_MASK.replace(/\\9/g,"#").replace(/[^0-8+#]/g,"").replace(/#/g,"9")).length){for(var pattern="[+?]",val_=val,newcode="",i=0;i<val_.length;++i){var char;if("+"!==(char=val_.charAt(i))||i){var tmp=pattern+("8"!==char||i&&(1!=i||"+"!==newcode)?char:"[78]"),reg=new RegExp("^"+tmp);if(!phone_code_mask.match(reg))break;pattern=tmp,newcode+=char}else newcode+=char}$input.data("code",newcode),phone_code_mask.match(new RegExp("^"+pattern+"$"))&&$input.val(val_.substr(newcode.length))}var now_is_phone_but_login_possible_mask=arAsproOptions.THEME.PHONE_MASK.replace(/\\9/g,"#").replace(/9/g,"*").replace(/#/g,"\\9");$input.inputmask("mask",{mask:now_is_phone_but_login_possible_mask,definitions:{"*":{validator:".*"}}})}}else test.bPossibleEmail?$parent.hasClass("phone_or_login-email")||($input.removeClass("phone").addClass("email"),$parent.removeClass("phone_or_login-phone").removeClass("phone_or_login-login").addClass("phone_or_login-email"),arAsproOptions.THEME.PHONE_MASK.length&&($input.inputmask("mask",{mask:""}),$(input).val(code+$(input).val()))):$parent.hasClass("phone_or_login-login")||($input.removeClass("phone").removeClass("email"),$parent.removeClass("phone_or_login-phone").removeClass("phone_or_login-email").addClass("phone_or_login-login"),arAsproOptions.THEME.PHONE_MASK.length&&($input.inputmask("mask",{mask:""}),$(input).val(code+$(input).val())));else($parent.hasClass("phone_or_login-login")||$parent.hasClass("phone_or_login-email")||$parent.hasClass("phone_or_login-phone"))&&($parent.removeClass("phone_or_login-phone").removeClass("phone_or_login-login").removeClass("phone_or_login-email"),$input.removeClass("phone").removeClass("email"),$input.inputmask("mask",{mask:""}),$input.data("code",""));"function"==typeof callback&&callback(input,test)}$(this).length&&$(this).each((function(){init(this,callback)}))};
//# sourceMappingURL=phoneorlogin.min.js.map