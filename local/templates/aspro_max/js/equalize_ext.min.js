$.fn.equalizeHeightsExt=function(e,t,i){for(var s=this.map(function(i,s){var n=0,r=0;return t!==!1&&(n=parseInt($(s).find(t).actual("outerHeight"))),n&&(n+=12),$(s).css("height",""),r=1==e?$(s).actual("outerHeight")-n:$(s).actual("height")-n}).get(),n=0,r=s.length;r>n;++n)s[n]%2&&--s[n];return this.height(Math.max.apply(this,s))},$.fn.sliceHeightExt=function(e){function t(t){if(t.each(function(){$(this).css("line-height",""),$(this).css("height","")}),"undefined"==typeof e.autoslicecount||e.autoslicecount!==!1){var i=t.first().hasClass("item")?t.first().outerWidth():t.first().parents(".item").outerWidth(),s=t.first().parents(".top_wrapper").outerWidth();i||(i=e.parent?t.closest(e.parent).outerWidth()-5:t.first().outerWidth()-5),s||(s=t.first().parents(".row").outerWidth()),s&&i&&(e.slice=Math.floor(s/i))}if(e.slice)for(var n=0;n<t.length;n+=e.slice)$(t.slice(n,n+e.slice)).equalizeHeightsExt(e.outer,e.classNull,e.minHeight);if(e.lineheight){var r=parseInt(e.lineheight);isNaN(r)&&(r=0),t.each(function(){$(this).css("line-height",$(this).actual("height")+r+"px")})}}var e=$.extend({slice:null,outer:!1,lineheight:!1,autoslicecount:!0,classNull:!1,minHeight:!1,options:!1,parent:!1},e),i=$(this);t(i),BX.addCustomEvent("onWindowResize",function(e){ignoreResize.push(!0),t(i),ignoreResize.pop()})};var timerResize=!1,ignoreResize=[];$(window).resize(function(){ignoreResize.length||(timerResize&&(clearTimeout(timerResize),timerResize=!1),timerResize=setTimeout(function(){BX.onCustomEvent("onWindowResize",!1)},100))});