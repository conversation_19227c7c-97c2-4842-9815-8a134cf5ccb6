!function(w){var a=!0;function T(e){var t={identifier:0,target:e.target,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY,pageX:e.pageX,pageY:e.pageY};return{touches:[t],targetTouches:[t],changedTouches:[t],altKey:e.alt<PERSON>ey,metaKey:e.meta<PERSON>ey,ctrlKey:e.ctrl<PERSON>ey,shiftKey:e.shift<PERSON>ey,target:e.target,preventDefault:e.preventDefault.bind(e),type:"mousedown"===e.type?"touchstart":"mousemove"===e.type?"touchmove":"mouseup"===e.type?"touchend":e.type,timeStamp:e.timeStamp||Date.now()}}w.flexslider=function(h,e){var y=w(h);y.vars=w.extend({},w.flexslider.defaults,e);var t,u=y.vars.namespace,S=window.navigator&&window.navigator.msPointerEnabled&&window.MSGesture,v=y.vars.touch,o="click touchend MSPointerUp keyup",l="",x="vertical"===y.vars.direction,b=y.vars.reverse,C=0<y.vars.itemWidth,N="fade"===y.vars.animation,p=""!==y.vars.asNavFor,m={};w.data(h,"flexslider",y),m={init:function(){y.animating=!1,y.currentSlide=parseInt(y.vars.startAt?y.vars.startAt:0,10),isNaN(y.currentSlide)&&(y.currentSlide=0),y.animatingTo=y.currentSlide,y.atEnd=0===y.currentSlide||y.currentSlide===y.last,y.containerSelector=y.vars.selector.substr(0,y.vars.selector.search(" ")),y.slides=w(y.vars.selector,y),y.container=w(y.containerSelector,y),y.count=y.slides.length,y.syncExists=0<w(y.vars.sync).length,"slide"===y.vars.animation&&(y.vars.animation="swing"),y.prop=x?"top":"marginLeft",y.args={},y.manualPause=!1,y.stopped=!1,y.started=!1,y.startTimeout=null,y.transitions=!y.vars.video&&!N&&y.vars.useCSS&&function(){var e=document.createElement("div"),t=["perspectiveProperty","WebkitPerspective","MozPerspective","OPerspective","msPerspective"];for(var a in t)if(void 0!==e.style[t[a]])return y.pfx=t[a].replace("Perspective","").toLowerCase(),y.prop="-"+y.pfx+"-transform",!0;return!1}(),(y.ensureAnimationEnd="")!==y.vars.controlsContainer&&(y.controlsContainer=0<w(y.vars.controlsContainer).length&&w(y.vars.controlsContainer)),""!==y.vars.manualControls&&(y.manualControls=0<w(y.vars.manualControls).length&&w(y.vars.manualControls)),""!==y.vars.customDirectionNav&&(y.customDirectionNav=2===w(y.vars.customDirectionNav).length&&w(y.vars.customDirectionNav)),y.vars.randomize&&(y.slides.sort(function(){return Math.round(Math.random())-.5}),y.container.empty().append(y.slides)),y.doMath(),y.setup("init"),y.vars.controlNav&&m.controlNav.setup(),y.vars.directionNav&&m.directionNav.setup(),y.vars.keyboard&&(1===w(y.containerSelector).length||y.vars.multipleKeyboard)&&w(document).bind("keyup",function(e){var t=e.keyCode;if(!y.animating&&(39===t||37===t)){var a=39===t?y.getTarget("next"):37===t&&y.getTarget("prev");y.flexAnimate(a,y.vars.pauseOnAction)}}),y.vars.mousewheel&&y.bind("mousewheel",function(e,t,a,n){e.preventDefault();var i=t<0?y.getTarget("next"):y.getTarget("prev");y.flexAnimate(i,y.vars.pauseOnAction)}),y.vars.pausePlay&&m.pausePlay.setup(),y.vars.slideshow&&y.vars.pauseInvisible&&m.pauseInvisible.init(),y.vars.slideshow&&(y.vars.pauseOnHover&&y.hover(function(){y.manualPlay||y.manualPause||y.pause()},function(){y.manualPause||y.manualPlay||y.stopped||y.play()}),y.vars.pauseInvisible&&m.pauseInvisible.isHidden()||(0<y.vars.initDelay?y.startTimeout=setTimeout(y.play,y.vars.initDelay):y.play())),p&&m.asNav.setup(),v&&y.vars.touch&&m.touch(),(!N||N&&y.vars.smoothHeight)&&w(window).bind("resize orientationchange",m.resize),y.find("img").attr("draggable","false"),setTimeout(function(){y.vars.start(y)},200)},asNav:{setup:function(){y.asNav=!0,y.animatingTo=Math.floor(y.currentSlide/y.move),y.currentItem=y.currentSlide,y.slides.removeClass(u+"active-slide").eq(y.currentItem).addClass(u+"active-slide"),S?(h._slider=y).slides.each(function(){var e=this;e._gesture=new MSGesture,(e._gesture.target=e).addEventListener("MSPointerDown",function(e){e.preventDefault(),e.currentTarget._gesture&&e.currentTarget._gesture.addPointer(e.pointerId)},!1),e.addEventListener("MSGestureTap",function(e){e.preventDefault();var t=w(this),a=t.index();w(y.vars.asNavFor).data("flexslider").animating||t.hasClass("active")||(y.direction=y.currentItem<a?"next":"prev",y.flexAnimate(a,y.vars.pauseOnAction,!1,!0,!0))})}):y.slides.on(o,function(e){e.preventDefault();var t=w(this),a=t.index();t.offset().left-w(y).scrollLeft()<=0&&t.hasClass(u+"active-slide")?y.flexAnimate(y.getTarget("prev"),!0):w(y.vars.asNavFor).data("flexslider").animating||t.hasClass(u+"active-slide")||(y.direction=y.currentItem<a?"next":"prev",y.flexAnimate(a,y.vars.pauseOnAction,!1,!0,!0))})}},controlNav:{setup:function(){y.manualControls?m.controlNav.setupManual():m.controlNav.setupPaging()},setupPaging:function(){var e,t,a="thumbnails"===y.vars.controlNav?"control-thumbs":"control-paging",n=1;if(y.controlNavScaffold=w('<ol class="'+u+"control-nav "+u+a+'"></ol>'),1<y.pagingCount)for(var i=0;i<y.pagingCount;i++){void 0===(t=y.slides.eq(i)).attr("data-thumb-alt")&&t.attr("data-thumb-alt","");var r=""!==t.attr("data-thumb-alt")?r=' alt="'+t.attr("data-thumb-alt")+'"':"";if(e="thumbnails"===y.vars.controlNav?'<img src="'+t.attr("data-thumb")+'"'+r+"/>":'<a href="#">'+n+"</a>","thumbnails"===y.vars.controlNav&&!0===y.vars.thumbCaptions){var s=t.attr("data-thumbcaption");""!==s&&void 0!==s&&(e+='<span class="'+u+'caption">'+s+"</span>")}y.controlNavScaffold.append("<li>"+e+"</li>"),n++}y.controlsContainer?w(y.controlsContainer).append(y.controlNavScaffold):y.append(y.controlNavScaffold),m.controlNav.set(),m.controlNav.active(),y.controlNavScaffold.delegate("a, img",o,function(e){if(e.preventDefault(),""===l||l===e.type){var t=w(this),a=y.controlNav.index(t);t.hasClass(u+"active")||(y.direction=a>y.currentSlide?"next":"prev",y.flexAnimate(a,y.vars.pauseOnAction))}""===l&&(l=e.type),m.setToClearWatchedEvent()})},setupManual:function(){y.controlNav=y.manualControls,m.controlNav.active(),y.controlNav.bind(o,function(e){if(e.preventDefault(),""===l||l===e.type){var t=w(this),a=y.controlNav.index(t);t.hasClass(u+"active")||(a>y.currentSlide?y.direction="next":y.direction="prev",y.flexAnimate(a,y.vars.pauseOnAction))}""===l&&(l=e.type),m.setToClearWatchedEvent()})},set:function(){var e="thumbnails"===y.vars.controlNav?"img":"a";y.controlNav=w("."+u+"control-nav li "+e,y.controlsContainer?y.controlsContainer:y)},active:function(){y.controlNav.removeClass(u+"active").eq(y.animatingTo).addClass(u+"active")},update:function(e,t){1<y.pagingCount&&"add"===e?y.controlNavScaffold.append(w('<li><a href="#">'+y.count+"</a></li>")):1===y.pagingCount?y.controlNavScaffold.find("li").remove():y.controlNav.eq(t).closest("li").remove(),m.controlNav.set(),1<y.pagingCount&&y.pagingCount!==y.controlNav.length?y.update(t,e):m.controlNav.active()}},directionNav:{setup:function(){var e=w('<ul class="'+u+'direction-nav"><li class="'+u+'nav-prev"><a class="'+u+'prev" href="#">'+y.vars.prevText+'</a></li><li class="'+u+'nav-next"><a class="'+u+'next" href="#">'+y.vars.nextText+"</a></li></ul>");y.customDirectionNav?y.directionNav=y.customDirectionNav:y.controlsContainer?(w(y.controlsContainer).append(e),y.directionNav=w("."+u+"direction-nav li a",y.controlsContainer)):(y.append(e),y.directionNav=w("."+u+"direction-nav li a",y)),m.directionNav.update(),y.directionNav.bind(o,function(e){var t;e.preventDefault(),""!==l&&l!==e.type||(t=w(this).hasClass(u+"next")?y.getTarget("next"):y.getTarget("prev"),y.flexAnimate(t,y.vars.pauseOnAction)),""===l&&(l=e.type),m.setToClearWatchedEvent()})},update:function(){var e=u+"disabled";1===y.pagingCount||y.pagingCount<1?(y.directionNav.addClass(e).attr("tabindex","-1"),y.directionNav.parent().addClass(e)):y.vars.animationLoop?(y.directionNav.removeClass(e).removeAttr("tabindex"),y.directionNav.parent().removeClass(e)):0===y.animatingTo?(y.directionNav.removeClass(e).filter("."+u+"prev").addClass(e).attr("tabindex","-1"),y.directionNav.parent().removeClass(e),y.directionNav.filter("."+u+"prev").parent().addClass(e)):y.animatingTo===y.last?(y.directionNav.removeClass(e).filter("."+u+"next").addClass(e).attr("tabindex","-1"),y.directionNav.parent().removeClass(e),y.directionNav.filter("."+u+"next").parent().addClass(e)):(y.directionNav.removeClass(e).removeAttr("tabindex"),y.directionNav.parent().removeClass(e))}},pausePlay:{setup:function(){var e=w('<div class="'+u+'pauseplay"><a href="#"></a></div>');y.controlsContainer?(y.controlsContainer.append(e),y.pausePlay=w("."+u+"pauseplay a",y.controlsContainer)):(y.append(e),y.pausePlay=w("."+u+"pauseplay a",y)),m.pausePlay.update(y.vars.slideshow?u+"pause":u+"play"),y.pausePlay.bind(o,function(e){e.preventDefault(),""!==l&&l!==e.type||(w(this).hasClass(u+"pause")?(y.manualPause=!0,y.manualPlay=!1,y.pause()):(y.manualPause=!1,y.manualPlay=!0,y.play())),""===l&&(l=e.type),m.setToClearWatchedEvent()})},update:function(e){"play"===e?y.pausePlay.removeClass(u+"pause").addClass(u+"play").html(y.vars.playText):y.pausePlay.removeClass(u+"play").addClass(u+"pause").html(y.vars.pauseText)}},touch:function(){var i,r,s,o,l,c,t,d,u,e,v,p,m=!1,a=0,n=0,f=0,g=y.vars.simulateTouch;if(S){h.style.msTouchAction="none",h._gesture=new MSGesture,(h._gesture.target=h).addEventListener("MSPointerDown",function(e){e.stopPropagation(),y.animating?e.preventDefault():(y.pause(),h._gesture.addPointer(e.pointerId),f=0,o=x?y.h:y.w,c=Number(new Date),s=C&&b&&y.animatingTo===y.last?0:C&&b?y.limit-(y.itemW+y.vars.itemMargin)*y.move*y.animatingTo:C&&y.currentSlide===y.last?y.limit:C?(y.itemW+y.vars.itemMargin)*y.move*y.currentSlide:b?(y.last-y.currentSlide+y.cloneOffset)*o:(y.currentSlide+y.cloneOffset)*o)},!1),h._slider=y,h.addEventListener("MSGestureChange",function(e){e.stopPropagation();var t=e.target._slider;if(!t)return;var a=-e.translationX,n=-e.translationY;if(l=f+=x?n:a,m=x?Math.abs(f)<Math.abs(-a):Math.abs(f)<Math.abs(-n),e.detail===e.MSGESTURE_FLAG_INERTIA)return void setImmediate(function(){h._gesture.stop()});(!m||500<Number(new Date)-c)&&(e.preventDefault(),!N&&t.transitions&&(t.vars.animationLoop||(l=f/(0===t.currentSlide&&f<0||t.currentSlide===t.last&&0<f?Math.abs(f)/o+2:1)),t.setProps(s+l,"setTouch")))},!1),h.addEventListener("MSGestureEnd",function(e){e.stopPropagation();var t=e.target._slider;if(!t)return;if(t.animatingTo===t.currentSlide&&!m&&null!==l){var a=b?-l:l,n=0<a?t.getTarget("next"):t.getTarget("prev");t.canAdvance(n)&&(Number(new Date)-c<550&&50<Math.abs(a)||Math.abs(a)>o/2)?t.flexAnimate(n,t.vars.pauseOnAction):N||t.flexAnimate(t.currentSlide,t.vars.pauseOnAction,!0)}s=l=r=i=null,f=0},!1)}else t=function(e){var t="touches"in e&&"A"==w(e.target)[0].nodeName;y.animating?e.preventDefault():!window.navigator.msPointerEnabled&&1!==e.touches.length||(y.pause(),o=x?y.h:y.w,c=Number(new Date),a=e.touches[0].pageX,n=e.touches[0].pageY,s=C&&b&&y.animatingTo===y.last?0:C&&b?y.limit-(y.itemW+y.vars.itemMargin)*y.move*y.animatingTo:C&&y.currentSlide===y.last?y.limit:C?(y.itemW+y.vars.itemMargin)*y.move*y.currentSlide:b?(y.last-y.currentSlide+y.cloneOffset)*o:(y.currentSlide+y.cloneOffset)*o,i=x?n:a,r=x?a:n,h.addEventListener("touchmove",d,!1),h.addEventListener("touchend",u,!1),g&&!t&&(h.addEventListener("mousemove",v,!1),h.addEventListener("mouseup",p,!1)))},d=function(e){a=e.touches[0].pageX,n=e.touches[0].pageY,l=x?i-n:i-a;(!(m=x?Math.abs(l)<Math.abs(a-r):Math.abs(l)<Math.abs(n-r))||500<Number(new Date)-c)&&(e.preventDefault(),!N&&y.transitions&&(y.vars.animationLoop||(l/=0===y.currentSlide&&l<0||y.currentSlide===y.last&&0<l?Math.abs(l)/o+2:1),y.setProps(s+l,"setTouch")))},u=function(e){var t="touches"in e&&"A"==w(e.target)[0].nodeName;if(h.removeEventListener("touchmove",d,!1),g&&!t&&h.removeEventListener("mousemove",v,!1),y.animatingTo===y.currentSlide&&!m&&null!==l){var a=b?-l:l,n=0<a?y.getTarget("next"):y.getTarget("prev");y.canAdvance(n)&&(Number(new Date)-c<550&&50<Math.abs(a)||Math.abs(a)>o/2)?y.flexAnimate(n,y.vars.pauseOnAction):N||y.flexAnimate(y.currentSlide,y.vars.pauseOnAction,!0)}h.removeEventListener("touchend",u,!1),g&&!t&&h.removeEventListener("mouseup",p,!1),s=l=r=i=null},e=function(e){t.call(this,T(e))},v=function(e){d.call(this,T(e))},p=function(e){u.call(this,T(e))},h.addEventListener("touchstart",t,!1),g&&h.addEventListener("mousedown",e,!1)},resize:function(){!y.animating&&y.is(":visible")&&(void 0!==y.vars.autoHeight&&y.vars.autoHeight&&y.viewport.find("li").css("height",""),C||y.doMath(),N?m.smoothHeight():C?(y.slides.width(y.computedW),y.update(y.pagingCount)):x?(y.viewport.height(y.h),y.setProps(y.h,"setTotal"),y.viewport.find("li").css("height",y.h)):(y.vars.smoothHeight&&m.smoothHeight(),y.newSlides.width(y.computedW),y.setProps(y.computedW,"setTotal")))},smoothHeight:function(e){if(!x||N){var t=N?y:y.viewport;e?t.animate({height:y.slides.eq(y.animatingTo).innerHeight()},e):t.innerHeight(y.slides.eq(y.animatingTo).innerHeight())}},sync:function(e){var t=w(y.vars.sync).data("flexslider"),a=y.animatingTo;switch(e){case"animate":t.flexAnimate(a,y.vars.pauseOnAction,!1,!0);break;case"play":t.playing||t.asNav||t.play();break;case"pause":t.pause()}},uniqueID:function(e){return e.filter("[id]").add(e.find("[id]")).each(function(){var e=w(this);e.attr("id",e.attr("id")+"_clone")}),e},pauseInvisible:{visProp:null,init:function(){var e=m.pauseInvisible.getHiddenProp();if(e){var t=e.replace(/[H|h]idden/,"")+"visibilitychange";document.addEventListener(t,function(){m.pauseInvisible.isHidden()?y.startTimeout?clearTimeout(y.startTimeout):y.pause():y.started?y.play():0<y.vars.initDelay?setTimeout(y.play,y.vars.initDelay):y.play()})}},isHidden:function(){var e=m.pauseInvisible.getHiddenProp();return!!e&&document[e]},getHiddenProp:function(){var e=["webkit","moz","ms","o"];if("hidden"in document)return"hidden";for(var t=0;t<e.length;t++)if(e[t]+"Hidden"in document)return e[t]+"Hidden";return null}},setToClearWatchedEvent:function(){clearTimeout(t),t=setTimeout(function(){l=""},3e3)}},y.flexAnimate=function(e,t,a,n,i){if(y.vars.animationLoop||e===y.currentSlide||(y.direction=e>y.currentSlide?"next":"prev"),p&&1===y.pagingCount&&(y.direction=y.currentItem<e?"next":"prev"),!y.animating&&(y.canAdvance(e,i)||a)){if(p&&n){var r=w(y.vars.asNavFor).data("flexslider");y.atEnd=0===e||e===y.count-1,r.flexAnimate(e,!0,!1,!0,i),y.direction=y.currentItem<e?"next":"prev",r.direction=y.direction,y.currentItem=e,y.slides.removeClass(u+"active-slide").eq(e).addClass(u+"active-slide");var s=0<Math.floor((e+1)/y.visible)?Math.floor((e+1-y.visible)/y.move):0;if(s==y.currentSlide)return!1;e=s}if(y.animating=!0,y.animatingTo=e,t&&y.pause(),y.vars.before(y),y.syncExists&&!i&&m.sync("animate"),y.vars.controlNav&&m.controlNav.active(),C||y.slides.removeClass(u+"active-slide").eq(e).addClass(u+"active-slide"),y.atEnd=0===e||e===y.last,y.vars.directionNav&&m.directionNav.update(),e===y.last&&(y.vars.end(y),y.vars.animationLoop||y.pause()),N)v?(y.slides.eq(y.currentSlide).css({opacity:0,zIndex:1}),y.slides.eq(e).css({opacity:1,zIndex:2}),y.wrapup(d)):(y.slides.eq(y.currentSlide).css({zIndex:1}).animate({opacity:0},y.vars.animationSpeed,y.vars.easing),y.slides.eq(e).css({zIndex:2}).animate({opacity:1},y.vars.animationSpeed,y.vars.easing,y.wrapup));else{var o,l,c,d=x?y.slides.filter(":first").height():y.computedW;l=C?(o=y.vars.itemMargin,(c=(y.itemW+o)*y.move*y.animatingTo)>y.limit&&1!==y.visible?y.limit:c):0===y.currentSlide&&e===y.count-1&&y.vars.animationLoop&&"next"!==y.direction?b?(y.count+y.cloneOffset)*d:0:y.currentSlide===y.last&&0===e&&y.vars.animationLoop&&"prev"!==y.direction?b?0:(y.count+1)*d:b?(y.count-1-e+y.cloneOffset)*d:(e+y.cloneOffset)*d,y.setProps(l,"",y.vars.animationSpeed),y.transitions?(y.vars.animationLoop&&y.atEnd||(y.animating=!1,y.currentSlide=y.animatingTo),y.container.unbind("webkitTransitionEnd transitionend"),y.container.bind("webkitTransitionEnd transitionend",function(){clearTimeout(y.ensureAnimationEnd),y.wrapup(d)}),clearTimeout(y.ensureAnimationEnd),y.ensureAnimationEnd=setTimeout(function(){y.wrapup(d)},y.vars.animationSpeed+100)):y.container.animate(y.args,y.vars.animationSpeed,y.vars.easing,function(){y.wrapup(d)})}y.vars.smoothHeight&&m.smoothHeight(y.vars.animationSpeed)}},y.wrapup=function(e){N||C||(0===y.currentSlide&&y.animatingTo===y.last&&y.vars.animationLoop?y.setProps(e,"jumpEnd"):y.currentSlide===y.last&&0===y.animatingTo&&y.vars.animationLoop&&y.setProps(e,"jumpStart")),y.animating=!1,y.currentSlide=y.animatingTo,y.vars.after(y)},y.animateSlides=function(){!y.animating&&a&&y.flexAnimate(y.getTarget("next"))},y.pause=function(){clearInterval(y.animatedSlides),y.animatedSlides=null,y.playing=!1,y.vars.pausePlay&&m.pausePlay.update("play"),y.syncExists&&m.sync("pause")},y.play=function(){y.playing&&clearInterval(y.animatedSlides),y.animatedSlides=y.animatedSlides||setInterval(y.animateSlides,y.vars.slideshowSpeed),y.started=y.playing=!0,y.vars.pausePlay&&m.pausePlay.update("pause"),y.syncExists&&m.sync("play")},y.smoothHeight=function(){m.smoothHeight()},y.stop=function(){y.pause(),y.stopped=!0},y.canAdvance=function(e,t){var a=p?y.pagingCount-1:y.last;return!!t||(p&&y.currentItem===y.count-1&&0===e&&"prev"===y.direction||(!p||0!==y.currentItem||e!==y.pagingCount-1||"next"===y.direction)&&((e!==y.currentSlide||p)&&(!!y.vars.animationLoop||(!y.atEnd||0!==y.currentSlide||e!==a||"next"===y.direction)&&(!y.atEnd||y.currentSlide!==a||0!==e||"next"!==y.direction))))},y.getTarget=function(e){return"next"===(y.direction=e)?y.currentSlide===y.last?0:y.currentSlide+1:0===y.currentSlide?y.last:y.currentSlide-1},y.setProps=function(e,t,a){var n,i=(n=e||(y.itemW+y.vars.itemMargin)*y.move*y.animatingTo,-1*function(){if(C)return"setTouch"===t?e:b&&y.animatingTo===y.last?0:b?y.limit-(y.itemW+y.vars.itemMargin)*y.move*y.animatingTo:y.animatingTo===y.last?y.limit:n;switch(t){case"setTotal":return b?(y.count-1-y.currentSlide+y.cloneOffset)*e:(y.currentSlide+y.cloneOffset)*e;case"setTouch":return e;case"jumpEnd":return b?e:y.count*e;case"jumpStart":return b?y.count*e:e;default:return e}}()+"px");y.transitions&&(i=x?"translate3d(0,"+i+",0)":"translate3d("+i+",0,0)",a=void 0!==a?a/1e3+"s":"0s",y.container.css("-"+y.pfx+"-transition-duration",a),y.container.css("transition-duration",a)),y.args[y.prop]=i,!y.transitions&&void 0!==a||y.container.css(y.args),y.container.css("transform",i)},y.setup=function(e){var t,a;N?(y.slides.css({width:"100%",float:"left",marginRight:"-100%",position:"relative"}),"init"===e&&(v?y.slides.css({opacity:0,display:"block",webkitTransition:"opacity "+y.vars.animationSpeed/1e3+"s ease",zIndex:1}).eq(y.currentSlide).css({opacity:1,zIndex:2}):0==y.vars.fadeFirstSlide?y.slides.css({opacity:0,display:"block",zIndex:1}).eq(y.currentSlide).css({zIndex:2}).css({opacity:1}):y.slides.css({opacity:0,display:"block",zIndex:1}).eq(y.currentSlide).css({zIndex:2}).animate({opacity:1},y.vars.animationSpeed,y.vars.easing)),y.vars.smoothHeight&&m.smoothHeight()):("init"===e&&(y.viewport=w('<div class="'+u+'viewport"></div>').css({overflow:"hidden",position:"relative"}).appendTo(y).append(y.container),y.cloneCount=0,y.cloneOffset=0,b&&(a=w.makeArray(y.slides).reverse(),y.slides=w(a),y.container.empty().append(y.slides))),y.vars.animationLoop&&!C&&(y.cloneCount=2,y.cloneOffset=1,y.viewport.find("li").css("height",y.h),"init"!==e&&y.container.find(".clone").remove(),y.container.append(m.uniqueID(y.slides.first().clone().addClass("clone")).attr("aria-hidden","true")).prepend(m.uniqueID(y.slides.last().clone().addClass("clone")).attr("aria-hidden","true"))),y.newSlides=w(y.vars.selector,y),t=b?y.count-1-y.currentSlide+y.cloneOffset:y.currentSlide+y.cloneOffset,x&&!C?(y.container.height(200*(y.count+y.cloneCount)+"%").css("position","absolute").width("100%"),setTimeout(function(){y.newSlides.css({display:"block"}),y.doMath(),y.viewport.height(y.h),y.setProps(t*y.h,"init")},"init"===e?100:0)):(y.container.width(200*(y.count+y.cloneCount)+"%"),y.setProps(t*y.computedW,"init"),setTimeout(function(){y.doMath(),y.newSlides.css({width:y.computedW,marginRight:y.computedM,float:"left",display:"block"}),y.vars.smoothHeight&&m.smoothHeight()},"init"===e?100:0)));C||y.slides.removeClass(u+"active-slide").eq(y.currentSlide).addClass(u+"active-slide"),y.vars.init(y)},y.doMath=function(){var e=y.slides.first(),t=y.vars.itemMargin,a=y.vars.minItems,n=y.vars.maxItems;y.w=void 0===y.viewport?y.width():y.viewport.width(),y.h=e.height();var i=0;x&&(y.slides.each(function(e,t){var a=w(t).height();i<a&&(i=a)}),y.h=i),y.boxPadding=e.outerWidth()-e.width(),C?(y.itemT=y.vars.itemWidth+t,y.itemM=t,y.minW=a?a*y.itemT:y.w,y.maxW=n?n*y.itemT-t:y.w,y.itemW=y.minW>y.w?(y.w-t*(a-1))/a:y.maxW<y.w?(y.w-t*(n-1))/n:y.vars.itemWidth>y.w?y.w:y.vars.itemWidth,y.visible=Math.floor(y.w/y.itemW),y.move=0<y.vars.move&&y.vars.move<y.visible?y.vars.move:y.visible,y.pagingCount=Math.ceil((y.count-y.visible)/y.move+1),y.last=y.pagingCount-1,y.limit=1===y.pagingCount?0:y.vars.itemWidth>y.w?y.itemW*(y.count-1)+t*(y.count-1):(y.itemW+t)*y.count-y.w-t):(y.itemW=y.w,y.itemM=t,y.pagingCount=y.count,y.last=y.count-1),y.computedW=y.itemW-y.boxPadding,y.computedM=y.itemM},y.update=function(e,t){y.doMath(),C||(e<y.currentSlide?y.currentSlide+=1:e<=y.currentSlide&&0!==e&&(y.currentSlide-=1),y.animatingTo=y.currentSlide),y.vars.controlNav&&!y.manualControls&&("add"===t&&!C||y.pagingCount>y.controlNav.length?m.controlNav.update("add"):("remove"===t&&!C||y.pagingCount<y.controlNav.length)&&m.controlNav.update("remove",y.last)),y.vars.directionNav&&m.directionNav.update()},y.addSlide=function(e,t){var a=w(e);y.count+=1,y.last=y.count-1,x&&b?void 0!==t?y.slides.eq(y.count-t).after(a):y.container.prepend(a):void 0!==t?y.slides.eq(t).before(a):y.container.append(a),y.update(t,"add"),y.slides=w(y.vars.selector+":not(.clone)",y),y.setup(),y.vars.added(y)},y.removeSlide=function(e){var t=isNaN(e)?y.slides.index(w(e)):e;y.count-=1,y.last=y.count-1,isNaN(e)?w(e,y.slides).remove():x&&b?y.slides.eq(y.last).remove():y.slides.eq(e).remove(),y.doMath(),y.update(t,"remove"),y.slides=w(y.vars.selector+":not(.clone)",y),y.setup(),y.vars.removed(y)},m.init()},w(window).blur(function(e){a=!1}).focus(function(e){a=!0}),w.flexslider.defaults={namespace:"flex-",selector:".slides > li",animation:"fade",easing:"swing",direction:"horizontal",reverse:!1,animationLoop:!0,smoothHeight:!1,startAt:0,slideshow:!0,slideshowSpeed:7e3,animationSpeed:600,initDelay:0,randomize:!1,fadeFirstSlide:!0,thumbCaptions:!1,pauseOnAction:!0,pauseOnHover:!1,pauseInvisible:!0,useCSS:!0,touch:!0,video:!1,simulateTouch:!1,controlNav:!0,directionNav:!0,prevText:"Previous",nextText:"Next",keyboard:!0,multipleKeyboard:!1,mousewheel:!1,pausePlay:!1,pauseText:"Pause",playText:"Play",controlsContainer:"",manualControls:"",customDirectionNav:"",sync:"",asNavFor:"",itemWidth:0,itemMargin:0,minItems:1,maxItems:0,move:0,allowOneSlide:!0,start:function(){},before:function(){},after:function(){},end:function(){},added:function(){},removed:function(){},init:function(){}},w.fn.flexslider=function(n){if(void 0===n&&(n={}),"object"==typeof n)return this.each(function(){var e=w(this),t=n.selector?n.selector:".slides > li",a=e.find(t);1===a.length&&!1===n.allowOneSlide||0===a.length?(a.fadeIn(400),n.start&&n.start(e)):void 0===e.data("flexslider")&&new w.flexslider(this,n)});var e=w(this).data("flexslider");switch(n){case"play":e.play();break;case"pause":e.pause();break;case"stop":e.stop();break;case"next":e.flexAnimate(e.getTarget("next"),!0);break;case"prev":case"previous":e.flexAnimate(e.getTarget("prev"),!0);break;default:"number"==typeof n&&e.flexAnimate(n,!0)}}}(jQuery);