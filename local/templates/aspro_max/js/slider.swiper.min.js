function initSwiperSlider(selector){const $slider=$(selector||".swiper-container");$slider.each((function(){const _this=$(this);let options={grabCursor:!0,navigation:{nextEl:_this.find(".swiper-button-next")[0],prevEl:_this.find(".swiper-button-prev")[0]},pagination:{el:".swiper-pagination",type:"bullets",clickable:!0}};_this.data("pluginOptions")&&(options=deepMerge({},options,_this.data("pluginOptions"))),BX.onCustomEvent("onSetSliderOptions",[options]);const swiper=new Swiper(this,options);swiper.on("slideChange",(function(swiper){const eventdata={slider:swiper};BX.onCustomEvent("onSlideChanges",[eventdata])})),!1===options.init&&(swiper.on("init",(function(swiper){const eventdata={slider:swiper,options:options};BX.onCustomEvent("onInitSlider",[eventdata]),1===$slider.length&&BX.onCustomEvent("onSlideChanges",[{slider:swiper}])})),swiper.init()),_this.data("swiper",swiper)}))}function deepMerge(){const arr=[].slice.call(arguments);let destination=arr[0];const other=arr.slice(1);return other.forEach((function(params){for(let param in params)if("object"==typeof params[param])for(let param2 in params[param])"object"!=typeof destination[param]&&(destination[param]={}),destination[param][param2]=params[param][param2];else destination[param]=params[param]})),destination}readyDOM((function(){initSwiperSlider()}));
//# sourceMappingURL=slider.swiper.min.js.map