"undefined"==typeof AMutationObserver&&"function"==typeof MutationObserver&&function(w,d){"use strict";var _AMutationObserver=function(_MutationObserver){function _AMutationObserver(callback){var _this;_this=_MutationObserver.call(this,(function(mutationsList,observer){_AMutationObserver.ignore||observer.paused||callback(mutationsList,observer)}))||this,_this.paused=!0,_this.id=Math.floor(Math.random()*Math.floor(1e3));try{eval("@")}catch(e){if("string"==typeof e.stack){_this.stack=e.stack.match(/[^\s]*:[\d]+:[\d]+[^\s]/g),_this.stack.splice(0,1);var script=_this.stack[0];-1!==script.indexOf("yandex.ru/metrika")?_this.id="ym":(script=script.replace(/^(.*[/])?(.*\.js)(.*)$/gi,"$2"),_this.id=script.replace(/(\.min)?\.js$/i,""))}}return _this}_inheritsLoose(_AMutationObserver,_MutationObserver);var _proto=_AMutationObserver.prototype;return _proto.observe=function observe(target,options){this.target=target,this.options=options,this.paused=!1,void 0===_AMutationObserver.observers[this.id]&&(_AMutationObserver.observers[this.id]=this),_MutationObserver.prototype.observe.call(this,target,options)},_proto.disconnect=function disconnect(){this.paused=!0,_MutationObserver.prototype.disconnect.call(this)},_proto.takeRecords=function takeRecords(){return _AMutationObserver.ignore||this.paused?[]:_MutationObserver.prototype.takeRecords.call(this)},_proto.pause=function pause(){this.paused=!0},_proto.resume=function resume(){"false"!=typeof this.options&&(this.paused=!1)},_AMutationObserver}(_wrapNativeSuper(MutationObserver));function _inheritsLoose(subClass,superClass){subClass.prototype=Object.create(superClass.prototype),subClass.prototype.constructor=subClass,subClass.__proto__=superClass}function _wrapNativeSuper(Class){var _cache="function"==typeof Map?new Map:void 0;return(_wrapNativeSuper=function _wrapNativeSuper(Class){if(null===Class||!_isNativeFunction(Class))return Class;if("function"!=typeof Class)throw new TypeError("Super expression must either be null or a function");if(void 0!==_cache){if(_cache.has(Class))return _cache.get(Class);_cache.set(Class,Wrapper)}function Wrapper(){return _construct(Class,arguments,_getPrototypeOf(this).constructor)}return Wrapper.prototype=Object.create(Class.prototype,{constructor:{value:Wrapper,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(Wrapper,Class)})(Class)}function _construct(Parent,args,Class){return(_construct=_isNativeReflectConstruct()?Reflect.construct:function _construct(Parent,args,Class){var a=[null];a.push.apply(a,args);var Constructor,instance=new(Function.bind.apply(Parent,a));return Class&&_setPrototypeOf(instance,Class.prototype),instance}).apply(null,arguments)}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function _isNativeFunction(fn){return-1!==Function.toString.call(fn).indexOf("[native code]")}function _setPrototypeOf(o,p){return(_setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(o,p){return o.__proto__=p,o})(o,p)}function _getPrototypeOf(o){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function _getPrototypeOf(o){return o.__proto__||Object.getPrototypeOf(o)})(o)}_AMutationObserver.ignore=!1,_AMutationObserver.observers=[],_AMutationObserver.parent=MutationObserver,_AMutationObserver.toString=function(){return"function MutationObserver() { [native code] }"},MutationObserver=_AMutationObserver}(window,document);
//# sourceMappingURL=observer.min.js.map