!function(w){w.fn.menuAim=function(e){return this.each(function(){(function(e){var m=w(this),x=null,y=[],p=null,n=null,d=w.extend({firstActiveRow:!1,rowSelector:"> li",submenuSelector:"*",submenuDirection:"right",tolerance:5,enter:w.noop,exit:w.noop,activate:w.noop,deactivate:w.noop,exitMenu:w.noop},e),i=function(e){e!=x&&(x&&d.deactivate(x),d.activate(e),x=e)},o=function(e){var t=u();t?n=setTimeout(function(){o(e)},t):i(e)},u=function(){if(!x||!w(x).is(d.submenuSelector))return 0;var e=m.offset(),t={x:e.left,y:e.top-d.tolerance},n={x:e.left+m.outerWidth(),y:t.y},i={x:e.left,y:e.top+m.outerHeight()+d.tolerance},o={x:e.left+m.outerWidth(),y:i.y},u=y[y.length-1],r=y[0];if(!u)return 0;if(r||(r=u),r.x<e.left||r.x>o.x||r.y<e.top||r.y>o.y)return 0;if(p&&u.x==p.x&&u.y==p.y)return 0;function c(e,t){return(t.y-e.y)/(t.x-e.x)}var l=n,f=o;"left"==d.submenuDirection?(l=i,f=t):"below"==d.submenuDirection?(l=o,f=i):"above"==d.submenuDirection&&(l=t,f=n);var s=c(u,l),a=c(u,f),h=c(r,l),v=c(r,f);return s<h&&v<a?(p=u,3e3):(p=null,0)};m.mouseleave(function(){n&&clearTimeout(n),d.exitMenu(this)&&(x&&d.deactivate(x),x=null)}).find(d.rowSelector).mouseenter(function(){n&&clearTimeout(n),d.enter(this),o(this)}).mouseleave(function(){d.exit(this)}).click(function(){i(this)}),"firstActiveRow"in d&&(m.find(d.rowSelector).hasClass("active")||(!0===d.firstActiveRow?i(m.find(d.rowSelector).eq(0)):i(d.firstActiveRow)));w(document).mousemove(function(e){y.push({x:e.pageX,y:e.pageY}),3<y.length&&y.shift()})}).call(this,e)}),this}}(jQuery);