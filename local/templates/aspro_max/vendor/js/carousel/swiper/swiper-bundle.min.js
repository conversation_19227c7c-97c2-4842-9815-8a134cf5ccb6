/**
 * Swiper 6.7.0
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2021 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: June 1, 2021
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Swiper=t()}(this,(function(){"use strict";function e(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function t(){return(t=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(e[i]=a[i])}return e}).apply(this,arguments)}function a(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function i(e,t){void 0===e&&(e={}),void 0===t&&(t={}),Object.keys(t).forEach((function(n){void 0===e[n]?e[n]=t[n]:a(t[n])&&a(e[n])&&Object.keys(t[n]).length>0&&i(e[n],t[n])}))}var n={body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},createElementNS:function(){return{}},importNode:function(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function s(){var e="undefined"!=typeof document?document:{};return i(e,n),e}var r={document:n,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState:function(){},pushState:function(){},go:function(){},back:function(){}},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){},matchMedia:function(){return{}},requestAnimationFrame:function(e){return"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0)},cancelAnimationFrame:function(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function o(){var e="undefined"!=typeof window?window:{};return i(e,r),e}function l(e){return(l=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function d(e,t){return(d=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function p(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function u(e,t,a){return(u=p()?Reflect.construct:function(e,t,a){var i=[null];i.push.apply(i,t);var n=new(Function.bind.apply(e,i));return a&&d(n,a.prototype),n}).apply(null,arguments)}function c(e){var t="function"==typeof Map?new Map:void 0;return(c=function(e){if(null===e||(a=e,-1===Function.toString.call(a).indexOf("[native code]")))return e;var a;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,i)}function i(){return u(e,arguments,l(this).constructor)}return i.prototype=Object.create(e.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),d(i,e)})(e)}var v=function(e){var t,a;function i(t){var a,i,n;return a=e.call.apply(e,[this].concat(t))||this,i=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(a),n=i.__proto__,Object.defineProperty(i,"__proto__",{get:function(){return n},set:function(e){n.__proto__=e}}),a}return a=e,(t=i).prototype=Object.create(a.prototype),t.prototype.constructor=t,t.__proto__=a,i}(c(Array));function f(e){void 0===e&&(e=[]);var t=[];return e.forEach((function(e){Array.isArray(e)?t.push.apply(t,f(e)):t.push(e)})),t}function h(e,t){return Array.prototype.filter.call(e,t)}function m(e,t){var a=o(),i=s(),n=[];if(!t&&e instanceof v)return e;if(!e)return new v(n);if("string"==typeof e){var r=e.trim();if(r.indexOf("<")>=0&&r.indexOf(">")>=0){var l="div";0===r.indexOf("<li")&&(l="ul"),0===r.indexOf("<tr")&&(l="tbody"),0!==r.indexOf("<td")&&0!==r.indexOf("<th")||(l="tr"),0===r.indexOf("<tbody")&&(l="table"),0===r.indexOf("<option")&&(l="select");var d=i.createElement(l);d.innerHTML=r;for(var p=0;p<d.childNodes.length;p+=1)n.push(d.childNodes[p])}else n=function(e,t){if("string"!=typeof e)return[e];for(var a=[],i=t.querySelectorAll(e),n=0;n<i.length;n+=1)a.push(i[n]);return a}(e.trim(),t||i)}else if(e.nodeType||e===a||e===i)n.push(e);else if(Array.isArray(e)){if(e instanceof v)return e;n=e}return new v(function(e){for(var t=[],a=0;a<e.length;a+=1)-1===t.indexOf(e[a])&&t.push(e[a]);return t}(n))}m.fn=v.prototype;var g,y,b,w={addClass:function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];var i=f(t.map((function(e){return e.split(" ")})));return this.forEach((function(e){var t;(t=e.classList).add.apply(t,i)})),this},removeClass:function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];var i=f(t.map((function(e){return e.split(" ")})));return this.forEach((function(e){var t;(t=e.classList).remove.apply(t,i)})),this},hasClass:function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];var i=f(t.map((function(e){return e.split(" ")})));return h(this,(function(e){return i.filter((function(t){return e.classList.contains(t)})).length>0})).length>0},toggleClass:function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];var i=f(t.map((function(e){return e.split(" ")})));this.forEach((function(e){i.forEach((function(t){e.classList.toggle(t)}))}))},attr:function(e,t){if(1===arguments.length&&"string"==typeof e)return this[0]?this[0].getAttribute(e):void 0;for(var a=0;a<this.length;a+=1)if(2===arguments.length)this[a].setAttribute(e,t);else for(var i in e)this[a][i]=e[i],this[a].setAttribute(i,e[i]);return this},removeAttr:function(e){for(var t=0;t<this.length;t+=1)this[t].removeAttribute(e);return this},transform:function(e){for(var t=0;t<this.length;t+=1)this[t].style.transform=e;return this},transition:function(e){for(var t=0;t<this.length;t+=1)this[t].style.transitionDuration="string"!=typeof e?e+"ms":e;return this},on:function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];var i=t[0],n=t[1],s=t[2],r=t[3];function o(e){var t=e.target;if(t){var a=e.target.dom7EventData||[];if(a.indexOf(e)<0&&a.unshift(e),m(t).is(n))s.apply(t,a);else for(var i=m(t).parents(),r=0;r<i.length;r+=1)m(i[r]).is(n)&&s.apply(i[r],a)}}function l(e){var t=e&&e.target&&e.target.dom7EventData||[];t.indexOf(e)<0&&t.unshift(e),s.apply(this,t)}"function"==typeof t[1]&&(i=t[0],s=t[1],r=t[2],n=void 0),r||(r=!1);for(var d,p=i.split(" "),u=0;u<this.length;u+=1){var c=this[u];if(n)for(d=0;d<p.length;d+=1){var v=p[d];c.dom7LiveListeners||(c.dom7LiveListeners={}),c.dom7LiveListeners[v]||(c.dom7LiveListeners[v]=[]),c.dom7LiveListeners[v].push({listener:s,proxyListener:o}),c.addEventListener(v,o,r)}else for(d=0;d<p.length;d+=1){var f=p[d];c.dom7Listeners||(c.dom7Listeners={}),c.dom7Listeners[f]||(c.dom7Listeners[f]=[]),c.dom7Listeners[f].push({listener:s,proxyListener:l}),c.addEventListener(f,l,r)}}return this},off:function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];var i=t[0],n=t[1],s=t[2],r=t[3];"function"==typeof t[1]&&(i=t[0],s=t[1],r=t[2],n=void 0),r||(r=!1);for(var o=i.split(" "),l=0;l<o.length;l+=1)for(var d=o[l],p=0;p<this.length;p+=1){var u=this[p],c=void 0;if(!n&&u.dom7Listeners?c=u.dom7Listeners[d]:n&&u.dom7LiveListeners&&(c=u.dom7LiveListeners[d]),c&&c.length)for(var v=c.length-1;v>=0;v-=1){var f=c[v];s&&f.listener===s||s&&f.listener&&f.listener.dom7proxy&&f.listener.dom7proxy===s?(u.removeEventListener(d,f.proxyListener,r),c.splice(v,1)):s||(u.removeEventListener(d,f.proxyListener,r),c.splice(v,1))}}return this},trigger:function(){for(var e=o(),t=arguments.length,a=new Array(t),i=0;i<t;i++)a[i]=arguments[i];for(var n=a[0].split(" "),s=a[1],r=0;r<n.length;r+=1)for(var l=n[r],d=0;d<this.length;d+=1){var p=this[d];if(e.CustomEvent){var u=new e.CustomEvent(l,{detail:s,bubbles:!0,cancelable:!0});p.dom7EventData=a.filter((function(e,t){return t>0})),p.dispatchEvent(u),p.dom7EventData=[],delete p.dom7EventData}}return this},transitionEnd:function(e){var t=this;return e&&t.on("transitionend",(function a(i){i.target===this&&(e.call(this,i),t.off("transitionend",a))})),this},outerWidth:function(e){if(this.length>0){if(e){var t=this.styles();return this[0].offsetWidth+parseFloat(t.getPropertyValue("margin-right"))+parseFloat(t.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null},outerHeight:function(e){if(this.length>0){if(e){var t=this.styles();return this[0].offsetHeight+parseFloat(t.getPropertyValue("margin-top"))+parseFloat(t.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null},styles:function(){var e=o();return this[0]?e.getComputedStyle(this[0],null):{}},offset:function(){if(this.length>0){var e=o(),t=s(),a=this[0],i=a.getBoundingClientRect(),n=t.body,r=a.clientTop||n.clientTop||0,l=a.clientLeft||n.clientLeft||0,d=a===e?e.scrollY:a.scrollTop,p=a===e?e.scrollX:a.scrollLeft;return{top:i.top+d-r,left:i.left+p-l}}return null},css:function(e,t){var a,i=o();if(1===arguments.length){if("string"!=typeof e){for(a=0;a<this.length;a+=1)for(var n in e)this[a].style[n]=e[n];return this}if(this[0])return i.getComputedStyle(this[0],null).getPropertyValue(e)}if(2===arguments.length&&"string"==typeof e){for(a=0;a<this.length;a+=1)this[a].style[e]=t;return this}return this},each:function(e){return e?(this.forEach((function(t,a){e.apply(t,[t,a])})),this):this},html:function(e){if(void 0===e)return this[0]?this[0].innerHTML:null;for(var t=0;t<this.length;t+=1)this[t].innerHTML=e;return this},text:function(e){if(void 0===e)return this[0]?this[0].textContent.trim():null;for(var t=0;t<this.length;t+=1)this[t].textContent=e;return this},is:function(e){var t,a,i=o(),n=s(),r=this[0];if(!r||void 0===e)return!1;if("string"==typeof e){if(r.matches)return r.matches(e);if(r.webkitMatchesSelector)return r.webkitMatchesSelector(e);if(r.msMatchesSelector)return r.msMatchesSelector(e);for(t=m(e),a=0;a<t.length;a+=1)if(t[a]===r)return!0;return!1}if(e===n)return r===n;if(e===i)return r===i;if(e.nodeType||e instanceof v){for(t=e.nodeType?[e]:e,a=0;a<t.length;a+=1)if(t[a]===r)return!0;return!1}return!1},index:function(){var e,t=this[0];if(t){for(e=0;null!==(t=t.previousSibling);)1===t.nodeType&&(e+=1);return e}},eq:function(e){if(void 0===e)return this;var t=this.length;if(e>t-1)return m([]);if(e<0){var a=t+e;return m(a<0?[]:[this[a]])}return m([this[e]])},append:function(){for(var e,t=s(),a=0;a<arguments.length;a+=1){e=a<0||arguments.length<=a?void 0:arguments[a];for(var i=0;i<this.length;i+=1)if("string"==typeof e){var n=t.createElement("div");for(n.innerHTML=e;n.firstChild;)this[i].appendChild(n.firstChild)}else if(e instanceof v)for(var r=0;r<e.length;r+=1)this[i].appendChild(e[r]);else this[i].appendChild(e)}return this},prepend:function(e){var t,a,i=s();for(t=0;t<this.length;t+=1)if("string"==typeof e){var n=i.createElement("div");for(n.innerHTML=e,a=n.childNodes.length-1;a>=0;a-=1)this[t].insertBefore(n.childNodes[a],this[t].childNodes[0])}else if(e instanceof v)for(a=0;a<e.length;a+=1)this[t].insertBefore(e[a],this[t].childNodes[0]);else this[t].insertBefore(e,this[t].childNodes[0]);return this},next:function(e){return this.length>0?e?this[0].nextElementSibling&&m(this[0].nextElementSibling).is(e)?m([this[0].nextElementSibling]):m([]):this[0].nextElementSibling?m([this[0].nextElementSibling]):m([]):m([])},nextAll:function(e){var t=[],a=this[0];if(!a)return m([]);for(;a.nextElementSibling;){var i=a.nextElementSibling;e?m(i).is(e)&&t.push(i):t.push(i),a=i}return m(t)},prev:function(e){if(this.length>0){var t=this[0];return e?t.previousElementSibling&&m(t.previousElementSibling).is(e)?m([t.previousElementSibling]):m([]):t.previousElementSibling?m([t.previousElementSibling]):m([])}return m([])},prevAll:function(e){var t=[],a=this[0];if(!a)return m([]);for(;a.previousElementSibling;){var i=a.previousElementSibling;e?m(i).is(e)&&t.push(i):t.push(i),a=i}return m(t)},parent:function(e){for(var t=[],a=0;a<this.length;a+=1)null!==this[a].parentNode&&(e?m(this[a].parentNode).is(e)&&t.push(this[a].parentNode):t.push(this[a].parentNode));return m(t)},parents:function(e){for(var t=[],a=0;a<this.length;a+=1)for(var i=this[a].parentNode;i;)e?m(i).is(e)&&t.push(i):t.push(i),i=i.parentNode;return m(t)},closest:function(e){var t=this;return void 0===e?m([]):(t.is(e)||(t=t.parents(e).eq(0)),t)},find:function(e){for(var t=[],a=0;a<this.length;a+=1)for(var i=this[a].querySelectorAll(e),n=0;n<i.length;n+=1)t.push(i[n]);return m(t)},children:function(e){for(var t=[],a=0;a<this.length;a+=1)for(var i=this[a].children,n=0;n<i.length;n+=1)e&&!m(i[n]).is(e)||t.push(i[n]);return m(t)},filter:function(e){return m(h(this,e))},remove:function(){for(var e=0;e<this.length;e+=1)this[e].parentNode&&this[e].parentNode.removeChild(this[e]);return this}};function C(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function T(){return Date.now()}function E(e,t){void 0===t&&(t="x");var a,i,n,s=o(),r=function(e){var t,a=o();return a.getComputedStyle&&(t=a.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return s.WebKitCSSMatrix?((i=r.transform||r.webkitTransform).split(",").length>6&&(i=i.split(", ").map((function(e){return e.replace(",",".")})).join(", ")),n=new s.WebKitCSSMatrix("none"===i?"":i)):a=(n=r.MozTransform||r.OTransform||r.MsTransform||r.msTransform||r.transform||r.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(i=s.WebKitCSSMatrix?n.m41:16===a.length?parseFloat(a[12]):parseFloat(a[4])),"y"===t&&(i=s.WebKitCSSMatrix?n.m42:16===a.length?parseFloat(a[13]):parseFloat(a[5])),i||0}function x(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function S(){for(var e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"],a=1;a<arguments.length;a+=1){var i=a<0||arguments.length<=a?void 0:arguments[a];if(null!=i)for(var n=Object.keys(Object(i)).filter((function(e){return t.indexOf(e)<0})),s=0,r=n.length;s<r;s+=1){var o=n[s],l=Object.getOwnPropertyDescriptor(i,o);void 0!==l&&l.enumerable&&(x(e[o])&&x(i[o])?i[o].__swiper__?e[o]=i[o]:S(e[o],i[o]):!x(e[o])&&x(i[o])?(e[o]={},i[o].__swiper__?e[o]=i[o]:S(e[o],i[o])):e[o]=i[o])}}return e}function M(e,t){Object.keys(t).forEach((function(a){x(t[a])&&Object.keys(t[a]).forEach((function(i){"function"==typeof t[a][i]&&(t[a][i]=t[a][i].bind(e))})),e[a]=t[a]}))}function k(e){return void 0===e&&(e=""),"."+e.trim().replace(/([\.:\/])/g,"\\$1").replace(/ /g,".")}function P(e,t,a,i){var n=s();return a&&Object.keys(i).forEach((function(a){if(!t[a]&&!0===t.auto){var s=n.createElement("div");s.className=i[a],e.append(s),t[a]=s}})),t}function L(){return g||(g=function(){var e=o(),t=s();return{touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch),pointerEvents:!!e.PointerEvent&&"maxTouchPoints"in e.navigator&&e.navigator.maxTouchPoints>=0,observer:"MutationObserver"in e||"WebkitMutationObserver"in e,passiveListener:function(){var t=!1;try{var a=Object.defineProperty({},"passive",{get:function(){t=!0}});e.addEventListener("testPassiveListener",null,a)}catch(e){}return t}(),gestures:"ongesturestart"in e}}()),g}function z(e){return void 0===e&&(e={}),y||(y=function(e){var t=(void 0===e?{}:e).userAgent,a=L(),i=o(),n=i.navigator.platform,s=t||i.navigator.userAgent,r={ios:!1,android:!1},l=i.screen.width,d=i.screen.height,p=s.match(/(Android);?[\s\/]+([\d.]+)?/),u=s.match(/(iPad).*OS\s([\d_]+)/),c=s.match(/(iPod)(.*OS\s([\d_]+))?/),v=!u&&s.match(/(iPhone\sOS|iOS)\s([\d_]+)/),f="Win32"===n,h="MacIntel"===n;return!u&&h&&a.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(l+"x"+d)>=0&&((u=s.match(/(Version)\/([\d.]+)/))||(u=[0,1,"13_0_0"]),h=!1),p&&!f&&(r.os="android",r.android=!0),(u||v||c)&&(r.os="ios",r.ios=!0),r}(e)),y}function O(){return b||(b=function(){var e,t=o();return{isEdge:!!t.navigator.userAgent.match(/Edge/g),isSafari:(e=t.navigator.userAgent.toLowerCase(),e.indexOf("safari")>=0&&e.indexOf("chrome")<0&&e.indexOf("android")<0),isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent)}}()),b}Object.keys(w).forEach((function(e){Object.defineProperty(m.fn,e,{value:w[e],writable:!0})}));var I={name:"resize",create:function(){var e=this;S(e,{resize:{observer:null,createObserver:function(){e&&!e.destroyed&&e.initialized&&(e.resize.observer=new ResizeObserver((function(t){var a=e.width,i=e.height,n=a,s=i;t.forEach((function(t){var a=t.contentBoxSize,i=t.contentRect,r=t.target;r&&r!==e.el||(n=i?i.width:(a[0]||a).inlineSize,s=i?i.height:(a[0]||a).blockSize)})),n===a&&s===i||e.resize.resizeHandler()})),e.resize.observer.observe(e.el))},removeObserver:function(){e.resize.observer&&e.resize.observer.unobserve&&e.el&&(e.resize.observer.unobserve(e.el),e.resize.observer=null)},resizeHandler:function(){e&&!e.destroyed&&e.initialized&&(e.emit("beforeResize"),e.emit("resize"))},orientationChangeHandler:function(){e&&!e.destroyed&&e.initialized&&e.emit("orientationchange")}}})},on:{init:function(e){var t=o();e.params.resizeObserver&&void 0!==o().ResizeObserver?e.resize.createObserver():(t.addEventListener("resize",e.resize.resizeHandler),t.addEventListener("orientationchange",e.resize.orientationChangeHandler))},destroy:function(e){var t=o();e.resize.removeObserver(),t.removeEventListener("resize",e.resize.resizeHandler),t.removeEventListener("orientationchange",e.resize.orientationChangeHandler)}}},A={attach:function(e,t){void 0===t&&(t={});var a=o(),i=this,n=new(a.MutationObserver||a.WebkitMutationObserver)((function(e){if(1!==e.length){var t=function(){i.emit("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(t):a.setTimeout(t,0)}else i.emit("observerUpdate",e[0])}));n.observe(e,{attributes:void 0===t.attributes||t.attributes,childList:void 0===t.childList||t.childList,characterData:void 0===t.characterData||t.characterData}),i.observer.observers.push(n)},init:function(){var e=this;if(e.support.observer&&e.params.observer){if(e.params.observeParents)for(var t=e.$el.parents(),a=0;a<t.length;a+=1)e.observer.attach(t[a]);e.observer.attach(e.$el[0],{childList:e.params.observeSlideChildren}),e.observer.attach(e.$wrapperEl[0],{attributes:!1})}},destroy:function(){this.observer.observers.forEach((function(e){e.disconnect()})),this.observer.observers=[]}},N={name:"observer",params:{observer:!1,observeParents:!1,observeSlideChildren:!1},create:function(){M(this,{observer:t({},A,{observers:[]})})},on:{init:function(e){e.observer.init()},destroy:function(e){e.observer.destroy()}}};function $(e){var t=this,a=s(),i=o(),n=t.touchEventsData,r=t.params,l=t.touches;if(t.enabled&&(!t.animating||!r.preventInteractionOnTransition)){var d=e;d.originalEvent&&(d=d.originalEvent);var p=m(d.target);if("wrapper"!==r.touchEventsTarget||p.closest(t.wrapperEl).length)if(n.isTouchEvent="touchstart"===d.type,n.isTouchEvent||!("which"in d)||3!==d.which)if(!(!n.isTouchEvent&&"button"in d&&d.button>0))if(!n.isTouched||!n.isMoved)if(!!r.noSwipingClass&&""!==r.noSwipingClass&&d.target&&d.target.shadowRoot&&e.path&&e.path[0]&&(p=m(e.path[0])),r.noSwiping&&p.closest(r.noSwipingSelector?r.noSwipingSelector:"."+r.noSwipingClass)[0])t.allowClick=!0;else if(!r.swipeHandler||p.closest(r.swipeHandler)[0]){l.currentX="touchstart"===d.type?d.targetTouches[0].pageX:d.pageX,l.currentY="touchstart"===d.type?d.targetTouches[0].pageY:d.pageY;var u=l.currentX,c=l.currentY,v=r.edgeSwipeDetection||r.iOSEdgeSwipeDetection,f=r.edgeSwipeThreshold||r.iOSEdgeSwipeThreshold;if(v&&(u<=f||u>=i.innerWidth-f)){if("prevent"!==v)return;e.preventDefault()}if(S(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),l.startX=u,l.startY=c,n.touchStartTime=T(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,r.threshold>0&&(n.allowThresholdMove=!1),"touchstart"!==d.type){var h=!0;p.is(n.formElements)&&(h=!1),a.activeElement&&m(a.activeElement).is(n.formElements)&&a.activeElement!==p[0]&&a.activeElement.blur();var g=h&&t.allowTouchMove&&r.touchStartPreventDefault;!r.touchStartForcePreventDefault&&!g||p[0].isContentEditable||d.preventDefault()}t.emit("touchStart",d)}}}function D(e){var t=s(),a=this,i=a.touchEventsData,n=a.params,r=a.touches,o=a.rtlTranslate;if(a.enabled){var l=e;if(l.originalEvent&&(l=l.originalEvent),i.isTouched){if(!i.isTouchEvent||"touchmove"===l.type){var d="touchmove"===l.type&&l.targetTouches&&(l.targetTouches[0]||l.changedTouches[0]),p="touchmove"===l.type?d.pageX:l.pageX,u="touchmove"===l.type?d.pageY:l.pageY;if(l.preventedByNestedSwiper)return r.startX=p,void(r.startY=u);if(!a.allowTouchMove)return a.allowClick=!1,void(i.isTouched&&(S(r,{startX:p,startY:u,currentX:p,currentY:u}),i.touchStartTime=T()));if(i.isTouchEvent&&n.touchReleaseOnEdges&&!n.loop)if(a.isVertical()){if(u<r.startY&&a.translate<=a.maxTranslate()||u>r.startY&&a.translate>=a.minTranslate())return i.isTouched=!1,void(i.isMoved=!1)}else if(p<r.startX&&a.translate<=a.maxTranslate()||p>r.startX&&a.translate>=a.minTranslate())return;if(i.isTouchEvent&&t.activeElement&&l.target===t.activeElement&&m(l.target).is(i.formElements))return i.isMoved=!0,void(a.allowClick=!1);if(i.allowTouchCallbacks&&a.emit("touchMove",l),!(l.targetTouches&&l.targetTouches.length>1)){r.currentX=p,r.currentY=u;var c=r.currentX-r.startX,v=r.currentY-r.startY;if(!(a.params.threshold&&Math.sqrt(Math.pow(c,2)+Math.pow(v,2))<a.params.threshold)){var f;if(void 0===i.isScrolling)a.isHorizontal()&&r.currentY===r.startY||a.isVertical()&&r.currentX===r.startX?i.isScrolling=!1:c*c+v*v>=25&&(f=180*Math.atan2(Math.abs(v),Math.abs(c))/Math.PI,i.isScrolling=a.isHorizontal()?f>n.touchAngle:90-f>n.touchAngle);if(i.isScrolling&&a.emit("touchMoveOpposite",l),void 0===i.startMoving&&(r.currentX===r.startX&&r.currentY===r.startY||(i.startMoving=!0)),i.isScrolling)i.isTouched=!1;else if(i.startMoving){a.allowClick=!1,!n.cssMode&&l.cancelable&&l.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&l.stopPropagation(),i.isMoved||(n.loop&&a.loopFix(),i.startTranslate=a.getTranslate(),a.setTransition(0),a.animating&&a.$wrapperEl.trigger("webkitTransitionEnd transitionend"),i.allowMomentumBounce=!1,!n.grabCursor||!0!==a.allowSlideNext&&!0!==a.allowSlidePrev||a.setGrabCursor(!0),a.emit("sliderFirstMove",l)),a.emit("sliderMove",l),i.isMoved=!0;var h=a.isHorizontal()?c:v;r.diff=h,h*=n.touchRatio,o&&(h=-h),a.swipeDirection=h>0?"prev":"next",i.currentTranslate=h+i.startTranslate;var g=!0,y=n.resistanceRatio;if(n.touchReleaseOnEdges&&(y=0),h>0&&i.currentTranslate>a.minTranslate()?(g=!1,n.resistance&&(i.currentTranslate=a.minTranslate()-1+Math.pow(-a.minTranslate()+i.startTranslate+h,y))):h<0&&i.currentTranslate<a.maxTranslate()&&(g=!1,n.resistance&&(i.currentTranslate=a.maxTranslate()+1-Math.pow(a.maxTranslate()-i.startTranslate-h,y))),g&&(l.preventedByNestedSwiper=!0),!a.allowSlideNext&&"next"===a.swipeDirection&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!a.allowSlidePrev&&"prev"===a.swipeDirection&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),a.allowSlidePrev||a.allowSlideNext||(i.currentTranslate=i.startTranslate),n.threshold>0){if(!(Math.abs(h)>n.threshold||i.allowThresholdMove))return void(i.currentTranslate=i.startTranslate);if(!i.allowThresholdMove)return i.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,i.currentTranslate=i.startTranslate,void(r.diff=a.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY)}n.followFinger&&!n.cssMode&&((n.freeMode||n.watchSlidesProgress||n.watchSlidesVisibility)&&(a.updateActiveIndex(),a.updateSlidesClasses()),n.freeMode&&(0===i.velocities.length&&i.velocities.push({position:r[a.isHorizontal()?"startX":"startY"],time:i.touchStartTime}),i.velocities.push({position:r[a.isHorizontal()?"currentX":"currentY"],time:T()})),a.updateProgress(i.currentTranslate),a.setTranslate(i.currentTranslate))}}}}}else i.startMoving&&i.isScrolling&&a.emit("touchMoveOpposite",l)}}function B(e){var t=this,a=t.touchEventsData,i=t.params,n=t.touches,s=t.rtlTranslate,r=t.$wrapperEl,o=t.slidesGrid,l=t.snapGrid;if(t.enabled){var d=e;if(d.originalEvent&&(d=d.originalEvent),a.allowTouchCallbacks&&t.emit("touchEnd",d),a.allowTouchCallbacks=!1,!a.isTouched)return a.isMoved&&i.grabCursor&&t.setGrabCursor(!1),a.isMoved=!1,void(a.startMoving=!1);i.grabCursor&&a.isMoved&&a.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);var p,u=T(),c=u-a.touchStartTime;if(t.allowClick&&(t.updateClickedSlide(d),t.emit("tap click",d),c<300&&u-a.lastClickTime<300&&t.emit("doubleTap doubleClick",d)),a.lastClickTime=T(),C((function(){t.destroyed||(t.allowClick=!0)})),!a.isTouched||!a.isMoved||!t.swipeDirection||0===n.diff||a.currentTranslate===a.startTranslate)return a.isTouched=!1,a.isMoved=!1,void(a.startMoving=!1);if(a.isTouched=!1,a.isMoved=!1,a.startMoving=!1,p=i.followFinger?s?t.translate:-t.translate:-a.currentTranslate,!i.cssMode)if(i.freeMode){if(p<-t.minTranslate())return void t.slideTo(t.activeIndex);if(p>-t.maxTranslate())return void(t.slides.length<l.length?t.slideTo(l.length-1):t.slideTo(t.slides.length-1));if(i.freeModeMomentum){if(a.velocities.length>1){var v=a.velocities.pop(),f=a.velocities.pop(),h=v.position-f.position,m=v.time-f.time;t.velocity=h/m,t.velocity/=2,Math.abs(t.velocity)<i.freeModeMinimumVelocity&&(t.velocity=0),(m>150||T()-v.time>300)&&(t.velocity=0)}else t.velocity=0;t.velocity*=i.freeModeMomentumVelocityRatio,a.velocities.length=0;var g=1e3*i.freeModeMomentumRatio,y=t.velocity*g,b=t.translate+y;s&&(b=-b);var w,E,x=!1,S=20*Math.abs(t.velocity)*i.freeModeMomentumBounceRatio;if(b<t.maxTranslate())i.freeModeMomentumBounce?(b+t.maxTranslate()<-S&&(b=t.maxTranslate()-S),w=t.maxTranslate(),x=!0,a.allowMomentumBounce=!0):b=t.maxTranslate(),i.loop&&i.centeredSlides&&(E=!0);else if(b>t.minTranslate())i.freeModeMomentumBounce?(b-t.minTranslate()>S&&(b=t.minTranslate()+S),w=t.minTranslate(),x=!0,a.allowMomentumBounce=!0):b=t.minTranslate(),i.loop&&i.centeredSlides&&(E=!0);else if(i.freeModeSticky){for(var M,k=0;k<l.length;k+=1)if(l[k]>-b){M=k;break}b=-(b=Math.abs(l[M]-b)<Math.abs(l[M-1]-b)||"next"===t.swipeDirection?l[M]:l[M-1])}if(E&&t.once("transitionEnd",(function(){t.loopFix()})),0!==t.velocity){if(g=s?Math.abs((-b-t.translate)/t.velocity):Math.abs((b-t.translate)/t.velocity),i.freeModeSticky){var P=Math.abs((s?-b:b)-t.translate),L=t.slidesSizesGrid[t.activeIndex];g=P<L?i.speed:P<2*L?1.5*i.speed:2.5*i.speed}}else if(i.freeModeSticky)return void t.slideToClosest();i.freeModeMomentumBounce&&x?(t.updateProgress(w),t.setTransition(g),t.setTranslate(b),t.transitionStart(!0,t.swipeDirection),t.animating=!0,r.transitionEnd((function(){t&&!t.destroyed&&a.allowMomentumBounce&&(t.emit("momentumBounce"),t.setTransition(i.speed),setTimeout((function(){t.setTranslate(w),r.transitionEnd((function(){t&&!t.destroyed&&t.transitionEnd()}))}),0))}))):t.velocity?(t.updateProgress(b),t.setTransition(g),t.setTranslate(b),t.transitionStart(!0,t.swipeDirection),t.animating||(t.animating=!0,r.transitionEnd((function(){t&&!t.destroyed&&t.transitionEnd()})))):(t.emit("_freeModeNoMomentumRelease"),t.updateProgress(b)),t.updateActiveIndex(),t.updateSlidesClasses()}else{if(i.freeModeSticky)return void t.slideToClosest();i.freeMode&&t.emit("_freeModeNoMomentumRelease")}(!i.freeModeMomentum||c>=i.longSwipesMs)&&(t.updateProgress(),t.updateActiveIndex(),t.updateSlidesClasses())}else{for(var z=0,O=t.slidesSizesGrid[0],I=0;I<o.length;I+=I<i.slidesPerGroupSkip?1:i.slidesPerGroup){var A=I<i.slidesPerGroupSkip-1?1:i.slidesPerGroup;void 0!==o[I+A]?p>=o[I]&&p<o[I+A]&&(z=I,O=o[I+A]-o[I]):p>=o[I]&&(z=I,O=o[o.length-1]-o[o.length-2])}var N=(p-o[z])/O,$=z<i.slidesPerGroupSkip-1?1:i.slidesPerGroup;if(c>i.longSwipesMs){if(!i.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(N>=i.longSwipesRatio?t.slideTo(z+$):t.slideTo(z)),"prev"===t.swipeDirection&&(N>1-i.longSwipesRatio?t.slideTo(z+$):t.slideTo(z))}else{if(!i.shortSwipes)return void t.slideTo(t.activeIndex);t.navigation&&(d.target===t.navigation.nextEl||d.target===t.navigation.prevEl)?d.target===t.navigation.nextEl?t.slideTo(z+$):t.slideTo(z):("next"===t.swipeDirection&&t.slideTo(z+$),"prev"===t.swipeDirection&&t.slideTo(z))}}}}function G(){var e=this,t=e.params,a=e.el;if(!a||0!==a.offsetWidth){t.breakpoints&&e.setBreakpoint();var i=e.allowSlideNext,n=e.allowSlidePrev,s=e.snapGrid;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses(),("auto"===t.slidesPerView||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.run(),e.allowSlidePrev=n,e.allowSlideNext=i,e.params.watchOverflow&&s!==e.snapGrid&&e.checkOverflow()}}function H(e){var t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function V(){var e=this,t=e.wrapperEl,a=e.rtlTranslate;if(e.enabled){e.previousTranslate=e.translate,e.isHorizontal()?e.translate=a?t.scrollWidth-t.offsetWidth-t.scrollLeft:-t.scrollLeft:e.translate=-t.scrollTop,-0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();var i=e.maxTranslate()-e.minTranslate();(0===i?0:(e.translate-e.minTranslate())/i)!==e.progress&&e.updateProgress(a?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}}var R=!1;function F(){}var _={init:!0,direction:"horizontal",touchEventsTarget:"container",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!1,nested:!1,createElements:!1,enabled:!0,width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,freeMode:!1,freeModeMomentum:!0,freeModeMomentumRatio:1,freeModeMomentumBounce:!0,freeModeMomentumBounceRatio:1,freeModeMomentumVelocityRatio:1,freeModeSticky:!1,freeModeMinimumVelocity:.02,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerColumn:1,slidesPerColumnFill:"column",slidesPerGroup:1,slidesPerGroupSkip:0,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!1,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,watchSlidesVisibility:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopFillGroupWithBlank:!1,loopPreventsSlide:!0,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,containerModifierClass:"swiper-container-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0,_emitClasses:!1},j={modular:{useParams:function(e){var t=this;t.modules&&Object.keys(t.modules).forEach((function(a){var i=t.modules[a];i.params&&S(e,i.params)}))},useModules:function(e){void 0===e&&(e={});var t=this;t.modules&&Object.keys(t.modules).forEach((function(a){var i=t.modules[a],n=e[a]||{};i.on&&t.on&&Object.keys(i.on).forEach((function(e){t.on(e,i.on[e])})),i.create&&i.create.bind(t)(n)}))}},eventsEmitter:{on:function(e,t,a){var i=this;if("function"!=typeof t)return i;var n=a?"unshift":"push";return e.split(" ").forEach((function(e){i.eventsListeners[e]||(i.eventsListeners[e]=[]),i.eventsListeners[e][n](t)})),i},once:function(e,t,a){var i=this;if("function"!=typeof t)return i;function n(){i.off(e,n),n.__emitterProxy&&delete n.__emitterProxy;for(var a=arguments.length,s=new Array(a),r=0;r<a;r++)s[r]=arguments[r];t.apply(i,s)}return n.__emitterProxy=t,i.on(e,n,a)},onAny:function(e,t){var a=this;if("function"!=typeof e)return a;var i=t?"unshift":"push";return a.eventsAnyListeners.indexOf(e)<0&&a.eventsAnyListeners[i](e),a},offAny:function(e){var t=this;if(!t.eventsAnyListeners)return t;var a=t.eventsAnyListeners.indexOf(e);return a>=0&&t.eventsAnyListeners.splice(a,1),t},off:function(e,t){var a=this;return a.eventsListeners?(e.split(" ").forEach((function(e){void 0===t?a.eventsListeners[e]=[]:a.eventsListeners[e]&&a.eventsListeners[e].forEach((function(i,n){(i===t||i.__emitterProxy&&i.__emitterProxy===t)&&a.eventsListeners[e].splice(n,1)}))})),a):a},emit:function(){var e,t,a,i=this;if(!i.eventsListeners)return i;for(var n=arguments.length,s=new Array(n),r=0;r<n;r++)s[r]=arguments[r];"string"==typeof s[0]||Array.isArray(s[0])?(e=s[0],t=s.slice(1,s.length),a=i):(e=s[0].events,t=s[0].data,a=s[0].context||i),t.unshift(a);var o=Array.isArray(e)?e:e.split(" ");return o.forEach((function(e){i.eventsAnyListeners&&i.eventsAnyListeners.length&&i.eventsAnyListeners.forEach((function(i){i.apply(a,[e].concat(t))})),i.eventsListeners&&i.eventsListeners[e]&&i.eventsListeners[e].forEach((function(e){e.apply(a,t)}))})),i}},update:{updateSize:function(){var e,t,a=this,i=a.$el;e=void 0!==a.params.width&&null!==a.params.width?a.params.width:i[0].clientWidth,t=void 0!==a.params.height&&null!==a.params.height?a.params.height:i[0].clientHeight,0===e&&a.isHorizontal()||0===t&&a.isVertical()||(e=e-parseInt(i.css("padding-left")||0,10)-parseInt(i.css("padding-right")||0,10),t=t-parseInt(i.css("padding-top")||0,10)-parseInt(i.css("padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),S(a,{width:e,height:t,size:a.isHorizontal()?e:t}))},updateSlides:function(){var e=this;function t(t){return e.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}function a(e,a){return parseFloat(e.getPropertyValue(t(a))||0)}var i=e.params,n=e.$wrapperEl,s=e.size,r=e.rtlTranslate,o=e.wrongRTL,l=e.virtual&&i.virtual.enabled,d=l?e.virtual.slides.length:e.slides.length,p=n.children("."+e.params.slideClass),u=l?e.virtual.slides.length:p.length,c=[],v=[],f=[],h=i.slidesOffsetBefore;"function"==typeof h&&(h=i.slidesOffsetBefore.call(e));var m=i.slidesOffsetAfter;"function"==typeof m&&(m=i.slidesOffsetAfter.call(e));var g=e.snapGrid.length,y=e.slidesGrid.length,b=i.spaceBetween,w=-h,C=0,T=0;if(void 0!==s){var E,x;"string"==typeof b&&b.indexOf("%")>=0&&(b=parseFloat(b.replace("%",""))/100*s),e.virtualSize=-b,r?p.css({marginLeft:"",marginTop:""}):p.css({marginRight:"",marginBottom:""}),i.slidesPerColumn>1&&(E=Math.floor(u/i.slidesPerColumn)===u/e.params.slidesPerColumn?u:Math.ceil(u/i.slidesPerColumn)*i.slidesPerColumn,"auto"!==i.slidesPerView&&"row"===i.slidesPerColumnFill&&(E=Math.max(E,i.slidesPerView*i.slidesPerColumn)));for(var M,k,P,L=i.slidesPerColumn,z=E/L,O=Math.floor(u/i.slidesPerColumn),I=0;I<u;I+=1){x=0;var A=p.eq(I);if(i.slidesPerColumn>1){var N=void 0,$=void 0,D=void 0;if("row"===i.slidesPerColumnFill&&i.slidesPerGroup>1){var B=Math.floor(I/(i.slidesPerGroup*i.slidesPerColumn)),G=I-i.slidesPerColumn*i.slidesPerGroup*B,H=0===B?i.slidesPerGroup:Math.min(Math.ceil((u-B*L*i.slidesPerGroup)/L),i.slidesPerGroup);N=($=G-(D=Math.floor(G/H))*H+B*i.slidesPerGroup)+D*E/L,A.css({"-webkit-box-ordinal-group":N,"-moz-box-ordinal-group":N,"-ms-flex-order":N,"-webkit-order":N,order:N})}else"column"===i.slidesPerColumnFill?(D=I-($=Math.floor(I/L))*L,($>O||$===O&&D===L-1)&&(D+=1)>=L&&(D=0,$+=1)):$=I-(D=Math.floor(I/z))*z;A.css(t("margin-top"),0!==D&&i.spaceBetween&&i.spaceBetween+"px")}if("none"!==A.css("display")){if("auto"===i.slidesPerView){var V=getComputedStyle(A[0]),R=A[0].style.transform,F=A[0].style.webkitTransform;if(R&&(A[0].style.transform="none"),F&&(A[0].style.webkitTransform="none"),i.roundLengths)x=e.isHorizontal()?A.outerWidth(!0):A.outerHeight(!0);else{var _=a(V,"width"),j=a(V,"padding-left"),q=a(V,"padding-right"),W=a(V,"margin-left"),X=a(V,"margin-right"),Y=V.getPropertyValue("box-sizing");if(Y&&"border-box"===Y)x=_+W+X;else{var U=A[0],K=U.clientWidth;x=_+j+q+W+X+(U.offsetWidth-K)}}R&&(A[0].style.transform=R),F&&(A[0].style.webkitTransform=F),i.roundLengths&&(x=Math.floor(x))}else x=(s-(i.slidesPerView-1)*b)/i.slidesPerView,i.roundLengths&&(x=Math.floor(x)),p[I]&&(p[I].style[t("width")]=x+"px");p[I]&&(p[I].swiperSlideSize=x),f.push(x),i.centeredSlides?(w=w+x/2+C/2+b,0===C&&0!==I&&(w=w-s/2-b),0===I&&(w=w-s/2-b),Math.abs(w)<.001&&(w=0),i.roundLengths&&(w=Math.floor(w)),T%i.slidesPerGroup==0&&c.push(w),v.push(w)):(i.roundLengths&&(w=Math.floor(w)),(T-Math.min(e.params.slidesPerGroupSkip,T))%e.params.slidesPerGroup==0&&c.push(w),v.push(w),w=w+x+b),e.virtualSize+=x+b,C=x,T+=1}}if(e.virtualSize=Math.max(e.virtualSize,s)+m,r&&o&&("slide"===i.effect||"coverflow"===i.effect)&&n.css({width:e.virtualSize+i.spaceBetween+"px"}),i.setWrapperSize)n.css(((k={})[t("width")]=e.virtualSize+i.spaceBetween+"px",k));if(i.slidesPerColumn>1)if(e.virtualSize=(x+i.spaceBetween)*E,e.virtualSize=Math.ceil(e.virtualSize/i.slidesPerColumn)-i.spaceBetween,n.css(((P={})[t("width")]=e.virtualSize+i.spaceBetween+"px",P)),i.centeredSlides){M=[];for(var J=0;J<c.length;J+=1){var Q=c[J];i.roundLengths&&(Q=Math.floor(Q)),c[J]<e.virtualSize+c[0]&&M.push(Q)}c=M}if(!i.centeredSlides){M=[];for(var Z=0;Z<c.length;Z+=1){var ee=c[Z];i.roundLengths&&(ee=Math.floor(ee)),c[Z]<=e.virtualSize-s&&M.push(ee)}c=M,Math.floor(e.virtualSize-s)-Math.floor(c[c.length-1])>1&&c.push(e.virtualSize-s)}if(0===c.length&&(c=[0]),0!==i.spaceBetween){var te,ae=e.isHorizontal()&&r?"marginLeft":t("marginRight");p.filter((function(e,t){return!i.cssMode||t!==p.length-1})).css(((te={})[ae]=b+"px",te))}if(i.centeredSlides&&i.centeredSlidesBounds){var ie=0;f.forEach((function(e){ie+=e+(i.spaceBetween?i.spaceBetween:0)}));var ne=(ie-=i.spaceBetween)-s;c=c.map((function(e){return e<0?-h:e>ne?ne+m:e}))}if(i.centerInsufficientSlides){var se=0;if(f.forEach((function(e){se+=e+(i.spaceBetween?i.spaceBetween:0)})),(se-=i.spaceBetween)<s){var re=(s-se)/2;c.forEach((function(e,t){c[t]=e-re})),v.forEach((function(e,t){v[t]=e+re}))}}S(e,{slides:p,snapGrid:c,slidesGrid:v,slidesSizesGrid:f}),u!==d&&e.emit("slidesLengthChange"),c.length!==g&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),v.length!==y&&e.emit("slidesGridLengthChange"),(i.watchSlidesProgress||i.watchSlidesVisibility)&&e.updateSlidesOffset()}},updateAutoHeight:function(e){var t,a=this,i=[],n=a.virtual&&a.params.virtual.enabled,s=0;"number"==typeof e?a.setTransition(e):!0===e&&a.setTransition(a.params.speed);var r=function(e){return n?a.slides.filter((function(t){return parseInt(t.getAttribute("data-swiper-slide-index"),10)===e}))[0]:a.slides.eq(e)[0]};if("auto"!==a.params.slidesPerView&&a.params.slidesPerView>1)if(a.params.centeredSlides)a.visibleSlides.each((function(e){i.push(e)}));else for(t=0;t<Math.ceil(a.params.slidesPerView);t+=1){var o=a.activeIndex+t;if(o>a.slides.length&&!n)break;i.push(r(o))}else i.push(r(a.activeIndex));for(t=0;t<i.length;t+=1)if(void 0!==i[t]){var l=i[t].offsetHeight;s=l>s?l:s}s&&a.$wrapperEl.css("height",s+"px")},updateSlidesOffset:function(){for(var e=this.slides,t=0;t<e.length;t+=1)e[t].swiperSlideOffset=this.isHorizontal()?e[t].offsetLeft:e[t].offsetTop},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);var t=this,a=t.params,i=t.slides,n=t.rtlTranslate;if(0!==i.length){void 0===i[0].swiperSlideOffset&&t.updateSlidesOffset();var s=-e;n&&(s=e),i.removeClass(a.slideVisibleClass),t.visibleSlidesIndexes=[],t.visibleSlides=[];for(var r=0;r<i.length;r+=1){var o=i[r],l=(s+(a.centeredSlides?t.minTranslate():0)-o.swiperSlideOffset)/(o.swiperSlideSize+a.spaceBetween);if(a.watchSlidesVisibility||a.centeredSlides&&a.autoHeight){var d=-(s-o.swiperSlideOffset),p=d+t.slidesSizesGrid[r];(d>=0&&d<t.size-1||p>1&&p<=t.size||d<=0&&p>=t.size)&&(t.visibleSlides.push(o),t.visibleSlidesIndexes.push(r),i.eq(r).addClass(a.slideVisibleClass))}o.progress=n?-l:l}t.visibleSlides=m(t.visibleSlides)}},updateProgress:function(e){var t=this;if(void 0===e){var a=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*a||0}var i=t.params,n=t.maxTranslate()-t.minTranslate(),s=t.progress,r=t.isBeginning,o=t.isEnd,l=r,d=o;0===n?(s=0,r=!0,o=!0):(r=(s=(e-t.minTranslate())/n)<=0,o=s>=1),S(t,{progress:s,isBeginning:r,isEnd:o}),(i.watchSlidesProgress||i.watchSlidesVisibility||i.centeredSlides&&i.autoHeight)&&t.updateSlidesProgress(e),r&&!l&&t.emit("reachBeginning toEdge"),o&&!d&&t.emit("reachEnd toEdge"),(l&&!r||d&&!o)&&t.emit("fromEdge"),t.emit("progress",s)},updateSlidesClasses:function(){var e,t=this,a=t.slides,i=t.params,n=t.$wrapperEl,s=t.activeIndex,r=t.realIndex,o=t.virtual&&i.virtual.enabled;a.removeClass(i.slideActiveClass+" "+i.slideNextClass+" "+i.slidePrevClass+" "+i.slideDuplicateActiveClass+" "+i.slideDuplicateNextClass+" "+i.slideDuplicatePrevClass),(e=o?t.$wrapperEl.find("."+i.slideClass+'[data-swiper-slide-index="'+s+'"]'):a.eq(s)).addClass(i.slideActiveClass),i.loop&&(e.hasClass(i.slideDuplicateClass)?n.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+r+'"]').addClass(i.slideDuplicateActiveClass):n.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+r+'"]').addClass(i.slideDuplicateActiveClass));var l=e.nextAll("."+i.slideClass).eq(0).addClass(i.slideNextClass);i.loop&&0===l.length&&(l=a.eq(0)).addClass(i.slideNextClass);var d=e.prevAll("."+i.slideClass).eq(0).addClass(i.slidePrevClass);i.loop&&0===d.length&&(d=a.eq(-1)).addClass(i.slidePrevClass),i.loop&&(l.hasClass(i.slideDuplicateClass)?n.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+l.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicateNextClass):n.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+l.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicateNextClass),d.hasClass(i.slideDuplicateClass)?n.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+d.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicatePrevClass):n.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+d.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicatePrevClass)),t.emitSlidesClasses()},updateActiveIndex:function(e){var t,a=this,i=a.rtlTranslate?a.translate:-a.translate,n=a.slidesGrid,s=a.snapGrid,r=a.params,o=a.activeIndex,l=a.realIndex,d=a.snapIndex,p=e;if(void 0===p){for(var u=0;u<n.length;u+=1)void 0!==n[u+1]?i>=n[u]&&i<n[u+1]-(n[u+1]-n[u])/2?p=u:i>=n[u]&&i<n[u+1]&&(p=u+1):i>=n[u]&&(p=u);r.normalizeSlideIndex&&(p<0||void 0===p)&&(p=0)}if(s.indexOf(i)>=0)t=s.indexOf(i);else{var c=Math.min(r.slidesPerGroupSkip,p);t=c+Math.floor((p-c)/r.slidesPerGroup)}if(t>=s.length&&(t=s.length-1),p!==o){var v=parseInt(a.slides.eq(p).attr("data-swiper-slide-index")||p,10);S(a,{snapIndex:t,realIndex:v,previousIndex:o,activeIndex:p}),a.emit("activeIndexChange"),a.emit("snapIndexChange"),l!==v&&a.emit("realIndexChange"),(a.initialized||a.params.runCallbacksOnInit)&&a.emit("slideChange")}else t!==d&&(a.snapIndex=t,a.emit("snapIndexChange"))},updateClickedSlide:function(e){var t,a=this,i=a.params,n=m(e.target).closest("."+i.slideClass)[0],s=!1;if(n)for(var r=0;r<a.slides.length;r+=1)if(a.slides[r]===n){s=!0,t=r;break}if(!n||!s)return a.clickedSlide=void 0,void(a.clickedIndex=void 0);a.clickedSlide=n,a.virtual&&a.params.virtual.enabled?a.clickedIndex=parseInt(m(n).attr("data-swiper-slide-index"),10):a.clickedIndex=t,i.slideToClickedSlide&&void 0!==a.clickedIndex&&a.clickedIndex!==a.activeIndex&&a.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");var t=this,a=t.params,i=t.rtlTranslate,n=t.translate,s=t.$wrapperEl;if(a.virtualTranslate)return i?-n:n;if(a.cssMode)return n;var r=E(s[0],e);return i&&(r=-r),r||0},setTranslate:function(e,t){var a=this,i=a.rtlTranslate,n=a.params,s=a.$wrapperEl,r=a.wrapperEl,o=a.progress,l=0,d=0;a.isHorizontal()?l=i?-e:e:d=e,n.roundLengths&&(l=Math.floor(l),d=Math.floor(d)),n.cssMode?r[a.isHorizontal()?"scrollLeft":"scrollTop"]=a.isHorizontal()?-l:-d:n.virtualTranslate||s.transform("translate3d("+l+"px, "+d+"px, 0px)"),a.previousTranslate=a.translate,a.translate=a.isHorizontal()?l:d;var p=a.maxTranslate()-a.minTranslate();(0===p?0:(e-a.minTranslate())/p)!==o&&a.updateProgress(e),a.emit("setTranslate",a.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,a,i,n){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===a&&(a=!0),void 0===i&&(i=!0);var s=this,r=s.params,o=s.wrapperEl;if(s.animating&&r.preventInteractionOnTransition)return!1;var l,d=s.minTranslate(),p=s.maxTranslate();if(l=i&&e>d?d:i&&e<p?p:e,s.updateProgress(l),r.cssMode){var u,c=s.isHorizontal();if(0===t)o[c?"scrollLeft":"scrollTop"]=-l;else if(o.scrollTo)o.scrollTo(((u={})[c?"left":"top"]=-l,u.behavior="smooth",u));else o[c?"scrollLeft":"scrollTop"]=-l;return!0}return 0===t?(s.setTransition(0),s.setTranslate(l),a&&(s.emit("beforeTransitionStart",t,n),s.emit("transitionEnd"))):(s.setTransition(t),s.setTranslate(l),a&&(s.emit("beforeTransitionStart",t,n),s.emit("transitionStart")),s.animating||(s.animating=!0,s.onTranslateToWrapperTransitionEnd||(s.onTranslateToWrapperTransitionEnd=function(e){s&&!s.destroyed&&e.target===this&&(s.$wrapperEl[0].removeEventListener("transitionend",s.onTranslateToWrapperTransitionEnd),s.$wrapperEl[0].removeEventListener("webkitTransitionEnd",s.onTranslateToWrapperTransitionEnd),s.onTranslateToWrapperTransitionEnd=null,delete s.onTranslateToWrapperTransitionEnd,a&&s.emit("transitionEnd"))}),s.$wrapperEl[0].addEventListener("transitionend",s.onTranslateToWrapperTransitionEnd),s.$wrapperEl[0].addEventListener("webkitTransitionEnd",s.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){var a=this;a.params.cssMode||a.$wrapperEl.transition(e),a.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);var a=this,i=a.activeIndex,n=a.params,s=a.previousIndex;if(!n.cssMode){n.autoHeight&&a.updateAutoHeight();var r=t;if(r||(r=i>s?"next":i<s?"prev":"reset"),a.emit("transitionStart"),e&&i!==s){if("reset"===r)return void a.emit("slideResetTransitionStart");a.emit("slideChangeTransitionStart"),"next"===r?a.emit("slideNextTransitionStart"):a.emit("slidePrevTransitionStart")}}},transitionEnd:function(e,t){void 0===e&&(e=!0);var a=this,i=a.activeIndex,n=a.previousIndex,s=a.params;if(a.animating=!1,!s.cssMode){a.setTransition(0);var r=t;if(r||(r=i>n?"next":i<n?"prev":"reset"),a.emit("transitionEnd"),e&&i!==n){if("reset"===r)return void a.emit("slideResetTransitionEnd");a.emit("slideChangeTransitionEnd"),"next"===r?a.emit("slideNextTransitionEnd"):a.emit("slidePrevTransitionEnd")}}}},slide:{slideTo:function(e,t,a,i,n){if(void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===a&&(a=!0),"number"!=typeof e&&"string"!=typeof e)throw new Error("The 'index' argument cannot have type other than 'number' or 'string'. ["+typeof e+"] given.");if("string"==typeof e){var s=parseInt(e,10);if(!isFinite(s))throw new Error("The passed-in 'index' (string) couldn't be converted to 'number'. ["+e+"] given.");e=s}var r=this,o=e;o<0&&(o=0);var l=r.params,d=r.snapGrid,p=r.slidesGrid,u=r.previousIndex,c=r.activeIndex,v=r.rtlTranslate,f=r.wrapperEl,h=r.enabled;if(r.animating&&l.preventInteractionOnTransition||!h&&!i&&!n)return!1;var m=Math.min(r.params.slidesPerGroupSkip,o),g=m+Math.floor((o-m)/r.params.slidesPerGroup);g>=d.length&&(g=d.length-1),(c||l.initialSlide||0)===(u||0)&&a&&r.emit("beforeSlideChangeStart");var y,b=-d[g];if(r.updateProgress(b),l.normalizeSlideIndex)for(var w=0;w<p.length;w+=1){var C=-Math.floor(100*b),T=Math.floor(100*p[w]),E=Math.floor(100*p[w+1]);void 0!==p[w+1]?C>=T&&C<E-(E-T)/2?o=w:C>=T&&C<E&&(o=w+1):C>=T&&(o=w)}if(r.initialized&&o!==c){if(!r.allowSlideNext&&b<r.translate&&b<r.minTranslate())return!1;if(!r.allowSlidePrev&&b>r.translate&&b>r.maxTranslate()&&(c||0)!==o)return!1}if(y=o>c?"next":o<c?"prev":"reset",v&&-b===r.translate||!v&&b===r.translate)return r.updateActiveIndex(o),l.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==l.effect&&r.setTranslate(b),"reset"!==y&&(r.transitionStart(a,y),r.transitionEnd(a,y)),!1;if(l.cssMode){var x,S=r.isHorizontal(),M=-b;if(v&&(M=f.scrollWidth-f.offsetWidth-M),0===t)f[S?"scrollLeft":"scrollTop"]=M;else if(f.scrollTo)f.scrollTo(((x={})[S?"left":"top"]=M,x.behavior="smooth",x));else f[S?"scrollLeft":"scrollTop"]=M;return!0}return 0===t?(r.setTransition(0),r.setTranslate(b),r.updateActiveIndex(o),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,i),r.transitionStart(a,y),r.transitionEnd(a,y)):(r.setTransition(t),r.setTranslate(b),r.updateActiveIndex(o),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,i),r.transitionStart(a,y),r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.$wrapperEl[0].removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].removeEventListener("webkitTransitionEnd",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(a,y))}),r.$wrapperEl[0].addEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].addEventListener("webkitTransitionEnd",r.onSlideToWrapperTransitionEnd))),!0},slideToLoop:function(e,t,a,i){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===a&&(a=!0);var n=this,s=e;return n.params.loop&&(s+=n.loopedSlides),n.slideTo(s,t,a,i)},slideNext:function(e,t,a){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);var i=this,n=i.params,s=i.animating;if(!i.enabled)return i;var r=i.activeIndex<n.slidesPerGroupSkip?1:n.slidesPerGroup;if(n.loop){if(s&&n.loopPreventsSlide)return!1;i.loopFix(),i._clientLeft=i.$wrapperEl[0].clientLeft}return i.slideTo(i.activeIndex+r,e,t,a)},slidePrev:function(e,t,a){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);var i=this,n=i.params,s=i.animating,r=i.snapGrid,o=i.slidesGrid,l=i.rtlTranslate;if(!i.enabled)return i;if(n.loop){if(s&&n.loopPreventsSlide)return!1;i.loopFix(),i._clientLeft=i.$wrapperEl[0].clientLeft}function d(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}var p=d(l?i.translate:-i.translate),u=r.map((function(e){return d(e)}));r[u.indexOf(p)];var c,v=r[u.indexOf(p)-1];return void 0===v&&n.cssMode&&r.forEach((function(e){!v&&p>=e&&(v=e)})),void 0!==v&&(c=o.indexOf(v))<0&&(c=i.activeIndex-1),i.slideTo(c,e,t,a)},slideReset:function(e,t,a){return void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),this.slideTo(this.activeIndex,e,t,a)},slideToClosest:function(e,t,a,i){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),void 0===i&&(i=.5);var n=this,s=n.activeIndex,r=Math.min(n.params.slidesPerGroupSkip,s),o=r+Math.floor((s-r)/n.params.slidesPerGroup),l=n.rtlTranslate?n.translate:-n.translate;if(l>=n.snapGrid[o]){var d=n.snapGrid[o];l-d>(n.snapGrid[o+1]-d)*i&&(s+=n.params.slidesPerGroup)}else{var p=n.snapGrid[o-1];l-p<=(n.snapGrid[o]-p)*i&&(s-=n.params.slidesPerGroup)}return s=Math.max(s,0),s=Math.min(s,n.slidesGrid.length-1),n.slideTo(s,e,t,a)},slideToClickedSlide:function(){var e,t=this,a=t.params,i=t.$wrapperEl,n="auto"===a.slidesPerView?t.slidesPerViewDynamic():a.slidesPerView,s=t.clickedIndex;if(a.loop){if(t.animating)return;e=parseInt(m(t.clickedSlide).attr("data-swiper-slide-index"),10),a.centeredSlides?s<t.loopedSlides-n/2||s>t.slides.length-t.loopedSlides+n/2?(t.loopFix(),s=i.children("."+a.slideClass+'[data-swiper-slide-index="'+e+'"]:not(.'+a.slideDuplicateClass+")").eq(0).index(),C((function(){t.slideTo(s)}))):t.slideTo(s):s>t.slides.length-n?(t.loopFix(),s=i.children("."+a.slideClass+'[data-swiper-slide-index="'+e+'"]:not(.'+a.slideDuplicateClass+")").eq(0).index(),C((function(){t.slideTo(s)}))):t.slideTo(s)}else t.slideTo(s)}},loop:{loopCreate:function(){var e=this,t=s(),a=e.params,i=e.$wrapperEl;i.children("."+a.slideClass+"."+a.slideDuplicateClass).remove();var n=i.children("."+a.slideClass);if(a.loopFillGroupWithBlank){var r=a.slidesPerGroup-n.length%a.slidesPerGroup;if(r!==a.slidesPerGroup){for(var o=0;o<r;o+=1){var l=m(t.createElement("div")).addClass(a.slideClass+" "+a.slideBlankClass);i.append(l)}n=i.children("."+a.slideClass)}}"auto"!==a.slidesPerView||a.loopedSlides||(a.loopedSlides=n.length),e.loopedSlides=Math.ceil(parseFloat(a.loopedSlides||a.slidesPerView,10)),e.loopedSlides+=a.loopAdditionalSlides,e.loopedSlides>n.length&&(e.loopedSlides=n.length);var d=[],p=[];n.each((function(t,a){var i=m(t);a<e.loopedSlides&&p.push(t),a<n.length&&a>=n.length-e.loopedSlides&&d.push(t),i.attr("data-swiper-slide-index",a)}));for(var u=0;u<p.length;u+=1)i.append(m(p[u].cloneNode(!0)).addClass(a.slideDuplicateClass));for(var c=d.length-1;c>=0;c-=1)i.prepend(m(d[c].cloneNode(!0)).addClass(a.slideDuplicateClass))},loopFix:function(){var e=this;e.emit("beforeLoopFix");var t,a=e.activeIndex,i=e.slides,n=e.loopedSlides,s=e.allowSlidePrev,r=e.allowSlideNext,o=e.snapGrid,l=e.rtlTranslate;e.allowSlidePrev=!0,e.allowSlideNext=!0;var d=-o[a]-e.getTranslate();if(a<n)t=i.length-3*n+a,t+=n,e.slideTo(t,0,!1,!0)&&0!==d&&e.setTranslate((l?-e.translate:e.translate)-d);else if(a>=i.length-n){t=-i.length+a+n,t+=n,e.slideTo(t,0,!1,!0)&&0!==d&&e.setTranslate((l?-e.translate:e.translate)-d)}e.allowSlidePrev=s,e.allowSlideNext=r,e.emit("loopFix")},loopDestroy:function(){var e=this,t=e.$wrapperEl,a=e.params,i=e.slides;t.children("."+a.slideClass+"."+a.slideDuplicateClass+",."+a.slideClass+"."+a.slideBlankClass).remove(),i.removeAttr("data-swiper-slide-index")}},grabCursor:{setGrabCursor:function(e){var t=this;if(!(t.support.touch||!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)){var a=t.el;a.style.cursor="move",a.style.cursor=e?"-webkit-grabbing":"-webkit-grab",a.style.cursor=e?"-moz-grabbin":"-moz-grab",a.style.cursor=e?"grabbing":"grab"}},unsetGrabCursor:function(){var e=this;e.support.touch||e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.el.style.cursor="")}},manipulation:{appendSlide:function(e){var t=this,a=t.$wrapperEl,i=t.params;if(i.loop&&t.loopDestroy(),"object"==typeof e&&"length"in e)for(var n=0;n<e.length;n+=1)e[n]&&a.append(e[n]);else a.append(e);i.loop&&t.loopCreate(),i.observer&&t.support.observer||t.update()},prependSlide:function(e){var t=this,a=t.params,i=t.$wrapperEl,n=t.activeIndex;a.loop&&t.loopDestroy();var s=n+1;if("object"==typeof e&&"length"in e){for(var r=0;r<e.length;r+=1)e[r]&&i.prepend(e[r]);s=n+e.length}else i.prepend(e);a.loop&&t.loopCreate(),a.observer&&t.support.observer||t.update(),t.slideTo(s,0,!1)},addSlide:function(e,t){var a=this,i=a.$wrapperEl,n=a.params,s=a.activeIndex;n.loop&&(s-=a.loopedSlides,a.loopDestroy(),a.slides=i.children("."+n.slideClass));var r=a.slides.length;if(e<=0)a.prependSlide(t);else if(e>=r)a.appendSlide(t);else{for(var o=s>e?s+1:s,l=[],d=r-1;d>=e;d-=1){var p=a.slides.eq(d);p.remove(),l.unshift(p)}if("object"==typeof t&&"length"in t){for(var u=0;u<t.length;u+=1)t[u]&&i.append(t[u]);o=s>e?s+t.length:s}else i.append(t);for(var c=0;c<l.length;c+=1)i.append(l[c]);n.loop&&a.loopCreate(),n.observer&&a.support.observer||a.update(),n.loop?a.slideTo(o+a.loopedSlides,0,!1):a.slideTo(o,0,!1)}},removeSlide:function(e){var t=this,a=t.params,i=t.$wrapperEl,n=t.activeIndex;a.loop&&(n-=t.loopedSlides,t.loopDestroy(),t.slides=i.children("."+a.slideClass));var s,r=n;if("object"==typeof e&&"length"in e){for(var o=0;o<e.length;o+=1)s=e[o],t.slides[s]&&t.slides.eq(s).remove(),s<r&&(r-=1);r=Math.max(r,0)}else s=e,t.slides[s]&&t.slides.eq(s).remove(),s<r&&(r-=1),r=Math.max(r,0);a.loop&&t.loopCreate(),a.observer&&t.support.observer||t.update(),a.loop?t.slideTo(r+t.loopedSlides,0,!1):t.slideTo(r,0,!1)},removeAllSlides:function(){for(var e=[],t=0;t<this.slides.length;t+=1)e.push(t);this.removeSlide(e)}},events:{attachEvents:function(){var e=this,t=s(),a=e.params,i=e.touchEvents,n=e.el,r=e.wrapperEl,o=e.device,l=e.support;e.onTouchStart=$.bind(e),e.onTouchMove=D.bind(e),e.onTouchEnd=B.bind(e),a.cssMode&&(e.onScroll=V.bind(e)),e.onClick=H.bind(e);var d=!!a.nested;if(!l.touch&&l.pointerEvents)n.addEventListener(i.start,e.onTouchStart,!1),t.addEventListener(i.move,e.onTouchMove,d),t.addEventListener(i.end,e.onTouchEnd,!1);else{if(l.touch){var p=!("touchstart"!==i.start||!l.passiveListener||!a.passiveListeners)&&{passive:!0,capture:!1};n.addEventListener(i.start,e.onTouchStart,p),n.addEventListener(i.move,e.onTouchMove,l.passiveListener?{passive:!1,capture:d}:d),n.addEventListener(i.end,e.onTouchEnd,p),i.cancel&&n.addEventListener(i.cancel,e.onTouchEnd,p),R||(t.addEventListener("touchstart",F),R=!0)}(a.simulateTouch&&!o.ios&&!o.android||a.simulateTouch&&!l.touch&&o.ios)&&(n.addEventListener("mousedown",e.onTouchStart,!1),t.addEventListener("mousemove",e.onTouchMove,d),t.addEventListener("mouseup",e.onTouchEnd,!1))}(a.preventClicks||a.preventClicksPropagation)&&n.addEventListener("click",e.onClick,!0),a.cssMode&&r.addEventListener("scroll",e.onScroll),a.updateOnWindowResize?e.on(o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",G,!0):e.on("observerUpdate",G,!0)},detachEvents:function(){var e=this,t=s(),a=e.params,i=e.touchEvents,n=e.el,r=e.wrapperEl,o=e.device,l=e.support,d=!!a.nested;if(!l.touch&&l.pointerEvents)n.removeEventListener(i.start,e.onTouchStart,!1),t.removeEventListener(i.move,e.onTouchMove,d),t.removeEventListener(i.end,e.onTouchEnd,!1);else{if(l.touch){var p=!("onTouchStart"!==i.start||!l.passiveListener||!a.passiveListeners)&&{passive:!0,capture:!1};n.removeEventListener(i.start,e.onTouchStart,p),n.removeEventListener(i.move,e.onTouchMove,d),n.removeEventListener(i.end,e.onTouchEnd,p),i.cancel&&n.removeEventListener(i.cancel,e.onTouchEnd,p)}(a.simulateTouch&&!o.ios&&!o.android||a.simulateTouch&&!l.touch&&o.ios)&&(n.removeEventListener("mousedown",e.onTouchStart,!1),t.removeEventListener("mousemove",e.onTouchMove,d),t.removeEventListener("mouseup",e.onTouchEnd,!1))}(a.preventClicks||a.preventClicksPropagation)&&n.removeEventListener("click",e.onClick,!0),a.cssMode&&r.removeEventListener("scroll",e.onScroll),e.off(o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",G)}},breakpoints:{setBreakpoint:function(){var e=this,t=e.activeIndex,a=e.initialized,i=e.loopedSlides,n=void 0===i?0:i,s=e.params,r=e.$el,o=s.breakpoints;if(o&&(!o||0!==Object.keys(o).length)){var l=e.getBreakpoint(o,e.params.breakpointsBase,e.el);if(l&&e.currentBreakpoint!==l){var d=l in o?o[l]:void 0;d&&["slidesPerView","spaceBetween","slidesPerGroup","slidesPerGroupSkip","slidesPerColumn"].forEach((function(e){var t=d[e];void 0!==t&&(d[e]="slidesPerView"!==e||"AUTO"!==t&&"auto"!==t?"slidesPerView"===e?parseFloat(t):parseInt(t,10):"auto")}));var p=d||e.originalParams,u=s.slidesPerColumn>1,c=p.slidesPerColumn>1,v=s.enabled;u&&!c?(r.removeClass(s.containerModifierClass+"multirow "+s.containerModifierClass+"multirow-column"),e.emitContainerClasses()):!u&&c&&(r.addClass(s.containerModifierClass+"multirow"),"column"===p.slidesPerColumnFill&&r.addClass(s.containerModifierClass+"multirow-column"),e.emitContainerClasses());var f=p.direction&&p.direction!==s.direction,h=s.loop&&(p.slidesPerView!==s.slidesPerView||f);f&&a&&e.changeDirection(),S(e.params,p);var m=e.params.enabled;S(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),v&&!m?e.disable():!v&&m&&e.enable(),e.currentBreakpoint=l,e.emit("_beforeBreakpoint",p),h&&a&&(e.loopDestroy(),e.loopCreate(),e.updateSlides(),e.slideTo(t-n+e.loopedSlides,0,!1)),e.emit("breakpoint",p)}}},getBreakpoint:function(e,t,a){if(void 0===t&&(t="window"),e&&("container"!==t||a)){var i=!1,n=o(),s="window"===t?n.innerWidth:a.clientWidth,r="window"===t?n.innerHeight:a.clientHeight,l=Object.keys(e).map((function(e){if("string"==typeof e&&0===e.indexOf("@")){var t=parseFloat(e.substr(1));return{value:r*t,point:e}}return{value:e,point:e}}));l.sort((function(e,t){return parseInt(e.value,10)-parseInt(t.value,10)}));for(var d=0;d<l.length;d+=1){var p=l[d],u=p.point;p.value<=s&&(i=u)}return i||"max"}}},checkOverflow:{checkOverflow:function(){var e=this,t=e.params,a=e.isLocked,i=e.slides.length>0&&t.slidesOffsetBefore+t.spaceBetween*(e.slides.length-1)+e.slides[0].offsetWidth*e.slides.length;t.slidesOffsetBefore&&t.slidesOffsetAfter&&i?e.isLocked=i<=e.size:e.isLocked=1===e.snapGrid.length,e.allowSlideNext=!e.isLocked,e.allowSlidePrev=!e.isLocked,a!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock"),a&&a!==e.isLocked&&(e.isEnd=!1,e.navigation&&e.navigation.update())}},classes:{addClasses:function(){var e,t,a,i=this,n=i.classNames,s=i.params,r=i.rtl,o=i.$el,l=i.device,d=i.support,p=(e=["initialized",s.direction,{"pointer-events":d.pointerEvents&&!d.touch},{"free-mode":s.freeMode},{autoheight:s.autoHeight},{rtl:r},{multirow:s.slidesPerColumn>1},{"multirow-column":s.slidesPerColumn>1&&"column"===s.slidesPerColumnFill},{android:l.android},{ios:l.ios},{"css-mode":s.cssMode}],t=s.containerModifierClass,a=[],e.forEach((function(e){"object"==typeof e?Object.keys(e).forEach((function(i){e[i]&&a.push(t+i)})):"string"==typeof e&&a.push(t+e)})),a);n.push.apply(n,p),o.addClass([].concat(n).join(" ")),i.emitContainerClasses()},removeClasses:function(){var e=this,t=e.$el,a=e.classNames;t.removeClass(a.join(" ")),e.emitContainerClasses()}},images:{loadImage:function(e,t,a,i,n,s){var r,l=o();function d(){s&&s()}m(e).parent("picture")[0]||e.complete&&n?d():t?((r=new l.Image).onload=d,r.onerror=d,i&&(r.sizes=i),a&&(r.srcset=a),t&&(r.src=t)):d()},preloadImages:function(){var e=this;function t(){null!=e&&e&&!e.destroyed&&(void 0!==e.imagesLoaded&&(e.imagesLoaded+=1),e.imagesLoaded===e.imagesToLoad.length&&(e.params.updateOnImagesReady&&e.update(),e.emit("imagesReady")))}e.imagesToLoad=e.$el.find("img");for(var a=0;a<e.imagesToLoad.length;a+=1){var i=e.imagesToLoad[a];e.loadImage(i,i.currentSrc||i.getAttribute("src"),i.srcset||i.getAttribute("srcset"),i.sizes||i.getAttribute("sizes"),!0,t)}}}},q={},W=function(){function t(){for(var e,a,i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];if(1===n.length&&n[0].constructor&&"Object"===Object.prototype.toString.call(n[0]).slice(8,-1)?a=n[0]:(e=n[0],a=n[1]),a||(a={}),a=S({},a),e&&!a.el&&(a.el=e),a.el&&m(a.el).length>1){var r=[];return m(a.el).each((function(e){var i=S({},a,{el:e});r.push(new t(i))})),r}var o=this;o.__swiper__=!0,o.support=L(),o.device=z({userAgent:a.userAgent}),o.browser=O(),o.eventsListeners={},o.eventsAnyListeners=[],void 0===o.modules&&(o.modules={}),Object.keys(o.modules).forEach((function(e){var t=o.modules[e];if(t.params){var i=Object.keys(t.params)[0],n=t.params[i];if("object"!=typeof n||null===n)return;if(["navigation","pagination","scrollbar"].indexOf(i)>=0&&!0===a[i]&&(a[i]={auto:!0}),!(i in a)||!("enabled"in n))return;!0===a[i]&&(a[i]={enabled:!0}),"object"!=typeof a[i]||"enabled"in a[i]||(a[i].enabled=!0),a[i]||(a[i]={enabled:!1})}}));var l,d,p=S({},_);return o.useParams(p),o.params=S({},p,q,a),o.originalParams=S({},o.params),o.passedParams=S({},a),o.params&&o.params.on&&Object.keys(o.params.on).forEach((function(e){o.on(e,o.params.on[e])})),o.params&&o.params.onAny&&o.onAny(o.params.onAny),o.$=m,S(o,{enabled:o.params.enabled,el:e,classNames:[],slides:m(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:function(){return"horizontal"===o.params.direction},isVertical:function(){return"vertical"===o.params.direction},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEvents:(l=["touchstart","touchmove","touchend","touchcancel"],d=["mousedown","mousemove","mouseup"],o.support.pointerEvents&&(d=["pointerdown","pointermove","pointerup"]),o.touchEventsTouch={start:l[0],move:l[1],end:l[2],cancel:l[3]},o.touchEventsDesktop={start:d[0],move:d[1],end:d[2]},o.support.touch||!o.params.simulateTouch?o.touchEventsTouch:o.touchEventsDesktop),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,formElements:"input, select, option, textarea, button, video, label",lastClickTime:T(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.useModules(),o.emit("_swiper"),o.params.init&&o.init(),o}var a,i,n,r=t.prototype;return r.enable=function(){var e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))},r.disable=function(){var e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))},r.setProgress=function(e,t){var a=this;e=Math.min(Math.max(e,0),1);var i=a.minTranslate(),n=(a.maxTranslate()-i)*e+i;a.translateTo(n,void 0===t?0:t),a.updateActiveIndex(),a.updateSlidesClasses()},r.emitContainerClasses=function(){var e=this;if(e.params._emitClasses&&e.el){var t=e.el.className.split(" ").filter((function(t){return 0===t.indexOf("swiper-container")||0===t.indexOf(e.params.containerModifierClass)}));e.emit("_containerClasses",t.join(" "))}},r.getSlideClasses=function(e){var t=this;return e.className.split(" ").filter((function(e){return 0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)})).join(" ")},r.emitSlidesClasses=function(){var e=this;if(e.params._emitClasses&&e.el){var t=[];e.slides.each((function(a){var i=e.getSlideClasses(a);t.push({slideEl:a,classNames:i}),e.emit("_slideClass",a,i)})),e.emit("_slideClasses",t)}},r.slidesPerViewDynamic=function(){var e=this,t=e.params,a=e.slides,i=e.slidesGrid,n=e.size,s=e.activeIndex,r=1;if(t.centeredSlides){for(var o,l=a[s].swiperSlideSize,d=s+1;d<a.length;d+=1)a[d]&&!o&&(r+=1,(l+=a[d].swiperSlideSize)>n&&(o=!0));for(var p=s-1;p>=0;p-=1)a[p]&&!o&&(r+=1,(l+=a[p].swiperSlideSize)>n&&(o=!0))}else for(var u=s+1;u<a.length;u+=1)i[u]-i[s]<n&&(r+=1);return r},r.update=function(){var e=this;if(e&&!e.destroyed){var t=e.snapGrid,a=e.params;a.breakpoints&&e.setBreakpoint(),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.params.freeMode?(i(),e.params.autoHeight&&e.updateAutoHeight()):(("auto"===e.params.slidesPerView||e.params.slidesPerView>1)&&e.isEnd&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0))||i(),a.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}function i(){var t=e.rtlTranslate?-1*e.translate:e.translate,a=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(a),e.updateActiveIndex(),e.updateSlidesClasses()}},r.changeDirection=function(e,t){void 0===t&&(t=!0);var a=this,i=a.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(a.$el.removeClass(""+a.params.containerModifierClass+i).addClass(""+a.params.containerModifierClass+e),a.emitContainerClasses(),a.params.direction=e,a.slides.each((function(t){"vertical"===e?t.style.width="":t.style.height=""})),a.emit("changeDirection"),t&&a.update()),a},r.mount=function(e){var t=this;if(t.mounted)return!0;var a=m(e||t.params.el);if(!(e=a[0]))return!1;e.swiper=t;var i=function(){if(e&&e.shadowRoot&&e.shadowRoot.querySelector){var i=m(e.shadowRoot.querySelector("."+t.params.wrapperClass));return i.children=function(e){return a.children(e)},i}return a.children("."+t.params.wrapperClass)}();if(0===i.length&&t.params.createElements){var n=s().createElement("div");i=m(n),n.className=t.params.wrapperClass,a.append(n),a.children("."+t.params.slideClass).each((function(e){i.append(e)}))}return S(t,{$el:a,el:e,$wrapperEl:i,wrapperEl:i[0],mounted:!0,rtl:"rtl"===e.dir.toLowerCase()||"rtl"===a.css("direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===e.dir.toLowerCase()||"rtl"===a.css("direction")),wrongRTL:"-webkit-box"===i.css("display")}),!0},r.init=function(e){var t=this;return t.initialized||!1===t.mount(e)||(t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.params.loop&&t.loopCreate(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.preloadImages&&t.preloadImages(),t.params.loop?t.slideTo(t.params.initialSlide+t.loopedSlides,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.attachEvents(),t.initialized=!0,t.emit("init"),t.emit("afterInit")),t},r.destroy=function(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);var a,i=this,n=i.params,s=i.$el,r=i.$wrapperEl,o=i.slides;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),n.loop&&i.loopDestroy(),t&&(i.removeClasses(),s.removeAttr("style"),r.removeAttr("style"),o&&o.length&&o.removeClass([n.slideVisibleClass,n.slideActiveClass,n.slideNextClass,n.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index")),i.emit("destroy"),Object.keys(i.eventsListeners).forEach((function(e){i.off(e)})),!1!==e&&(i.$el[0].swiper=null,a=i,Object.keys(a).forEach((function(e){try{a[e]=null}catch(e){}try{delete a[e]}catch(e){}}))),i.destroyed=!0),null},t.extendDefaults=function(e){S(q,e)},t.installModule=function(e){t.prototype.modules||(t.prototype.modules={});var a=e.name||Object.keys(t.prototype.modules).length+"_"+T();t.prototype.modules[a]=e},t.use=function(e){return Array.isArray(e)?(e.forEach((function(e){return t.installModule(e)})),t):(t.installModule(e),t)},a=t,n=[{key:"extendedDefaults",get:function(){return q}},{key:"defaults",get:function(){return _}}],(i=null)&&e(a.prototype,i),n&&e(a,n),t}();Object.keys(j).forEach((function(e){Object.keys(j[e]).forEach((function(t){W.prototype[t]=j[e][t]}))})),W.use([I,N]);var X={handle:function(e){var t=this;if(t.enabled){var a=o(),i=s(),n=t.rtlTranslate,r=e;r.originalEvent&&(r=r.originalEvent);var l=r.keyCode||r.charCode,d=t.params.keyboard.pageUpDown,p=d&&33===l,u=d&&34===l,c=37===l,v=39===l,f=38===l,h=40===l;if(!t.allowSlideNext&&(t.isHorizontal()&&v||t.isVertical()&&h||u))return!1;if(!t.allowSlidePrev&&(t.isHorizontal()&&c||t.isVertical()&&f||p))return!1;if(!(r.shiftKey||r.altKey||r.ctrlKey||r.metaKey||i.activeElement&&i.activeElement.nodeName&&("input"===i.activeElement.nodeName.toLowerCase()||"textarea"===i.activeElement.nodeName.toLowerCase()))){if(t.params.keyboard.onlyInViewport&&(p||u||c||v||f||h)){var m=!1;if(t.$el.parents("."+t.params.slideClass).length>0&&0===t.$el.parents("."+t.params.slideActiveClass).length)return;var g=t.$el,y=g[0].clientWidth,b=g[0].clientHeight,w=a.innerWidth,C=a.innerHeight,T=t.$el.offset();n&&(T.left-=t.$el[0].scrollLeft);for(var E=[[T.left,T.top],[T.left+y,T.top],[T.left,T.top+b],[T.left+y,T.top+b]],x=0;x<E.length;x+=1){var S=E[x];if(S[0]>=0&&S[0]<=w&&S[1]>=0&&S[1]<=C){if(0===S[0]&&0===S[1])continue;m=!0}}if(!m)return}t.isHorizontal()?((p||u||c||v)&&(r.preventDefault?r.preventDefault():r.returnValue=!1),((u||v)&&!n||(p||c)&&n)&&t.slideNext(),((p||c)&&!n||(u||v)&&n)&&t.slidePrev()):((p||u||f||h)&&(r.preventDefault?r.preventDefault():r.returnValue=!1),(u||h)&&t.slideNext(),(p||f)&&t.slidePrev()),t.emit("keyPress",l)}}},enable:function(){var e=this,t=s();e.keyboard.enabled||(m(t).on("keydown",e.keyboard.handle),e.keyboard.enabled=!0)},disable:function(){var e=this,t=s();e.keyboard.enabled&&(m(t).off("keydown",e.keyboard.handle),e.keyboard.enabled=!1)}},Y={toggleEl:function(e,t){e[t?"addClass":"removeClass"](this.params.navigation.disabledClass),e[0]&&"BUTTON"===e[0].tagName&&(e[0].disabled=t)},update:function(){var e=this,t=e.params.navigation,a=e.navigation.toggleEl;if(!e.params.loop){var i=e.navigation,n=i.$nextEl,s=i.$prevEl;s&&s.length>0&&(e.isBeginning?a(s,!0):a(s,!1),e.params.watchOverflow&&e.enabled&&s[e.isLocked?"addClass":"removeClass"](t.lockClass)),n&&n.length>0&&(e.isEnd?a(n,!0):a(n,!1),e.params.watchOverflow&&e.enabled&&n[e.isLocked?"addClass":"removeClass"](t.lockClass))}},onPrevClick:function(e){var t=this;e.preventDefault(),t.isBeginning&&!t.params.loop||t.slidePrev()},onNextClick:function(e){var t=this;e.preventDefault(),t.isEnd&&!t.params.loop||t.slideNext()},init:function(){var e,t,a=this,i=a.params.navigation;(a.params.navigation=P(a.$el,a.params.navigation,a.params.createElements,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),i.nextEl||i.prevEl)&&(i.nextEl&&(e=m(i.nextEl),a.params.uniqueNavElements&&"string"==typeof i.nextEl&&e.length>1&&1===a.$el.find(i.nextEl).length&&(e=a.$el.find(i.nextEl))),i.prevEl&&(t=m(i.prevEl),a.params.uniqueNavElements&&"string"==typeof i.prevEl&&t.length>1&&1===a.$el.find(i.prevEl).length&&(t=a.$el.find(i.prevEl))),e&&e.length>0&&e.on("click",a.navigation.onNextClick),t&&t.length>0&&t.on("click",a.navigation.onPrevClick),S(a.navigation,{$nextEl:e,nextEl:e&&e[0],$prevEl:t,prevEl:t&&t[0]}),a.enabled||(e&&e.addClass(i.lockClass),t&&t.addClass(i.lockClass)))},destroy:function(){var e=this,t=e.navigation,a=t.$nextEl,i=t.$prevEl;a&&a.length&&(a.off("click",e.navigation.onNextClick),a.removeClass(e.params.navigation.disabledClass)),i&&i.length&&(i.off("click",e.navigation.onPrevClick),i.removeClass(e.params.navigation.disabledClass))}},U={update:function(){var e=this,t=e.rtl,a=e.params.pagination;if(a.el&&e.pagination.el&&e.pagination.$el&&0!==e.pagination.$el.length){var i,n=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,s=e.pagination.$el,r=e.params.loop?Math.ceil((n-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?((i=Math.ceil((e.activeIndex-e.loopedSlides)/e.params.slidesPerGroup))>n-1-2*e.loopedSlides&&(i-=n-2*e.loopedSlides),i>r-1&&(i-=r),i<0&&"bullets"!==e.params.paginationType&&(i=r+i)):i=void 0!==e.snapIndex?e.snapIndex:e.activeIndex||0,"bullets"===a.type&&e.pagination.bullets&&e.pagination.bullets.length>0){var o,l,d,p=e.pagination.bullets;if(a.dynamicBullets&&(e.pagination.bulletSize=p.eq(0)[e.isHorizontal()?"outerWidth":"outerHeight"](!0),s.css(e.isHorizontal()?"width":"height",e.pagination.bulletSize*(a.dynamicMainBullets+4)+"px"),a.dynamicMainBullets>1&&void 0!==e.previousIndex&&(e.pagination.dynamicBulletIndex+=i-e.previousIndex,e.pagination.dynamicBulletIndex>a.dynamicMainBullets-1?e.pagination.dynamicBulletIndex=a.dynamicMainBullets-1:e.pagination.dynamicBulletIndex<0&&(e.pagination.dynamicBulletIndex=0)),o=i-e.pagination.dynamicBulletIndex,d=((l=o+(Math.min(p.length,a.dynamicMainBullets)-1))+o)/2),p.removeClass(a.bulletActiveClass+" "+a.bulletActiveClass+"-next "+a.bulletActiveClass+"-next-next "+a.bulletActiveClass+"-prev "+a.bulletActiveClass+"-prev-prev "+a.bulletActiveClass+"-main"),s.length>1)p.each((function(e){var t=m(e),n=t.index();n===i&&t.addClass(a.bulletActiveClass),a.dynamicBullets&&(n>=o&&n<=l&&t.addClass(a.bulletActiveClass+"-main"),n===o&&t.prev().addClass(a.bulletActiveClass+"-prev").prev().addClass(a.bulletActiveClass+"-prev-prev"),n===l&&t.next().addClass(a.bulletActiveClass+"-next").next().addClass(a.bulletActiveClass+"-next-next"))}));else{var u=p.eq(i),c=u.index();if(u.addClass(a.bulletActiveClass),a.dynamicBullets){for(var v=p.eq(o),f=p.eq(l),h=o;h<=l;h+=1)p.eq(h).addClass(a.bulletActiveClass+"-main");if(e.params.loop)if(c>=p.length-a.dynamicMainBullets){for(var g=a.dynamicMainBullets;g>=0;g-=1)p.eq(p.length-g).addClass(a.bulletActiveClass+"-main");p.eq(p.length-a.dynamicMainBullets-1).addClass(a.bulletActiveClass+"-prev")}else v.prev().addClass(a.bulletActiveClass+"-prev").prev().addClass(a.bulletActiveClass+"-prev-prev"),f.next().addClass(a.bulletActiveClass+"-next").next().addClass(a.bulletActiveClass+"-next-next");else v.prev().addClass(a.bulletActiveClass+"-prev").prev().addClass(a.bulletActiveClass+"-prev-prev"),f.next().addClass(a.bulletActiveClass+"-next").next().addClass(a.bulletActiveClass+"-next-next")}}if(a.dynamicBullets){var y=Math.min(p.length,a.dynamicMainBullets+4),b=(e.pagination.bulletSize*y-e.pagination.bulletSize)/2-d*e.pagination.bulletSize,w=t?"right":"left";p.css(e.isHorizontal()?w:"top",b+"px")}}if("fraction"===a.type&&(s.find(k(a.currentClass)).text(a.formatFractionCurrent(i+1)),s.find(k(a.totalClass)).text(a.formatFractionTotal(r))),"progressbar"===a.type){var C;C=a.progressbarOpposite?e.isHorizontal()?"vertical":"horizontal":e.isHorizontal()?"horizontal":"vertical";var T=(i+1)/r,E=1,x=1;"horizontal"===C?E=T:x=T,s.find(k(a.progressbarFillClass)).transform("translate3d(0,0,0) scaleX("+E+") scaleY("+x+")").transition(e.params.speed)}"custom"===a.type&&a.renderCustom?(s.html(a.renderCustom(e,i+1,r)),e.emit("paginationRender",s[0])):e.emit("paginationUpdate",s[0]),e.params.watchOverflow&&e.enabled&&s[e.isLocked?"addClass":"removeClass"](a.lockClass)}},render:function(){var e=this,t=e.params.pagination;if(t.el&&e.pagination.el&&e.pagination.$el&&0!==e.pagination.$el.length){var a=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,i=e.pagination.$el,n="";if("bullets"===t.type){var s=e.params.loop?Math.ceil((a-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&!e.params.loop&&s>a&&(s=a);for(var r=0;r<s;r+=1)t.renderBullet?n+=t.renderBullet.call(e,r,t.bulletClass):n+="<"+t.bulletElement+' class="'+t.bulletClass+'"></'+t.bulletElement+">";i.html(n),e.pagination.bullets=i.find(k(t.bulletClass))}"fraction"===t.type&&(n=t.renderFraction?t.renderFraction.call(e,t.currentClass,t.totalClass):'<span class="'+t.currentClass+'"></span> / <span class="'+t.totalClass+'"></span>',i.html(n)),"progressbar"===t.type&&(n=t.renderProgressbar?t.renderProgressbar.call(e,t.progressbarFillClass):'<span class="'+t.progressbarFillClass+'"></span>',i.html(n)),"custom"!==t.type&&e.emit("paginationRender",e.pagination.$el[0])}},init:function(){var e=this;e.params.pagination=P(e.$el,e.params.pagination,e.params.createElements,{el:"swiper-pagination"});var t=e.params.pagination;if(t.el){var a=m(t.el);0!==a.length&&(e.params.uniqueNavElements&&"string"==typeof t.el&&a.length>1&&(a=e.$el.find(t.el)),"bullets"===t.type&&t.clickable&&a.addClass(t.clickableClass),a.addClass(t.modifierClass+t.type),"bullets"===t.type&&t.dynamicBullets&&(a.addClass(""+t.modifierClass+t.type+"-dynamic"),e.pagination.dynamicBulletIndex=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&a.addClass(t.progressbarOppositeClass),t.clickable&&a.on("click",k(t.bulletClass),(function(t){t.preventDefault();var a=m(this).index()*e.params.slidesPerGroup;e.params.loop&&(a+=e.loopedSlides),e.slideTo(a)})),S(e.pagination,{$el:a,el:a[0]}),e.enabled||a.addClass(t.lockClass))}},destroy:function(){var e=this,t=e.params.pagination;if(t.el&&e.pagination.el&&e.pagination.$el&&0!==e.pagination.$el.length){var a=e.pagination.$el;a.removeClass(t.hiddenClass),a.removeClass(t.modifierClass+t.type),e.pagination.bullets&&e.pagination.bullets.removeClass(t.bulletActiveClass),t.clickable&&a.off("click",k(t.bulletClass))}}},K={loadInSlide:function(e,t){void 0===t&&(t=!0);var a=this,i=a.params.lazy;if(void 0!==e&&0!==a.slides.length){var n=a.virtual&&a.params.virtual.enabled?a.$wrapperEl.children("."+a.params.slideClass+'[data-swiper-slide-index="'+e+'"]'):a.slides.eq(e),s=n.find("."+i.elementClass+":not(."+i.loadedClass+"):not(."+i.loadingClass+")");!n.hasClass(i.elementClass)||n.hasClass(i.loadedClass)||n.hasClass(i.loadingClass)||s.push(n[0]),0!==s.length&&s.each((function(e){var s=m(e);s.addClass(i.loadingClass);var r=s.attr("data-background"),o=s.attr("data-src"),l=s.attr("data-srcset"),d=s.attr("data-sizes"),p=s.parent("picture");a.loadImage(s[0],o||r,l,d,!1,(function(){if(null!=a&&a&&(!a||a.params)&&!a.destroyed){if(r?(s.css("background-image",'url("'+r+'")'),s.removeAttr("data-background")):(l&&(s.attr("srcset",l),s.removeAttr("data-srcset")),d&&(s.attr("sizes",d),s.removeAttr("data-sizes")),p.length&&p.children("source").each((function(e){var t=m(e);t.attr("data-srcset")&&(t.attr("srcset",t.attr("data-srcset")),t.removeAttr("data-srcset"))})),o&&(s.attr("src",o),s.removeAttr("data-src"))),s.addClass(i.loadedClass).removeClass(i.loadingClass),n.find("."+i.preloaderClass).remove(),a.params.loop&&t){var e=n.attr("data-swiper-slide-index");if(n.hasClass(a.params.slideDuplicateClass)){var u=a.$wrapperEl.children('[data-swiper-slide-index="'+e+'"]:not(.'+a.params.slideDuplicateClass+")");a.lazy.loadInSlide(u.index(),!1)}else{var c=a.$wrapperEl.children("."+a.params.slideDuplicateClass+'[data-swiper-slide-index="'+e+'"]');a.lazy.loadInSlide(c.index(),!1)}}a.emit("lazyImageReady",n[0],s[0]),a.params.autoHeight&&a.updateAutoHeight()}})),a.emit("lazyImageLoad",n[0],s[0])}))}},load:function(){var e=this,t=e.$wrapperEl,a=e.params,i=e.slides,n=e.activeIndex,s=e.virtual&&a.virtual.enabled,r=a.lazy,o=a.slidesPerView;function l(e){if(s){if(t.children("."+a.slideClass+'[data-swiper-slide-index="'+e+'"]').length)return!0}else if(i[e])return!0;return!1}function d(e){return s?m(e).attr("data-swiper-slide-index"):m(e).index()}if("auto"===o&&(o=0),e.lazy.initialImageLoaded||(e.lazy.initialImageLoaded=!0),e.params.watchSlidesVisibility)t.children("."+a.slideVisibleClass).each((function(t){var a=s?m(t).attr("data-swiper-slide-index"):m(t).index();e.lazy.loadInSlide(a)}));else if(o>1)for(var p=n;p<n+o;p+=1)l(p)&&e.lazy.loadInSlide(p);else e.lazy.loadInSlide(n);if(r.loadPrevNext)if(o>1||r.loadPrevNextAmount&&r.loadPrevNextAmount>1){for(var u=r.loadPrevNextAmount,c=o,v=Math.min(n+c+Math.max(u,c),i.length),f=Math.max(n-Math.max(c,u),0),h=n+o;h<v;h+=1)l(h)&&e.lazy.loadInSlide(h);for(var g=f;g<n;g+=1)l(g)&&e.lazy.loadInSlide(g)}else{var y=t.children("."+a.slideNextClass);y.length>0&&e.lazy.loadInSlide(d(y));var b=t.children("."+a.slidePrevClass);b.length>0&&e.lazy.loadInSlide(d(b))}},checkInViewOnLoad:function(){var e=o(),t=this;if(t&&!t.destroyed){var a=t.params.lazy.scrollingElement?m(t.params.lazy.scrollingElement):m(e),i=a[0]===e,n=i?e.innerWidth:a[0].offsetWidth,s=i?e.innerHeight:a[0].offsetHeight,r=t.$el.offset(),l=!1;t.rtlTranslate&&(r.left-=t.$el[0].scrollLeft);for(var d=[[r.left,r.top],[r.left+t.width,r.top],[r.left,r.top+t.height],[r.left+t.width,r.top+t.height]],p=0;p<d.length;p+=1){var u=d[p];if(u[0]>=0&&u[0]<=n&&u[1]>=0&&u[1]<=s){if(0===u[0]&&0===u[1])continue;l=!0}}var c=!("touchstart"!==t.touchEvents.start||!t.support.passiveListener||!t.params.passiveListeners)&&{passive:!0,capture:!1};l?(t.lazy.load(),a.off("scroll",t.lazy.checkInViewOnLoad,c)):t.lazy.scrollHandlerAttached||(t.lazy.scrollHandlerAttached=!0,a.on("scroll",t.lazy.checkInViewOnLoad,c))}}},J={LinearSpline:function(e,t){var a,i,n,s,r,o=function(e,t){for(i=-1,a=e.length;a-i>1;)e[n=a+i>>1]<=t?i=n:a=n;return a};return this.x=e,this.y=t,this.lastIndex=e.length-1,this.interpolate=function(e){return e?(r=o(this.x,e),s=r-1,(e-this.x[s])*(this.y[r]-this.y[s])/(this.x[r]-this.x[s])+this.y[s]):0},this},getInterpolateFunction:function(e){var t=this;t.controller.spline||(t.controller.spline=t.params.loop?new J.LinearSpline(t.slidesGrid,e.slidesGrid):new J.LinearSpline(t.snapGrid,e.snapGrid))},setTranslate:function(e,t){var a,i,n=this,s=n.controller.control,r=n.constructor;function o(e){var t=n.rtlTranslate?-n.translate:n.translate;"slide"===n.params.controller.by&&(n.controller.getInterpolateFunction(e),i=-n.controller.spline.interpolate(-t)),i&&"container"!==n.params.controller.by||(a=(e.maxTranslate()-e.minTranslate())/(n.maxTranslate()-n.minTranslate()),i=(t-n.minTranslate())*a+e.minTranslate()),n.params.controller.inverse&&(i=e.maxTranslate()-i),e.updateProgress(i),e.setTranslate(i,n),e.updateActiveIndex(),e.updateSlidesClasses()}if(Array.isArray(s))for(var l=0;l<s.length;l+=1)s[l]!==t&&s[l]instanceof r&&o(s[l]);else s instanceof r&&t!==s&&o(s)},setTransition:function(e,t){var a,i=this,n=i.constructor,s=i.controller.control;function r(t){t.setTransition(e,i),0!==e&&(t.transitionStart(),t.params.autoHeight&&C((function(){t.updateAutoHeight()})),t.$wrapperEl.transitionEnd((function(){s&&(t.params.loop&&"slide"===i.params.controller.by&&t.loopFix(),t.transitionEnd())})))}if(Array.isArray(s))for(a=0;a<s.length;a+=1)s[a]!==t&&s[a]instanceof n&&r(s[a]);else s instanceof n&&t!==s&&r(s)}},Q={getRandomNumber:function(e){void 0===e&&(e=16);return"x".repeat(e).replace(/x/g,(function(){return Math.round(16*Math.random()).toString(16)}))},makeElFocusable:function(e){return e.attr("tabIndex","0"),e},makeElNotFocusable:function(e){return e.attr("tabIndex","-1"),e},addElRole:function(e,t){return e.attr("role",t),e},addElRoleDescription:function(e,t){return e.attr("aria-roledescription",t),e},addElControls:function(e,t){return e.attr("aria-controls",t),e},addElLabel:function(e,t){return e.attr("aria-label",t),e},addElId:function(e,t){return e.attr("id",t),e},addElLive:function(e,t){return e.attr("aria-live",t),e},disableEl:function(e){return e.attr("aria-disabled",!0),e},enableEl:function(e){return e.attr("aria-disabled",!1),e},onEnterOrSpaceKey:function(e){if(13===e.keyCode||32===e.keyCode){var t=this,a=t.params.a11y,i=m(e.target);t.navigation&&t.navigation.$nextEl&&i.is(t.navigation.$nextEl)&&(t.isEnd&&!t.params.loop||t.slideNext(),t.isEnd?t.a11y.notify(a.lastSlideMessage):t.a11y.notify(a.nextSlideMessage)),t.navigation&&t.navigation.$prevEl&&i.is(t.navigation.$prevEl)&&(t.isBeginning&&!t.params.loop||t.slidePrev(),t.isBeginning?t.a11y.notify(a.firstSlideMessage):t.a11y.notify(a.prevSlideMessage)),t.pagination&&i.is(k(t.params.pagination.bulletClass))&&i[0].click()}},notify:function(e){var t=this.a11y.liveRegion;0!==t.length&&(t.html(""),t.html(e))},updateNavigation:function(){var e=this;if(!e.params.loop&&e.navigation){var t=e.navigation,a=t.$nextEl,i=t.$prevEl;i&&i.length>0&&(e.isBeginning?(e.a11y.disableEl(i),e.a11y.makeElNotFocusable(i)):(e.a11y.enableEl(i),e.a11y.makeElFocusable(i))),a&&a.length>0&&(e.isEnd?(e.a11y.disableEl(a),e.a11y.makeElNotFocusable(a)):(e.a11y.enableEl(a),e.a11y.makeElFocusable(a)))}},updatePagination:function(){var e=this,t=e.params.a11y;e.pagination&&e.params.pagination.clickable&&e.pagination.bullets&&e.pagination.bullets.length&&e.pagination.bullets.each((function(a){var i=m(a);e.a11y.makeElFocusable(i),e.params.pagination.renderBullet||(e.a11y.addElRole(i,"button"),e.a11y.addElLabel(i,t.paginationBulletMessage.replace(/\{\{index\}\}/,i.index()+1)))}))},init:function(){var e=this,t=e.params.a11y;e.$el.append(e.a11y.liveRegion);var a=e.$el;t.containerRoleDescriptionMessage&&e.a11y.addElRoleDescription(a,t.containerRoleDescriptionMessage),t.containerMessage&&e.a11y.addElLabel(a,t.containerMessage);var i,n,s=e.$wrapperEl,r=s.attr("id")||"swiper-wrapper-"+e.a11y.getRandomNumber(16),o=e.params.autoplay&&e.params.autoplay.enabled?"off":"polite";e.a11y.addElId(s,r),e.a11y.addElLive(s,o),t.itemRoleDescriptionMessage&&e.a11y.addElRoleDescription(m(e.slides),t.itemRoleDescriptionMessage),e.a11y.addElRole(m(e.slides),t.slideRole),e.slides.each((function(a){var i=m(a),n=t.slideLabelMessage.replace(/\{\{index\}\}/,i.index()+1).replace(/\{\{slidesLength\}\}/,e.slides.length);e.a11y.addElLabel(i,n)})),e.navigation&&e.navigation.$nextEl&&(i=e.navigation.$nextEl),e.navigation&&e.navigation.$prevEl&&(n=e.navigation.$prevEl),i&&i.length&&(e.a11y.makeElFocusable(i),"BUTTON"!==i[0].tagName&&(e.a11y.addElRole(i,"button"),i.on("keydown",e.a11y.onEnterOrSpaceKey)),e.a11y.addElLabel(i,t.nextSlideMessage),e.a11y.addElControls(i,r)),n&&n.length&&(e.a11y.makeElFocusable(n),"BUTTON"!==n[0].tagName&&(e.a11y.addElRole(n,"button"),n.on("keydown",e.a11y.onEnterOrSpaceKey)),e.a11y.addElLabel(n,t.prevSlideMessage),e.a11y.addElControls(n,r)),e.pagination&&e.params.pagination.clickable&&e.pagination.bullets&&e.pagination.bullets.length&&e.pagination.$el.on("keydown",k(e.params.pagination.bulletClass),e.a11y.onEnterOrSpaceKey)},destroy:function(){var e,t,a=this;a.a11y.liveRegion&&a.a11y.liveRegion.length>0&&a.a11y.liveRegion.remove(),a.navigation&&a.navigation.$nextEl&&(e=a.navigation.$nextEl),a.navigation&&a.navigation.$prevEl&&(t=a.navigation.$prevEl),e&&e.off("keydown",a.a11y.onEnterOrSpaceKey),t&&t.off("keydown",a.a11y.onEnterOrSpaceKey),a.pagination&&a.params.pagination.clickable&&a.pagination.bullets&&a.pagination.bullets.length&&a.pagination.$el.off("keydown",k(a.params.pagination.bulletClass),a.a11y.onEnterOrSpaceKey)}},Z={onHashCange:function(){var e=this,t=s();e.emit("hashChange");var a=t.location.hash.replace("#","");if(a!==e.slides.eq(e.activeIndex).attr("data-hash")){var i=e.$wrapperEl.children("."+e.params.slideClass+'[data-hash="'+a+'"]').index();if(void 0===i)return;e.slideTo(i)}},setHash:function(){var e=this,t=o(),a=s();if(e.hashNavigation.initialized&&e.params.hashNavigation.enabled)if(e.params.hashNavigation.replaceState&&t.history&&t.history.replaceState)t.history.replaceState(null,null,"#"+e.slides.eq(e.activeIndex).attr("data-hash")||""),e.emit("hashSet");else{var i=e.slides.eq(e.activeIndex),n=i.attr("data-hash")||i.attr("data-history");a.location.hash=n||"",e.emit("hashSet")}},init:function(){var e=this,t=s(),a=o();if(!(!e.params.hashNavigation.enabled||e.params.history&&e.params.history.enabled)){e.hashNavigation.initialized=!0;var i=t.location.hash.replace("#","");if(i)for(var n=0,r=e.slides.length;n<r;n+=1){var l=e.slides.eq(n);if((l.attr("data-hash")||l.attr("data-history"))===i&&!l.hasClass(e.params.slideDuplicateClass)){var d=l.index();e.slideTo(d,0,e.params.runCallbacksOnInit,!0)}}e.params.hashNavigation.watchState&&m(a).on("hashchange",e.hashNavigation.onHashCange)}},destroy:function(){var e=o();this.params.hashNavigation.watchState&&m(e).off("hashchange",this.hashNavigation.onHashCange)}},ee={run:function(){var e=this,t=e.slides.eq(e.activeIndex),a=e.params.autoplay.delay;t.attr("data-swiper-autoplay")&&(a=t.attr("data-swiper-autoplay")||e.params.autoplay.delay),clearTimeout(e.autoplay.timeout),e.autoplay.timeout=C((function(){var t;e.params.autoplay.reverseDirection?e.params.loop?(e.loopFix(),t=e.slidePrev(e.params.speed,!0,!0),e.emit("autoplay")):e.isBeginning?e.params.autoplay.stopOnLastSlide?e.autoplay.stop():(t=e.slideTo(e.slides.length-1,e.params.speed,!0,!0),e.emit("autoplay")):(t=e.slidePrev(e.params.speed,!0,!0),e.emit("autoplay")):e.params.loop?(e.loopFix(),t=e.slideNext(e.params.speed,!0,!0),e.emit("autoplay")):e.isEnd?e.params.autoplay.stopOnLastSlide?e.autoplay.stop():(t=e.slideTo(0,e.params.speed,!0,!0),e.emit("autoplay")):(t=e.slideNext(e.params.speed,!0,!0),e.emit("autoplay")),(e.params.cssMode&&e.autoplay.running||!1===t)&&e.autoplay.run()}),a)},start:function(){var e=this;return void 0===e.autoplay.timeout&&(!e.autoplay.running&&(e.autoplay.running=!0,e.emit("autoplayStart"),e.autoplay.run(),!0))},stop:function(){var e=this;return!!e.autoplay.running&&(void 0!==e.autoplay.timeout&&(e.autoplay.timeout&&(clearTimeout(e.autoplay.timeout),e.autoplay.timeout=void 0),e.autoplay.running=!1,e.emit("autoplayStop"),!0))},pause:function(e){var t=this;t.autoplay.running&&(t.autoplay.paused||(t.autoplay.timeout&&clearTimeout(t.autoplay.timeout),t.autoplay.paused=!0,0!==e&&t.params.autoplay.waitForTransition?["transitionend","webkitTransitionEnd"].forEach((function(e){t.$wrapperEl[0].addEventListener(e,t.autoplay.onTransitionEnd)})):(t.autoplay.paused=!1,t.autoplay.run())))},onVisibilityChange:function(){var e=this,t=s();"hidden"===t.visibilityState&&e.autoplay.running&&e.autoplay.pause(),"visible"===t.visibilityState&&e.autoplay.paused&&(e.autoplay.run(),e.autoplay.paused=!1)},onTransitionEnd:function(e){var t=this;t&&!t.destroyed&&t.$wrapperEl&&e.target===t.$wrapperEl[0]&&(["transitionend","webkitTransitionEnd"].forEach((function(e){t.$wrapperEl[0].removeEventListener(e,t.autoplay.onTransitionEnd)})),t.autoplay.paused=!1,t.autoplay.running?t.autoplay.run():t.autoplay.stop())},onMouseEnter:function(){var e=this;e.params.autoplay.disableOnInteraction?e.autoplay.stop():e.autoplay.pause(),["transitionend","webkitTransitionEnd"].forEach((function(t){e.$wrapperEl[0].removeEventListener(t,e.autoplay.onTransitionEnd)}))},onMouseLeave:function(){var e=this;e.params.autoplay.disableOnInteraction||(e.autoplay.paused=!1,e.autoplay.run())},attachMouseEvents:function(){var e=this;e.params.autoplay.pauseOnMouseEnter&&(e.$el.on("mouseenter",e.autoplay.onMouseEnter),e.$el.on("mouseleave",e.autoplay.onMouseLeave))},detachMouseEvents:function(){var e=this;e.$el.off("mouseenter",e.autoplay.onMouseEnter),e.$el.off("mouseleave",e.autoplay.onMouseLeave)}},te={setTranslate:function(){for(var e=this,t=e.slides,a=0;a<t.length;a+=1){var i=e.slides.eq(a),n=-i[0].swiperSlideOffset;e.params.virtualTranslate||(n-=e.translate);var s=0;e.isHorizontal()||(s=n,n=0);var r=e.params.fadeEffect.crossFade?Math.max(1-Math.abs(i[0].progress),0):1+Math.min(Math.max(i[0].progress,-1),0);i.css({opacity:r}).transform("translate3d("+n+"px, "+s+"px, 0px)")}},setTransition:function(e){var t=this,a=t.slides,i=t.$wrapperEl;if(a.transition(e),t.params.virtualTranslate&&0!==e){var n=!1;a.transitionEnd((function(){if(!n&&t&&!t.destroyed){n=!0,t.animating=!1;for(var e=["webkitTransitionEnd","transitionend"],a=0;a<e.length;a+=1)i.trigger(e[a])}}))}}},ae={setTranslate:function(){for(var e=this,t=e.slides,a=e.rtlTranslate,i=0;i<t.length;i+=1){var n=t.eq(i),s=n[0].progress;e.params.flipEffect.limitRotation&&(s=Math.max(Math.min(n[0].progress,1),-1));var r=-180*s,o=0,l=-n[0].swiperSlideOffset,d=0;if(e.isHorizontal()?a&&(r=-r):(d=l,l=0,o=-r,r=0),n[0].style.zIndex=-Math.abs(Math.round(s))+t.length,e.params.flipEffect.slideShadows){var p=e.isHorizontal()?n.find(".swiper-slide-shadow-left"):n.find(".swiper-slide-shadow-top"),u=e.isHorizontal()?n.find(".swiper-slide-shadow-right"):n.find(".swiper-slide-shadow-bottom");0===p.length&&(p=m('<div class="swiper-slide-shadow-'+(e.isHorizontal()?"left":"top")+'"></div>'),n.append(p)),0===u.length&&(u=m('<div class="swiper-slide-shadow-'+(e.isHorizontal()?"right":"bottom")+'"></div>'),n.append(u)),p.length&&(p[0].style.opacity=Math.max(-s,0)),u.length&&(u[0].style.opacity=Math.max(s,0))}n.transform("translate3d("+l+"px, "+d+"px, 0px) rotateX("+o+"deg) rotateY("+r+"deg)")}},setTransition:function(e){var t=this,a=t.slides,i=t.activeIndex,n=t.$wrapperEl;if(a.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),t.params.virtualTranslate&&0!==e){var s=!1;a.eq(i).transitionEnd((function(){if(!s&&t&&!t.destroyed){s=!0,t.animating=!1;for(var e=["webkitTransitionEnd","transitionend"],a=0;a<e.length;a+=1)n.trigger(e[a])}}))}}},ie={init:function(){var e=this,t=e.params.thumbs;if(e.thumbs.initialized)return!1;e.thumbs.initialized=!0;var a=e.constructor;return t.swiper instanceof a?(e.thumbs.swiper=t.swiper,S(e.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),S(e.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1})):x(t.swiper)&&(e.thumbs.swiper=new a(S({},t.swiper,{watchSlidesVisibility:!0,watchSlidesProgress:!0,slideToClickedSlide:!1})),e.thumbs.swiperCreated=!0),e.thumbs.swiper.$el.addClass(e.params.thumbs.thumbsContainerClass),e.thumbs.swiper.on("tap",e.thumbs.onThumbClick),!0},onThumbClick:function(){var e=this,t=e.thumbs.swiper;if(t){var a=t.clickedIndex,i=t.clickedSlide;if(!(i&&m(i).hasClass(e.params.thumbs.slideThumbActiveClass)||null==a)){var n;if(n=t.params.loop?parseInt(m(t.clickedSlide).attr("data-swiper-slide-index"),10):a,e.params.loop){var s=e.activeIndex;e.slides.eq(s).hasClass(e.params.slideDuplicateClass)&&(e.loopFix(),e._clientLeft=e.$wrapperEl[0].clientLeft,s=e.activeIndex);var r=e.slides.eq(s).prevAll('[data-swiper-slide-index="'+n+'"]').eq(0).index(),o=e.slides.eq(s).nextAll('[data-swiper-slide-index="'+n+'"]').eq(0).index();n=void 0===r?o:void 0===o?r:o-s<s-r?o:r}e.slideTo(n)}}},update:function(e){var t=this,a=t.thumbs.swiper;if(a){var i="auto"===a.params.slidesPerView?a.slidesPerViewDynamic():a.params.slidesPerView,n=t.params.thumbs.autoScrollOffset,s=n&&!a.params.loop;if(t.realIndex!==a.realIndex||s){var r,o,l=a.activeIndex;if(a.params.loop){a.slides.eq(l).hasClass(a.params.slideDuplicateClass)&&(a.loopFix(),a._clientLeft=a.$wrapperEl[0].clientLeft,l=a.activeIndex);var d=a.slides.eq(l).prevAll('[data-swiper-slide-index="'+t.realIndex+'"]').eq(0).index(),p=a.slides.eq(l).nextAll('[data-swiper-slide-index="'+t.realIndex+'"]').eq(0).index();r=void 0===d?p:void 0===p?d:p-l==l-d?a.params.slidesPerGroup>1?p:l:p-l<l-d?p:d,o=t.activeIndex>t.previousIndex?"next":"prev"}else o=(r=t.realIndex)>t.previousIndex?"next":"prev";s&&(r+="next"===o?n:-1*n),a.visibleSlidesIndexes&&a.visibleSlidesIndexes.indexOf(r)<0&&(a.params.centeredSlides?r=r>l?r-Math.floor(i/2)+1:r+Math.floor(i/2)-1:r>l&&a.params.slidesPerGroup,a.slideTo(r,e?0:void 0))}var u=1,c=t.params.thumbs.slideThumbActiveClass;if(t.params.slidesPerView>1&&!t.params.centeredSlides&&(u=t.params.slidesPerView),t.params.thumbs.multipleActiveThumbs||(u=1),u=Math.floor(u),a.slides.removeClass(c),a.params.loop||a.params.virtual&&a.params.virtual.enabled)for(var v=0;v<u;v+=1)a.$wrapperEl.children('[data-swiper-slide-index="'+(t.realIndex+v)+'"]').addClass(c);else for(var f=0;f<u;f+=1)a.slides.eq(t.realIndex+f).addClass(c)}}},ne=[{name:"keyboard",params:{keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}},create:function(){M(this,{keyboard:t({enabled:!1},X)})},on:{init:function(e){e.params.keyboard.enabled&&e.keyboard.enable()},destroy:function(e){e.keyboard.enabled&&e.keyboard.disable()}}},{name:"navigation",params:{navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock"}},create:function(){M(this,{navigation:t({},Y)})},on:{init:function(e){e.navigation.init(),e.navigation.update()},toEdge:function(e){e.navigation.update()},fromEdge:function(e){e.navigation.update()},destroy:function(e){e.navigation.destroy()},"enable disable":function(e){var t=e.navigation,a=t.$nextEl,i=t.$prevEl;a&&a[e.enabled?"removeClass":"addClass"](e.params.navigation.lockClass),i&&i[e.enabled?"removeClass":"addClass"](e.params.navigation.lockClass)},click:function(e,t){var a=e.navigation,i=a.$nextEl,n=a.$prevEl,s=t.target;if(e.params.navigation.hideOnClick&&!m(s).is(n)&&!m(s).is(i)){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===s||e.pagination.el.contains(s)))return;var r;i?r=i.hasClass(e.params.navigation.hiddenClass):n&&(r=n.hasClass(e.params.navigation.hiddenClass)),!0===r?e.emit("navigationShow"):e.emit("navigationHide"),i&&i.toggleClass(e.params.navigation.hiddenClass),n&&n.toggleClass(e.params.navigation.hiddenClass)}}}},{name:"pagination",params:{pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:function(e){return e},formatFractionTotal:function(e){return e},bulletClass:"swiper-pagination-bullet",bulletActiveClass:"swiper-pagination-bullet-active",modifierClass:"swiper-pagination-",currentClass:"swiper-pagination-current",totalClass:"swiper-pagination-total",hiddenClass:"swiper-pagination-hidden",progressbarFillClass:"swiper-pagination-progressbar-fill",progressbarOppositeClass:"swiper-pagination-progressbar-opposite",clickableClass:"swiper-pagination-clickable",lockClass:"swiper-pagination-lock"}},create:function(){M(this,{pagination:t({dynamicBulletIndex:0},U)})},on:{init:function(e){e.pagination.init(),e.pagination.render(),e.pagination.update()},activeIndexChange:function(e){(e.params.loop||void 0===e.snapIndex)&&e.pagination.update()},snapIndexChange:function(e){e.params.loop||e.pagination.update()},slidesLengthChange:function(e){e.params.loop&&(e.pagination.render(),e.pagination.update())},snapGridLengthChange:function(e){e.params.loop||(e.pagination.render(),e.pagination.update())},destroy:function(e){e.pagination.destroy()},"enable disable":function(e){var t=e.pagination.$el;t&&t[e.enabled?"removeClass":"addClass"](e.params.pagination.lockClass)},click:function(e,t){var a=t.target;if(e.params.pagination.el&&e.params.pagination.hideOnClick&&e.pagination.$el.length>0&&!m(a).hasClass(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&a===e.navigation.nextEl||e.navigation.prevEl&&a===e.navigation.prevEl))return;!0===e.pagination.$el.hasClass(e.params.pagination.hiddenClass)?e.emit("paginationShow"):e.emit("paginationHide"),e.pagination.$el.toggleClass(e.params.pagination.hiddenClass)}}}},{name:"lazy",params:{lazy:{checkInView:!1,enabled:!1,loadPrevNext:!1,loadPrevNextAmount:1,loadOnTransitionStart:!1,scrollingElement:"",elementClass:"swiper-lazy",loadingClass:"swiper-lazy-loading",loadedClass:"swiper-lazy-loaded",preloaderClass:"swiper-lazy-preloader"}},create:function(){M(this,{lazy:t({initialImageLoaded:!1},K)})},on:{beforeInit:function(e){e.params.lazy.enabled&&e.params.preloadImages&&(e.params.preloadImages=!1)},init:function(e){e.params.lazy.enabled&&!e.params.loop&&0===e.params.initialSlide&&(e.params.lazy.checkInView?e.lazy.checkInViewOnLoad():e.lazy.load())},scroll:function(e){e.params.freeMode&&!e.params.freeModeSticky&&e.lazy.load()},"scrollbarDragMove resize _freeModeNoMomentumRelease":function(e){e.params.lazy.enabled&&e.lazy.load()},transitionStart:function(e){e.params.lazy.enabled&&(e.params.lazy.loadOnTransitionStart||!e.params.lazy.loadOnTransitionStart&&!e.lazy.initialImageLoaded)&&e.lazy.load()},transitionEnd:function(e){e.params.lazy.enabled&&!e.params.lazy.loadOnTransitionStart&&e.lazy.load()},slideChange:function(e){e.params.lazy.enabled&&e.params.cssMode&&e.lazy.load()}}},{name:"controller",params:{controller:{control:void 0,inverse:!1,by:"slide"}},create:function(){M(this,{controller:t({control:this.params.controller.control},J)})},on:{update:function(e){e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},resize:function(e){e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},observerUpdate:function(e){e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},setTranslate:function(e,t,a){e.controller.control&&e.controller.setTranslate(t,a)},setTransition:function(e,t,a){e.controller.control&&e.controller.setTransition(t,a)}}},{name:"a11y",params:{a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,itemRoleDescriptionMessage:null,slideRole:"group"}},create:function(){M(this,{a11y:t({},Q,{liveRegion:m('<span class="'+this.params.a11y.notificationClass+'" aria-live="assertive" aria-atomic="true"></span>')})})},on:{afterInit:function(e){e.params.a11y.enabled&&(e.a11y.init(),e.a11y.updateNavigation())},toEdge:function(e){e.params.a11y.enabled&&e.a11y.updateNavigation()},fromEdge:function(e){e.params.a11y.enabled&&e.a11y.updateNavigation()},paginationUpdate:function(e){e.params.a11y.enabled&&e.a11y.updatePagination()},destroy:function(e){e.params.a11y.enabled&&e.a11y.destroy()}}},{name:"hash-navigation",params:{hashNavigation:{enabled:!1,replaceState:!1,watchState:!1}},create:function(){M(this,{hashNavigation:t({initialized:!1},Z)})},on:{init:function(e){e.params.hashNavigation.enabled&&e.hashNavigation.init()},destroy:function(e){e.params.hashNavigation.enabled&&e.hashNavigation.destroy()},"transitionEnd _freeModeNoMomentumRelease":function(e){e.hashNavigation.initialized&&e.hashNavigation.setHash()},slideChange:function(e){e.hashNavigation.initialized&&e.params.cssMode&&e.hashNavigation.setHash()}}},{name:"autoplay",params:{autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}},create:function(){M(this,{autoplay:t({},ee,{running:!1,paused:!1})})},on:{init:function(e){e.params.autoplay.enabled&&(e.autoplay.start(),s().addEventListener("visibilitychange",e.autoplay.onVisibilityChange),e.autoplay.attachMouseEvents())},beforeTransitionStart:function(e,t,a){e.autoplay.running&&(a||!e.params.autoplay.disableOnInteraction?e.autoplay.pause(t):e.autoplay.stop())},sliderFirstMove:function(e){e.autoplay.running&&(e.params.autoplay.disableOnInteraction?e.autoplay.stop():e.autoplay.pause())},touchEnd:function(e){e.params.cssMode&&e.autoplay.paused&&!e.params.autoplay.disableOnInteraction&&e.autoplay.run()},destroy:function(e){e.autoplay.detachMouseEvents(),e.autoplay.running&&e.autoplay.stop(),s().removeEventListener("visibilitychange",e.autoplay.onVisibilityChange)}}},{name:"effect-fade",params:{fadeEffect:{crossFade:!1}},create:function(){M(this,{fadeEffect:t({},te)})},on:{beforeInit:function(e){if("fade"===e.params.effect){e.classNames.push(e.params.containerModifierClass+"fade");var t={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!0};S(e.params,t),S(e.originalParams,t)}},setTranslate:function(e){"fade"===e.params.effect&&e.fadeEffect.setTranslate()},setTransition:function(e,t){"fade"===e.params.effect&&e.fadeEffect.setTransition(t)}}},{name:"effect-flip",params:{flipEffect:{slideShadows:!0,limitRotation:!0}},create:function(){M(this,{flipEffect:t({},ae)})},on:{beforeInit:function(e){if("flip"===e.params.effect){e.classNames.push(e.params.containerModifierClass+"flip"),e.classNames.push(e.params.containerModifierClass+"3d");var t={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!0};S(e.params,t),S(e.originalParams,t)}},setTranslate:function(e){"flip"===e.params.effect&&e.flipEffect.setTranslate()},setTransition:function(e,t){"flip"===e.params.effect&&e.flipEffect.setTransition(t)}}},{name:"thumbs",params:{thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-container-thumbs"}},create:function(){M(this,{thumbs:t({swiper:null,initialized:!1},ie)})},on:{beforeInit:function(e){var t=e.params.thumbs;t&&t.swiper&&(e.thumbs.init(),e.thumbs.update(!0))},slideChange:function(e){e.thumbs.swiper&&e.thumbs.update()},update:function(e){e.thumbs.swiper&&e.thumbs.update()},resize:function(e){e.thumbs.swiper&&e.thumbs.update()},observerUpdate:function(e){e.thumbs.swiper&&e.thumbs.update()},setTransition:function(e,t){var a=e.thumbs.swiper;a&&a.setTransition(t)},beforeDestroy:function(e){var t=e.thumbs.swiper;t&&e.thumbs.swiperCreated&&t&&t.destroy()}}}];return W.use(ne),W}));
//# sourceMappingURL=swiper-bundle.min.js.map