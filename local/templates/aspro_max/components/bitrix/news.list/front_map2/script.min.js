$(document).ready((function(){$(document).on("click",".block_container .items .item.initied",(function(){var _this=$(this),itemID=_this.data("id"),animationTime=200;_this.closest(".items").fadeOut(200,(function(){_this.closest(".block_container").find(".detail_items").fadeIn(200),_this.closest(".block_container").find(".detail_items .item[data-id="+itemID+"]").fadeIn(200),console.log(_this.closest(".block_container").find(".detail_items .item[data-id="+itemID+"]"));var arCoordinates=_this.data("coordinates").split(",");void 0!==typeof map&&map.setCenter([arCoordinates[0],arCoordinates[1]],15)}))})),$(document).on("click",".block_container .top-close",(function(){var _this=$(this).closest(".block_container").find(".detail_items .item:visible"),animationTime=200;_this.fadeOut(200),_this.closest(".block_container").find(".detail_items").fadeOut(200,(function(){_this.closest(".block_container").find(".items").fadeIn(200),void 0!==typeof map&&void 0!==typeof clusterer&&map.setBounds(clusterer.getBounds(),{zoomMargin:40})}))}))}));
//# sourceMappingURL=script.min.js.map