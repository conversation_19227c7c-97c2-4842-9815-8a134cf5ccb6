$(document).ready((function(){$(".lookbooks .flexslider .js-click").click((function(){var _this=$(this),block="",activeBlock=_this.closest(".lookbook-wrapper").find(".lookbook.lookbook--active");_this.hasClass("flex-prev")?activeBlock.fadeOut((function(){$(this).removeClass("lookbook--active"),console.log(activeBlock),(block=activeBlock.prev(".lookbook")).length?block.fadeIn((function(){$(this).addClass("lookbook--active")})):_this.closest(".lookbook-wrapper").find(".lookbook:last-of-type").fadeIn((function(){$(this).addClass("lookbook--active")}))})):activeBlock.fadeOut((function(){$(this).removeClass("lookbook--active"),(block=activeBlock.next(".lookbook")).length?block.fadeIn((function(){$(this).addClass("lookbook--active")})):_this.closest(".lookbook-wrapper").find(".lookbook:eq(0)").fadeIn((function(){$(this).addClass("lookbook--active")}))}))}));var slideText=$(".lookbook__info-toggle:first").text();$(".lookbook__info-toggle").click((function(){var _this=$(this),slideBlock=_this.closest(".lookbook__info").find(".lookbook__info-text-more");slideBlock.is(":visible")?(_this.text(slideText),slideBlock.parent().removeClass("clicked")):(_this.text(_this.data("hide")),slideBlock.parent().addClass("clicked")),slideBlock.slideToggle()})),$(".lookbooks .tabs-wrapper").scrollTab({tabs_wrapper:"ul.tabs",arrows_css:{top:"-1px"},onResize:function(options){var top_wrapper=options.scrollTab.closest(".top_block");if(top_wrapper.length){var tabs_wrapper=top_wrapper.find(".right_block_wrapper .tabs-wrapper");if(window.matchMedia("(max-width: 767px)").matches)return tabs_wrapper.css({width:"100%","max-width":""}),!0;var title=top_wrapper.find("h3"),right_link=top_wrapper.find(".right_block_wrapper > a"),all_width=top_wrapper[0].getBoundingClientRect().width;title.length&&(all_width-=title.outerWidth(!0)),right_link.length&&(all_width-=right_link.outerWidth(!0)),all_width-=Number.parseInt(tabs_wrapper.css("margin-right")),tabs_wrapper.css({"max-width":all_width,width:""})}options.width=all_width}})})),BX.addCustomEvent(window,"clickedTabsLi",(function(e){$(".lookbook-wrapper").length&&!e.target.hasClass("clicked")&&($(".lookbook-wrapper").hide(),$(".lookbook-wrapper:eq("+e.index+")").show())}));
//# sourceMappingURL=script.min.js.map