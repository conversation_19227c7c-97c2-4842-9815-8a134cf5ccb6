BX.namespace("BX.Sale.PersonalOrderComponent");(function(){BX.Sale.PersonalOrderComponent.PersonalOrderDetail={init:function(e){var t=document.getElementsByClassName("sale-order-detail-about-order-inner-container-name-read-more")[0];var a=document.getElementsByClassName("sale-order-detail-about-order-inner-container-name-read-less")[0];var n=document.getElementsByClassName("sale-order-detail-about-order-inner-container-details")[0];var l=document.getElementsByClassName("sale-order-detail-payment-options-shipment");var s=document.getElementsByClassName("sale-order-detail-payment-options-methods");var i=document.getElementsByClassName("sale-order-detail-shipment-id");if(i[0]){Array.prototype.forEach.call(i,function(e){var t=e.parentNode.getElementsByClassName("sale-order-detail-shipment-id-icon")[0];if(t){BX.clipboard.bindCopyClick(t,{text:e.innerHTML})}})}BX.bind(t,"click",function(){n.style.display="inline-block";t.style.display="none";a.style.display="inline-block"},this);BX.bind(a,"click",function(){n.style.display="none";t.style.display="inline-block";a.style.display="none"},this);Array.prototype.forEach.call(l,function(e){var t=e.getElementsByClassName("sale-order-detail-payment-options-shipment-composition-map")[0];var a=e.getElementsByClassName("sale-order-detail-show-link")[0];var n=e.getElementsByClassName("sale-order-detail-hide-link")[0];BX.bindDelegate(e,"click",{class:"sale-order-detail-show-link"},BX.proxy(function(){a.style.display="none";n.style.display="inline-block";t.style.display="block"},this));BX.bindDelegate(e,"click",{class:"sale-order-detail-hide-link"},BX.proxy(function(){a.style.display="inline-block";n.style.display="none";t.style.display="none"},this))});Array.prototype.forEach.call(s,function(t){var a=t.getElementsByClassName("sale-order-detail-payment-options-methods-info")[0];BX.bindDelegate(t,"click",{class:"active-button"},BX.proxy(function(){BX.toggleClass(t,"sale-order-detail-active-event")},this));BX.bindDelegate(a,"click",{class:"sale-order-detail-payment-options-methods-info-change-link"},BX.proxy(function(t){t.preventDefault();var n=a.parentNode.getElementsByClassName("sale-order-detail-payment-options-methods-button-container")[0];var l=a.parentNode.getElementsByClassName("sale-order-detail-payment-inner-row-template")[0];BX.ajax({method:"POST",dataType:"html",url:e.url,data:{sessid:BX.bitrix_sessid(),orderData:e.paymentList[t.target.id],templateName:e.templateName,returnUrl:e.returnUrl},onsuccess:BX.proxy(function(e){a.innerHTML=e;if(n){n.parentNode.removeChild(n)}l.style.display="block";BX.bind(l,"click",function(){window.location.reload()},this)},this),onfailure:BX.proxy(function(){return this},this)},this)},this))})}}})();
//# sourceMappingURL=script.map.js