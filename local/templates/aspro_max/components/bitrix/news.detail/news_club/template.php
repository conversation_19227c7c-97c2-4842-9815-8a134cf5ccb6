<?if(!defined('B_PROLOG_INCLUDED') || B_PROLOG_INCLUDED !== true) die();?>

<?$this->setFrameMode(true);?>
<?use \Bitrix\Main\Localization\Loc;
use DmitrievLab\Aspro\CMaxCustom; ?>



<?// form question?>
<?
$bActiveDate = strlen($arResult['DISPLAY_PROPERTIES']['PERIOD']['VALUE']) || ($arResult['DISPLAY_ACTIVE_FROM'] && in_array('DATE_ACTIVE_FROM', $arParams['FIELD_CODE']));
$bDiscountCounter = ($arResult['ACTIVE_TO'] && in_array('ACTIVE_TO', $arParams['FIELD_CODE']));
$bShowDopBlock = ($arResult['DISPLAY_PROPERTIES']['SALE_NUMBER']['VALUE'] || $bDiscountCounter);
$bWideText = isset($arResult['PROPERTIES']['WIDE_TEXT']) && $arResult['PROPERTIES']['WIDE_TEXT']['VALUE_XML_ID'] == 'YES';

$bShowFormOrder = ($arResult['DISPLAY_PROPERTIES']['FORM_ORDER']['VALUE_XML_ID'] == 'YES');
$bShowFormQuestion = ($arResult['DISPLAY_PROPERTIES']['FORM_QUESTION']['VALUE_XML_ID'] == 'YES');


if($arResult['FIELDS']['DETAIL_PICTURE']){
	$atrTitle = (strlen($arResult['DETAIL_PICTURE']['DESCRIPTION']) ? $arResult['DETAIL_PICTURE']['DESCRIPTION'] : (strlen($arResult['DETAIL_PICTURE']['TITLE']) ? $arResult['DETAIL_PICTURE']['TITLE'] : $arResult['NAME']));
	$atrAlt = (strlen($arResult['DETAIL_PICTURE']['DESCRIPTION']) ? $arResult['DETAIL_PICTURE']['DESCRIPTION'] : (strlen($arResult['DETAIL_PICTURE']['ALT']) ? $arResult['DETAIL_PICTURE']['ALT'] : $arResult['NAME']));
}


$bTopImage = ($arResult['FIELDS']['DETAIL_PICTURE'] && $arResult['PROPERTIES']['PHOTOPOS']['VALUE_XML_ID'] == 'TOP');

$bPartnersMode = isset($arParams["PARTNERS_MODE"]) && $arParams["PARTNERS_MODE"] == "Y";

?>

<?
/*set array props for component_epilog*/
$templateData = array(
	'DOCUMENTS' => $arResult['DISPLAY_PROPERTIES']['DOCUMENTS']['VALUE'],
	'LINK_SALE' => $arResult['DISPLAY_PROPERTIES']['LINK_SALE']['VALUE'],
	'LINK_BRANDS' => $arResult['DISPLAY_PROPERTIES']['LINK_BRANDS']['VALUE'],
	'LINK_TIZERS' => $arResult['DISPLAY_PROPERTIES']['LINK_TIZERS']['VALUE'],
	'LINK_PROJECTS' => $arResult['DISPLAY_PROPERTIES']['LINK_PROJECTS']['VALUE'],
	'LINK_SERVICES' => $arResult['DISPLAY_PROPERTIES']['LINK_SERVICES']['VALUE'],
	'LINK_GOODS' => $arResult['DISPLAY_PROPERTIES']['LINK_GOODS']['VALUE'],
	'LINK_REVIEWS' => $arResult['DISPLAY_PROPERTIES']['LINK_REVIEWS']['VALUE'],
	'LINK_STAFF' => $arResult['DISPLAY_PROPERTIES']['LINK_STAFF']['VALUE'],
	'LINK_NEWS' => $arResult['DISPLAY_PROPERTIES']['LINK_NEWS']['VALUE'],
	'LINK_VACANCY' => $arResult['DISPLAY_PROPERTIES']['LINK_VACANCY']['VALUE'],
	'LINK_STAFF' => $arResult['DISPLAY_PROPERTIES']['LINK_STAFF']['VALUE'],
	'LINK_REVIEWS' => $arResult['DISPLAY_PROPERTIES']['LINK_REVIEWS']['VALUE'],
	'LINK_BLOG' => $arResult['DISPLAY_PROPERTIES']['LINK_BLOG']['VALUE'],
	'GALLERY_BIG' => $arResult['GALLERY_BIG'],
	'LINK_LANDINGS' => $arResult['DISPLAY_PROPERTIES']['LINK_LANDINGS']['VALUE'],
	'LINK_PARTNERS' => $arResult['DISPLAY_PROPERTIES']['LINK_PARTNERS']['VALUE'],
	'VIDEO' => $arResult["DISPLAY_PROPERTIES"]["VIDEO"]["~VALUE"],
	//'VIDEO_IFRAME' => $arResult['VIDEO_IFRAME'],
	'FORM_QUESTION' => $arResult['DISPLAY_PROPERTIES']['FORM_QUESTION']['VALUE'],
	'FORM_ORDER' => $arResult['DISPLAY_PROPERTIES']['FORM_ORDER']['VALUE'],
	'BNR_DARK_MENU_COLOR' => $arResult['PROPERTIES']['BNR_DARK_MENU_COLOR'],
	'SHOW_PERIOD_LINE' => $bShowDopBlock || $bActiveDate,
	//'CATALOG_LINKED_TEMPLATE' => $catalogLinkedTemplate,
	//'LIST_PAGE_URL' => $arResult['LIST_PAGE_URL'],
	'GALLERY_TYPE' => isset($arResult['PROPERTIES']['GALLERY_TYPE']) ? ($arResult['PROPERTIES']['GALLERY_TYPE']['VALUE'] === 'small' ? 'small' : 'big') : ($arParams['GALLERY_TYPE'] === 'small' ? 'small' : 'big'),
);
?>

<?//need for top banners
if(isset($arResult['PROPERTIES']['BNR_TOP']) && $arResult['PROPERTIES']['BNR_TOP']['VALUE_XML_ID'] == 'YES')
{
	$bBannerChar = isset($arResult['PROPERTIES']['BANNER_CHAR']) && $arResult['PROPERTIES']['BANNER_CHAR']['VALUE_XML_ID'] == 'Y';

	$templateData['SECTION_BNR_CONTENT'] = $bBannerChar ? false : true;
	if(isset($arResult['PROPERTIES']['BNR_ON_HEADER']) && $arResult['PROPERTIES']['BNR_ON_HEADER']['VALUE_XML_ID'] == 'YES' && !$bBannerChar)
	{
		$templateData['BNR_ON_HEAD'] = true;
	}
}

$isMobile = ($arParams["IS_MOBILE"] == "Y") ? true : false ;

//global $USER; if ($USER->IsAdmin()) {echo "<pre>"; print_r($arParams["IS_MOBILE"]); echo "</pre>";}
?>

<?if ($isMobile):?>
<?
	if (!empty ($arResult["PROPERTIES"]["BNR_TOP_BG_MOBILE"]["VALUE"]))
	{
		$arResult["PROPERTIES"]["BNR_TOP_BG"]["VALUE"] = $arResult["PROPERTIES"]["BNR_TOP_BG_MOBILE"]["VALUE"];
	}
?>
<?endif?>

<?// shot top banners start?>
<?$bShowTopBanner = (isset($templateData['SECTION_BNR_CONTENT'] ) && $templateData['SECTION_BNR_CONTENT'] == true);?>
<?if($bShowTopBanner):?>
	<?$this->SetViewTarget("section_bnr_content");?>
<?if ($isMobile):?>
	<div class="mobile-banner">
<?endif?>
<?$arParams['CONTENT_CLASS'] = 'banner_club';?>
		<?CMaxCustom::ShowTopDetailBanner($arResult, $arParams);?>
<?if ($isMobile):?>
	</div>
<?endif?>
	<?$this->EndViewTarget();?>
<?endif;?>
<?// shot top banners end?>


<div class="detail_wrapper detail-news1 bonus-page">
	<?if($bBannerChar): // banner char?>
		<?
		$arBannerChar = array(
			'TEXT' => isset($arResult['PROPERTIES']['BANNER_CHAR_TEXT']) ? $arResult['PROPERTIES']['BANNER_CHAR_TEXT']['VALUE'] : '',
			'GALLERY' => isset($arResult['PROPERTIES']['BANNER_CHAR_PHOTOS']) ? $arResult['PROPERTIES']['BANNER_CHAR_PHOTOS']['VALUE'] : false,
			'BTN' => false,
		);

		$sectionPath = '';
		$res = CIBlockSection::GetNavChain($arParams['IBLOCK_ID'], $arResult['IBLOCK_SECTION_ID'], array('ID', 'NAME'));
		while($section = $res->Fetch()) {
			$sectionPath .= $section['NAME'].($section['ID'] == $arResult['IBLOCK_SECTION_ID'] ? '' : '&nbsp;&nbsp;<span>&mdash;</span>&nbsp;&nbsp;');
		}
		$arBannerChar['SECTION_PATH'] = $sectionPath;

		$arAnchors = array(
			'GOODS' => '.ordered-block.goods, .ordered-block.goods_catalog',
			'PREVIEW' => '.introtext_wrapper',
			'SERVICES' => '.ordered-block.services',
			'NEWS' => '.ordered-block.news',
			'BRANDS' => '.ordered-block.brands',
			'PROJECTS' => '.ordered-block.projects-block',
			'TIZERS' => '.ordered-block.tizers-block',
			'REVIEWS' => '.ordered-block.reviews-block',
			'STAFF' => '.ordered-block.staff-block',
			'DOCS' => '.wraps.docs-block',
			'VACANCY' => '.ordered-block.vacancy',
			'BLOG' => '.ordered-block.blog',
			'PARTNERS' => '.ordered-block.partners',
			'SALE' => '.ordered-block.sale',
			'DETAIL' => '.ordered-block.detail_content_wrapper',
		);
		if( isset($arResult['PROPERTIES']['BANNER_CHAR_BTN_TEXT']) && $arResult['PROPERTIES']['BANNER_CHAR_BTN_TEXT']['VALUE'] ) {
			$arBannerChar['BTN'] = array(
				'TEXT' => $arResult['PROPERTIES']['BANNER_CHAR_BTN_TEXT']['VALUE'],
				'CLASS' => isset($arResult['PROPERTIES']['BANNER_CHAR_BTN_CLASS']) ? $arResult['PROPERTIES']['BANNER_CHAR_BTN_CLASS']['VALUE'] : '',
				'ANCHOR' => isset($arResult['PROPERTIES']['BANNER_CHAR_BTN_ANCHOR']) ? (isset($arAnchors[ $arResult['PROPERTIES']['BANNER_CHAR_BTN_ANCHOR']['VALUE_XML_ID'] ]) ? $arAnchors[ $arResult['PROPERTIES']['BANNER_CHAR_BTN_ANCHOR']['VALUE_XML_ID'] ] : false) : false,
			);
		}
		if($arBannerChar):?>
			<div class="banner-char bordered">
				<div class="row flexbox">
					<?if($arBannerChar['GALLERY']):?>
						<div class="banner-char__gallery col-md-6 col-sm-6 col-xs-12 flexbox align-items-center swipeignore">
							<div class="banner-char__gallery-inner">
								<div class="owl-theme owl-bg-nav short-nav owl-dots owl-carousel owl-drag" data-plugin-options='{"items": "1", "autoplay" : false, "autoplayTimeout" : "3000", "smartSpeed":1000, "dots": true, "nav": true, "loop": false, "rewind":true, "margin": 10}'>
									<?foreach($arBannerChar['GALLERY'] as $photo):?>
										<div class="banner-char__gallery-item">
											<a href="<?=CFile::GetPath($photo)?>" class="fancy" data-fancybox="gallery">
												<img class="banner-char__gallery-item-img" src="<?=CFile::GetPath($photo)?>" />
											</a>
										</div>
									<?endforeach;?>
								</div>
							</div>
						</div>
					<?endif;?>

					<div class="banner-char__info <?=$arBannerChar['GALLERY'] ? '' : 'banner-char__info--alone'?> col-md-6 col-sm-6 col-xs-12">
						<?if($arBannerChar['SECTION_PATH'] || $arBannerChar['TEXT']):?>
							<div class="banner-char__info--top">
								<?if($arBannerChar['SECTION_PATH']):?>
									<div class="banner-char__info-sections font_upper">
										<?=$arBannerChar['SECTION_PATH']?>
									</div>
								<?endif;?>

								<?if($arBannerChar['TEXT']):?>
									<div class="banner-char__info-text darken">
										<?=$arBannerChar['TEXT']?>
									</div>
								<?endif;?>
							</div>
						<?endif;?>

						<?if($arResult['DISPLAY_PROPERTIES_FORMATTED'] || $arBannerChar['BTN'] || $bShowFormQuestion):?>
							<div class="banner-char__info--bottom">
								<?if($arResult['DISPLAY_PROPERTIES_FORMATTED']):?>
									<div class="banner-char__info-props font_xs">
										<?foreach($arResult['DISPLAY_PROPERTIES_FORMATTED'] as $code => $arProp):?>
											<?
											if($arProp['PROPERTY_TYPE'] == 'E' || $arProp['PROPERTY_TYPE'] == 'G')
												continue;
											?>
											<div class="banner-char__info-props-prop">
												<span class="title-prop"><?=$arProp['NAME']?>&nbsp;&nbsp;<span>&mdash;</span>&nbsp;&nbsp;</span>
												<span class="value darken">
													<?if(is_array($arProp['DISPLAY_VALUE'])):?>
														<?foreach($arProp['DISPLAY_VALUE'] as $key => $value):?>
															<?if($arProp['DISPLAY_VALUE'][$key + 1]):?>
																<?=$value.'&nbsp;/ '?>
															<?else:?>
																<?=$value?>
															<?endif;?>
														<?endforeach;?>
													<?else:?>
														<?=$arProp['DISPLAY_VALUE']?>
													<?endif;?>
												</span>
											</div>
										<?endforeach;?>
									</div>
								<?endif;?>

								<?if($arBannerChar['BTN'] || $bShowFormQuestion):?>
									<div class="banner-char__info-buttons flexbox flexbox--row flex-wrap">
										<?if($arBannerChar['BTN']):?>
											<div class="banner-char__info-buttons-btn">
												<span class="btn <?=($arBannerChar['BTN']['CLASS'] ? $arBannerChar['BTN']['CLASS'] : "btn-default");?>" data-scroll-block="<?=($arBannerChar['BTN']['ANCHOR'] ? $arBannerChar['BTN']['ANCHOR'] : $arAnchors['GOODS']);?>"><?=$arBannerChar['BTN']['TEXT'];?></span>
											</div>
										<?endif;?>

										<?if($bShowFormQuestion):?>
											<div class="banner-char__info-buttons-question">
												<span class="btn btn-transparent-border-color  animate-load" data-event="jqm" data-param-form_id="ASK" data-name="ASK" data-autoload-product_name="<?=CMax::formatJsName($arResult['NAME']);?>">
													<?=(strlen($arParams['S_ASK_QUESTION']) ? $arParams['S_ASK_QUESTION'] : Loc::getMessage('S_ASK_QUESTION'))?>
												</span>
											</div>
										<?endif;?>
									</div>
								<?endif;?>
							</div>
						<?endif;?>
					</div>
				</div>
			</div>
		<?endif;?>
	<?endif;?>


	<? if (!empty($arResult['PROPERTIES']['BLOCK_0']['~VALUE']['TEXT'])) { ?>
		<?= $arResult['PROPERTIES']['BLOCK_0']['~VALUE']['TEXT'] ?>
	<? } ?>

	<div class="trigger-bonus">
		<?
		$t = 1;
		$ar = [1, 4, 7, 10, 13];
		$tr = [2, 3, 5, 6, 8, 9, 11, 12];
		while ($t <= 13) { ?>
			<? if (!empty($arResult['PROPERTIES']['BLOCK_' . $t . '_TEXT']['~VALUE']['TEXT']) && in_array($t, $ar)) { ?>
				<div class="row">
					<div class="col-md-12">
						<div class="trigger-bonus-item">
							<div class="trigger-bonus-img">
								<?= $arResult['PROPERTIES']['BLOCK_' . $t . '_ICON']['~VALUE']['TEXT'] ?>
							</div>
							<?= $arResult['PROPERTIES']['BLOCK_' . $t . '_TEXT']['~VALUE']['TEXT'] ?>
						</div>
					</div>
				</div>
			<? } ?>
			<? if ($t == 2 || $t == 5 || $t == 8 || $t == 11) { ?>
				<div class="row">
				<? } ?>
				<? if ($t == 2 || $t == 5 || $t == 8 || $t == 11) { ?>
					<? if (!empty($arResult['PROPERTIES']['BLOCK_' . $t . '_TEXT']['~VALUE']['TEXT'])) { ?>
						<div class="col-md-6">
							<div class="trigger-bonus-item">
								<div class="trigger-bonus-img">
									<?= $arResult['PROPERTIES']['BLOCK_' . $t . '_ICON']['~VALUE']['TEXT'] ?>
								</div>
								<?= $arResult['PROPERTIES']['BLOCK_' . $t . '_TEXT']['~VALUE']['TEXT'] ?>
							</div>
						</div>
					<? } ?>
				<? } ?>
				<? if ($t == 3 || $t == 6 || $t == 9 || $t == 12) { ?>
					<? if (!empty($arResult['PROPERTIES']['BLOCK_' . $t . '_TEXT']['~VALUE']['TEXT'])) { ?>
						<div class="col-md-6">
							<div class="trigger-bonus-item">
								<div class="trigger-bonus-img">
									<?= $arResult['PROPERTIES']['BLOCK_' . $t . '_ICON']['~VALUE']['TEXT'] ?>
								</div>
								<?= $arResult['PROPERTIES']['BLOCK_' . $t . '_TEXT']['~VALUE']['TEXT'] ?>
							</div>
						</div>
					<? } ?>
				<? } ?>
				<? if ($t == 3 || $t == 6 || $t == 9 || $t == 12) { ?>
				</div>
			<? } ?>
		<?
			$t++;
		}
		?>



	</div>