body .wrapper1 .wrapper_inner.front .drag-block.container .front_company .maxwidth-theme.wide {
  padding-top: 0px;
}
.content_wrapper_block.front_company > .maxwidth-theme {
  padding-top: 0 !important;
}
.item-views.company .item .content_wrapper_block {
  border: none;
}
body .wrapper_inner.front .drag-block.container .item-views.company .item .maxwidth-theme {
  padding: 0px;
}

.item-views.company .text-block .item {
  display: table;
  width: 100%;
}
.item-views.company .text-block .item .item-inner {
  display: table-cell;
  vertical-align: middle;
}
.item-views.company .text-block .item .item-inner h3 {
  margin: 20px 0px 32px;
}
.item-views.company.bg .text-block h3 {
  color: #333;
}
.item-views.company .text-block .item .item-inner .preview-text {
  padding: 0px 0px 28px 0px;
}
.item-views.company .text-block .item .item-inner .buttons {
  margin-bottom: 12px;
}

.item-views.company .text {
  padding: 32px 30px 32px 30px;
  position: relative;
}
.item-views.company .flexbox:not(.flex-direction-row-reverse) .text,
.flexbox.flex-direction-row-reverse .item.video-block {
  margin: 0px 0px 0px auto;
}
.maxwidth-theme:not(.wide) .item-views.company .text {
  padding-left: 0px;
}

.item-views.company .text.with-benefit {
  padding-bottom: 12px;
}
body .wrapper1 .drag-block .item-views.company .text.with-benefit .item-views.tizers {
  padding: 0px;
}

.item-views.company .item.video-block .image {
  height: 100%;
  background-repeat: no-repeat;
  background-position: top center;
  position: relative;
  background-size: cover;
}

.lg.item-views.company .item.video-block .image {
  background-size: cover;
  padding-top: 59.7%;
}

.with-padding.item-views.company {
  background-position: center bottom;
  background-repeat: no-repeat;
  background-size: cover;
}
.with-padding.item-views.company .image {
  background-position: bottom left;
}

.video-block .image .play .fancy {
  opacity: 0;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: block !important;
  z-index: 2;
}

.bg.item-views.company .flexbox.flex-direction-row-reverse .item.video-block .image {
  background-position: bottom center;
}

.item-views.company.type2:not(.sm) .item.video-block {
  padding-bottom: 60px;
}
.item-views.company.type2 .item.video-block .image {
  min-height: auto;
}

.item-views.company.type2.md .item.video-block .image {
  width: 320px;
  height: 320px;
}

.item-views.company.type2.sm .item.video-block .image {
  height: 240px;
  width: 240px;
}

.item-views.company.type2:not(.no-img) .item .with-benefit .item {
  padding-bottom: 21px;
}

.item-views.company.type2.sm.no-img .item.video-block .with-text-block-wrapper h3 {
  margin-top: 16px;
}
.item-views.company.type2.sm.no-img .text.with-benefit .item-views.tizers {
  padding-top: 8px;
}
.item-views.company.type2.sm.no-img .text.with-benefit {
  padding-bottom: 0px;
}

.fancybox-container iframe#company_video_iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.item-views.company .preview-text > p:last-of-type {
  margin-bottom: 0;
}

.item-views.company.company_light_text .item-inner .text,
.item-views.company.company_light_text .item-inner .text h3,
.item-views.company.company_light_text .item-inner .text .show_all,
.item-views.company.company_light_text .item-inner .text .preview-text {
  color: #fff;
}
.video-block .video-block__iframe{
  display: none;
}
@media (min-width: 1301px) and (max-width: 1400px) {
  .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.sm .text-block .item {
    padding-left: 25px;
  }
  .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.md .text-block .item {
    padding-left: 25px;
  }
}

@media (min-width: 992px) and (max-width: 1300px) {
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.md .text.with-benefit .buttons {
    right: 0;
  }
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.md .text.with-benefit .title {
    font-size: 1em;
  }
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.md .text.with-benefit {
    padding-left: 0;
    padding-right: 0;
  }
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.md .item.video-block {
    padding-top: 60px;
    padding-bottom: 0;
  }

  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.md .text.with-benefit {
    padding-top: 32px;
  }

  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.sm > .company-block > .row.flexbox > div:first-of-type {
    width: 100%;
  }
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.sm > .company-block > .row.flexbox > div:last-of-type {
    width: 100%;
  }
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.sm .item.video-block .image {
    margin-left: 0;
  }
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2 .company-block > .row.flexbox {
    flex-direction: column-reverse;
    -webkit-flex-direction: column-reverse;
  }
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.sm .item.video-block {
    padding-top: 60px;
    padding-bottom: 0px;
  }
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.sm.no-img .text.with-benefit {
    padding-left: 0;
  }
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.md .company-block > .row.flexbox {
    flex-direction: column-reverse;
    -webkit-flex-direction: column-reverse;
  }
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.md .company-block > .row > div {
    width: 100%;
  }
  body .wrapper1.sticky_menu:not(.sm) .item-views.company.type2.md .item.video-block .image {
    margin-left: 0;
  }
  body .wrapper1.sticky_menu:not(.sm) .drag-block .item-views.company.type2.sm.no-img .item.video-block {
    padding-top: 60px;
    padding-bottom: 0px;
  }
}

@media (min-width: 1200px) {
  .maxwidth-theme.wide .item-views.company.md .item.video-block .image {
    background-size: auto;
  }
}

@media (max-width: 1199px) {
  .item-views.company.type2.md .item.video-block .image {
    width: 280px;
    height: 280px;
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .item-views.company.type2.sm .item.video-block .image {
    height: 200px;
    width: 200px;
  }
  .item-views.company.type2.sm .item.video-block .image .play:after {
    margin: -35px 0 0 -35px;
    width: 70px;
    height: 70px;
  }
}

@media (min-width: 992px) {
  .item-views.company > .company-block > .row:not(.flex-direction-row-reverse) > div:not(.text-block) {
    padding-left: 0px;
  }
  .item-views.company:not(.type2) .text,
  .with-padding.item-views.company:not(.type2) .item.video-block {
    max-width: 687px;
  }
  .item-views.company.type2.md .text {
    max-width: none;
    padding-right: 0;
  }
  .item-views.company.type2.sm .text {
    max-width: none;
    padding-right: 0;
  }

  .with-padding.company .item.video-block {
    padding-top: 60px;
  }
  .wrapper1:not(.with_left_block) .drag-block .with-padding.company .item.video-block {
    padding-top: 70px;
  }

  .with-padding.company .item.video-block .image {
    min-height: 440px;
  }

  /*.item-views.company.type2 .item.video-block .image{margin-right: auto;}*/
  .item-views.company.type2 .item.video-block .image {
    margin-right: auto;
    margin-left: auto;
  }
  /*.item-views.company.type2.sm > .company-block > .row.flexbox > div:first-of-type{width: 71%;}*/
  /*.item-views.company.type2.sm > .company-block > .row.flexbox > div:last-of-type{width: 29%;}*/

  .item-views.company.type2.sm .item.video-block {
    padding-top: 70px;
    padding-bottom: 70px;
  }
  .wrapper1:not(.with_left_block) .drag-block .item-views.company.type2.sm .item.video-block {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  /*.item-views.company.type2.sm.no-img .text.with-benefit{padding-left: 65px;}*/

  /*.item-views.company.type2.sm.no-img > .company-block > .row.flexbox .with-text-block-wrapper{margin-left: auto; max-width: 250px;}*/
  /*.item-views.company.type2.sm.no-img > .company-block > .row.flexbox .with-text-block-wrapper{max-width: 250px;}*/
  .item-views.company.type2.sm.no-img > .company-block > .row.flexbox .with-text-block-wrapper {
    overflow: hidden;
  }

  .item-views.company.type2.sm.no-img .item.video-block {
    padding-top: 57px;
    padding-bottom: 81px;
    padding-right: 0px;
  }
  .wrapper1:not(.with_left_block) .drag-block .item-views.company.type2.sm.no-img .item.video-block {
    padding-top: 67px;
    padding-bottom: 91px;
  }

  .item-views.company:not(.with-padding):not(.bg) .text {
    padding-right: 70px;
  }

  /*.item-views.company.type2.md .item.video-block .image{margin: 0 auto;}*/
  /*.item-views.company.type2.sm .item.video-block .image{margin: 0 auto;}*/
}
@media (max-width: 991px) {
  .with-padding.company.item-views:not(.type2) .item.video-block .image {
    padding-top: 60%;
    background-size: contain;
  }
  .item-views.company.type2.sm.no-img .item.video-block {
    padding-left: 32px;
    padding-right: 32px;
  }
  .item-views.company.type2.sm .item.video-block {
    padding-bottom: 70px;
  }
  .item-views.company .text.with-benefit .js-tizers-tmp .maxwidth-theme {
    padding: 0px;
  }

  .item-views.company.type2.sm.no-img .item.video-block {
    padding-left: 0;
    padding-right: 0;
  }

  .item-views.company.type2:not(.sm) .item.video-block,
  .item-views.company.type2 .item.video-block,
  .item-views.company.type2.sm .item.video-block {
    padding-top: 60px;
    padding-bottom: 0;
  }

  .item-views.company.md:not(.type2) .text-block .item .text,
  .item-views.company.bg .text-block .item .text {
    padding-top: 60px;
    padding-bottom: 0;
  }

  body .wrapper1:not(.with_left_block) .drag-block .item-views.company.type2.md .text.with-benefit {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .item-views.company.lg .company-block > .row.flexbox,
  .item-views.company.type2 .company-block > .row.flexbox {
    flex-direction: column-reverse;
    -webkit-flex-direction: column-reverse;
  }

  .item-views.company.type2.md .text-block {
    width: 100%;
  }
  .item-views.company.type2.md .image-block {
    width: 100%;
  }
  .item-views.company.type2.sm .text-block {
    width: 100%;
  }
  .item-views.company.type2.sm .image-block {
    width: 100%;
  }

  .item-views.company.md:not(.type2) .image-block,
  .item-views.company.bg .image-block {
    margin-top: 20px;
  }
}
@media (min-width: 601px) {
  .item-views.company .item-views.tizers .item-wrapper > .item .pull-left + .inner-text {
    padding-left: 60px;
  }
  .item-views.company .item-views.tizers .item-wrapper > .item .pull-right + .inner-text {
    padding-right: 60px;
  }

  .item-views.company.type2.md .text.with-benefit {
    padding-top: 50px;
    padding-bottom: 33px;
  }
  .item-views.company .text.with-benefit .buttons {
    position: absolute;
    top: 60px;
    right: 1px;
  }

  /*.wrapper1:not(.with_left_block) .drag-block .item-views.company.type2.md .text.with-benefit{padding-bottom: 43px;padding-top: 60px;}*/
  .wrapper1:not(.with_left_block) .drag-block .item-views.company.type2.md .text.with-benefit {
    padding-bottom: 60px;
    padding-top: 60px;
  }
  .wrapper1:not(.with_left_block) .drag-block .item-views.company .text.with-benefit .buttons {
    top: 70px;
  }

  /*.item-views.company.type2 .item.video-block {padding-right:34px;}*/

  .item-views.company.type2.sm.no-img .text-block .text .buttons {
    display: none;
  }
}

@media (min-width: 768px) {
  .lg.item-views.company .video-block .image .play:before {
    margin: -17px 0px 0px -23px;
  }
  .lg.item-views.company .video-block .image .play:after {
    margin: -47px 0 0 -56px;
  }
}

@media (max-width: 767px) {
  .item-views.company.type2.md .item.video-block .image {
    width: 200px;
    height: 200px;
  }
  .video-block .image .play:after {
    margin: -35px 0 0 -35px;
    width: 70px;
    height: 70px;
  }
}

@media (min-width: 601px) and (max-width: 991px) {
  body .item-views.company.type2.md .text-block .item .item-inner h3 {
    margin-top: 30px;
  }
  body .item-views.company .text-block .item .item-inner h3 {
    margin-right: 25px;
  }
}

@media (max-width: 600px) {
  .item-views.company .text-block .item .item-inner .preview-text {
    padding: 0px 0px 25px 0px;
  }
  body#main .wrapper1 .wrapper_inner.front .drag-block.container .front_company .maxwidth-theme.wide {
    padding-top: 0px;
  }

  body .item-views.company .item-views.tizers .items.tops .item .image + .inner-text {
    padding-left: 0px;
  }

  .item-views.company.type2.md .item.video-block .image {
    height: 270px;
    width: 270px;
  }
  /*.item-views.company .text {padding-right: 0;padding-left: 0;}*/

  .item-views.company.type2:not(.sm) .item.video-block,
  .item-views.company.type2 .item.video-block,
  .item-views.company.type2.sm .item.video-block {
    /*padding-top: 30px;*/
    padding-bottom: 0;
  }

  .item-views.company.md:not(.type2) .text-block .item .text {
    padding-top: 30px;
    padding-bottom: 0;
  }

  .item-views.company.bg .text-block .item .text {
    padding-top: 35px;
    padding-bottom: 0;
  }

  .lg.item-views.company .text {
    padding-top: 24px;
  }
  .item-views.company .text-block .item .item-inner h3 {
    margin: 6px 0px 16px;
    line-height: 1.295em;
  }
  .item-views.company .text-block .item .item-inner .buttons {
    margin-top: -2px;
    margin-bottom: 13px;
  }
  .with-padding.company.item-views:not(.type2) .item.video-block .image {
    padding-top: 62.4%;
  }
  .item-views.company.md:not(.type2) .image-block,
  .item-views.company.bg .image-block {
    margin-top: 18px;
  }
  .item-views.company.type2.md .image-block {
    padding-left: 30px;
    padding-right: 30px;
  }
  .item-views.company.type2.md .item.video-block .image {
    width: 100%;
    height: auto;
    padding-top: 100%;
  }
  .item-views.company.type2.md .item.video-block {
    max-width: 314px; /*max-width: 100%;*/
    margin: 0 auto;
    padding-top: 45px;
  }

  body .wrapper1:not(.with_left_block) .drag-block .item-views.company.type2.md .text.with-benefit,
  body .with_left_block .drag-block .item-views.company.type2.md .text.with-benefit {
    padding: 25px 14px 32px 14px;
  }
  .item-views.company.type2 .js-tizers .item-views.tizers .mobile-list .item-wrapper {
    padding-left: 14px;
    padding-right: 14px;
  }
  body#main
    .wrapper1
    .content_wrapper_block
    .item-views.company.type2
    .js-tizers
    .content_wrapper_block
    .maxwidth-theme {
    padding-top: 0;
  }
  .item-views.company.type2:not(.no-img) .item .with-benefit .item-views.tizers .mobile-list .item {
    padding-bottom: 18px;
  }
  .item-views.company.type2:not(.no-img) .item .with-benefit .item-views.tizers .mobile-list.row {
    padding-bottom: 9px;
  }

  .item-views.company.type2.sm .image-block,
  .item-views.company.type2.sm .text-block {
    padding-left: 30px;
    padding-right: 30px;
  }
  .item-views.company.type2:not(.sm) .item.video-block,
  .item-views.company.type2 .item.video-block,
  .item-views.company.type2.sm .item.video-block {
    padding-top: 45px;
  }
  .item-views.company.type2.sm .text-block .text {
    padding-left: 0;
    padding-right: 0;
    padding-top: 25px;
  }

  .item-views.company.type2.sm.no-img .item.video-block .buttons {
    display: none;
  }

  .item-views.company.type2.sm.no-img .item.video-block .with-text-block-wrapper .js-h3 h3 {
    margin-top: 5px;
    margin-bottom: 16px;
  }
  .item-views.company.type2.sm.no-img .item.video-block {
    padding-top: 32px;
  }
  .item-views.company.type2.sm.no-img .text-block .text {
    padding-top: 0;
  }
  .item-views.company.type2.sm.no-img .text.with-benefit .item-views.tizers {
    padding-top: 16px;
  }
  .item-views.company.type2.sm.no-img .text.with-benefit .item-views.tizers .mobile-list.row {
    padding-bottom: 22px;
  }
}
