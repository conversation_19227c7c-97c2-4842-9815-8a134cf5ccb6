<?
$MESS["CATALOG_COMPARE_BUY"] = "Buy";
$MESS["CATALOG_NOT_AVAILABLE"] = "(not in stock)";
$MESS["CATALOG_SHOWN_CHARACTERISTICS"] = "Showed";
$MESS["CATALOG_ONLY_DIFFERENT"] = "Only differences";
$MESS["CATALOG_ALL_CHARACTERISTICS"] = "All characteristics";
$MESS["CATALOG_REMOVE_PRODUCT"] = "Delete";
$MESS["CATALOG_ADD_TO_COMPARE_LIST"] = "Add to a list of comparisons";
$MESS["CATALOG_COMPARE_PARAMS"] = "Comparison parameters";
$MESS["CATALOG_COMPARE_PRICE"] = "Price";
$MESS["CATALOG_COMPARE_ADD_NEW"] = "Add the product";
$MESS["CATALOG_COMPARE_ADD_NEW_DESCRIPTION"] = "Add goods to comparison";
$MESS["IBLOCK_OFFER_FIELD_ID"] = "ID suggestions";
$MESS["IBLOCK_OFFER_FIELD_CODE"] = "Symbolic proposal code";
$MESS["IBLOCK_OFFER_FIELD_XML_ID"] = "External proposal code";
$MESS["IBLOCK_OFFER_FIELD_NAME"] = "The name of the sentence";
$MESS["IBLOCK_OFFER_FIELD_TAGS"] = "Tags of sentences";
$MESS["IBLOCK_OFFER_FIELD_SORT"] = "Sorting a sentence";
$MESS["IBLOCK_OFFER_FIELD_DESCRIPTION"] = "Description of the sentence";
$MESS["IBLOCK_OFFER_FIELD_DESCRIPTION_TYPE"] = "Type of description of the sentence";
$MESS["IBLOCK_OFFER_FIELD_PICTURE"] = "The image of the sentence";
$MESS["IBLOCK_OFFER_FIELD_PREVIEW_TEXT"] = "Description for the announcement of the offer";
$MESS["IBLOCK_OFFER_FIELD_PREVIEW_TEXT_TYPE"] = "Type of description for the announcement of a sentence";
$MESS["IBLOCK_OFFER_FIELD_PREVIEW_PICTURE"] = "Picture for the announcement of the offer";
$MESS["IBLOCK_OFFER_FIELD_DETAIL_TEXT"] = "A detailed description of the sentence";
$MESS["IBLOCK_OFFER_FIELD_DETAIL_TEXT_TYPE"] = "Type of detailed description of the sentence";
$MESS["IBLOCK_OFFER_FIELD_DETAIL_PICTURE"] = "Detail picture of the sentence";
$MESS["IBLOCK_OFFER_FIELD_DATE_ACTIVE_FROM"] = "The beginning of activity (date) of the sentence";
$MESS["IBLOCK_OFFER_FIELD_ACTIVE_FROM"] = "The beginning of activity (time) of the sentence";
$MESS["IBLOCK_OFFER_FIELD_DATE_ACTIVE_TO"] = "End of activity (date) of the offer";
$MESS["IBLOCK_OFFER_FIELD_ACTIVE_TO"] = "Ending of activity (time) of the sentence";
$MESS["IBLOCK_OFFER_FIELD_SHOW_COUNTER"] = "The number of shows of proposal";
$MESS["IBLOCK_OFFER_FIELD_SHOW_COUNTER_START"] = "Date of the first show of sentences";
$MESS["IBLOCK_OFFER_FIELD_STATUS"] = "Status suggestions";
$MESS["IBLOCK_OFFER_FIELD_IBLOCK_TYPE_ID"] = "Type of information block of proposal";
$MESS["IBLOCK_OFFER_FIELD_IBLOCK_ID"] = "ID of the information block of proposal";
$MESS["IBLOCK_OFFER_FIELD_IBLOCK_CODE"] = "Symbol code of the information block of proposal";
$MESS["IBLOCK_OFFER_FIELD_IBLOCK_NAME"] = "The name of the information block of proposal";
$MESS["IBLOCK_OFFER_FIELD_IBLOCK_EXTERNAL_ID"] = "External code of the information block of proposal";
$MESS["IBLOCK_OFFER_FIELD_DATE_CREATE"] = "The date of creating a proposal";
$MESS["IBLOCK_OFFER_FIELD_CREATED_BY"] = "Who was created (ID) sentences";
$MESS["IBLOCK_OFFER_FIELD_CREATED_USER_NAME"] = "Who was created (name) sentences";
$MESS["IBLOCK_OFFER_FIELD_TIMESTAMP_X"] = "Date Amendments to proposals";
$MESS["IBLOCK_OFFER_FIELD_MODIFIED_BY"] = "KEM AMENDED (ID) suggestions";
$MESS["IBLOCK_OFFER_FIELD_USER_NAME"] = "Who is changed (name) sentences";
$MESS["IBLOCK_OFFER_FIELD_SECTION_ID"] = "Proposals";
$MESS["IBLOCK_OFFER_FIELD_ACTIVE"] = "The activity of the sentence";
$MESS["IBLOCK_OFFER_FIELD_BP_PUBLISHED"] = "Published proposals";
$MESS["IBLOCK_OFFER_FIELD_ACTIVE_PERIOD_FROM"] = "The beginning of the activity of the sentence";
$MESS["IBLOCK_OFFER_FIELD_ACTIVE_PERIOD_TO"] = "The end of the proposal activity";

$MESS["CLEAR_ALL_COMPARE"] = "Clear";
$MESS["MEASURE_UNIT"] = " pcs.";
$MESS["PRICE_FROM"] = "from";
?>