funcDefined("sliceItemBlock")||(sliceItemBlock=function(){$(".cur .catalog_block .catalog_item_wrapp.catalog_item .item_info .item-title").sliceHeight({item:".cur .catalog_item:not(.big)",mobile:!0}),$(".cur .catalog_block .catalog_item_wrapp.catalog_item .item_info .sa_block").sliceHeight({item:".cur .catalog_item:not(.big)",mobile:!0}),$(".cur .catalog_block .catalog_item_wrapp.catalog_item .item_info .cost.prices").sliceHeight({item:".cur .catalog_item:not(.big)",mobile:!0}),$(".cur .catalog_block .catalog_item_wrapp.catalog_item").sliceHeight({classNull:".footer_button",item:".cur .catalog_item:not(.big)",mobile:!0}),InitCustomScrollBar()}),funcDefined("sliceItemBlockSlide")||(sliceItemBlockSlide=function(){$(".cur .catalog_block.owl-carousel .catalog_item_wrapp.catalog_item .item_info .item-title").sliceHeight({item:".cur .catalog_item:not(.big)",mobile:!0,autoslicecount:!1,slice:999}),$(".cur .catalog_block.owl-carousel .catalog_item_wrapp.catalog_item .item_info .sa_block").sliceHeight({item:".cur .catalog_item:not(.big)",mobile:!0,autoslicecount:!1,slice:999}),$(".cur .catalog_block.owl-carousel .catalog_item_wrapp.catalog_item .item_info .cost.prices").sliceHeight({item:".cur .catalog_item:not(.big)",mobile:!0,autoslicecount:!1,slice:999}),InitCustomScrollBar()}),$(document).ready(function(){"SelectOfferProp"in window||"function"==typeof window.SelectOfferProp||(SelectOfferProp1=function(){var t=$(this),e={},r={},c=parseUrlQuery(),a="",s=t.closest(".bx_catalog_item_scu");e={PARAMS:t.closest(".js_wrapper_items").data("params"),ID:s.data("offer_id"),SITE_ID:s.data("site_id"),LINK_ID:s.data("id")+"_block",IBLOCK_ID:s.data("offer_iblockid"),PROPERTY_ID:s.data("propertyid"),DEPTH:t.closest(".item_wrapper").index(),VALUE:t.data("onevalue"),CLASS:"inner_content",PICTURE:t.closest(".catalog_item_wrapp").find(".thumb img").attr("src"),ARTICLE_NAME:t.closest(".catalog_item_wrapp").find(".article_block").data("name"),ARTICLE_VALUE:t.closest(".catalog_item_wrapp").find(".article_block").data("value")},"clear_cache"in c&&"Y"==c.clear_cache&&(a+="?clear_cache=Y");for(i=0;i<e.DEPTH+1;i++)strName="PROP_"+s.find(".item_wrapper:eq("+i+") > div").data("id"),r[strName]=s.find(".item_wrapper:eq("+i+") li.item.active").data("onevalue"),e[strName]=s.find(".item_wrapper:eq("+i+") li.item.active").data("onevalue");if(t.siblings().removeClass("active"),t.addClass("active"),t.attr("title"))t.closest(".item_wrapper").find(".show_class span").text(t.attr("title"));else{var n=t.find(" > i");n.length&&n.attr("title")&&t.closest(".item_wrapper").find(".show_class span").text(n.attr("title"))}$.ajax({url:arMaxOptions.SITE_DIR+"ajax/js_item_detail.php"+a,type:"POST",data:e}).success(function(t){var e=BX.processHTML(t);BX.ajax.processScripts(e.SCRIPT)})},$(document).on("click",".bx_catalog_item_scu li.item",SelectOfferProp))}),function(t){t.JCCatalogSectionOnlyElement||(t.JCCatalogSectionOnlyElement=function(t){"object"==typeof t&&(this.params=t,this.obProduct=null,this.set_quantity=1,this.currentPriceMode="",this.currentPrices=[],this.currentPriceSelected=0,this.currentQuantityRanges=[],this.currentQuantityRangeSelected=0,this.params.MESS&&(this.mess=this.params.MESS),this.init())},t.JCCatalogSectionOnlyElement.prototype={init:function(){this.obProduct=BX(this.params.ID),this.obProduct&&($(this.obProduct).find(".counter_wrapp .counter_block input").data("product","ob"+this.obProduct.id+"el"),this.currentPriceMode=this.params.ITEM_PRICE_MODE,this.currentPrices=this.params.ITEM_PRICES,this.currentQuantityRanges=this.params.ITEM_QUANTITY_RANGES)},setPriceAction:function(){this.set_quantity=this.params.MIN_QUANTITY_BUY,$(this.obProduct).find("input[name=quantity]").length&&(this.set_quantity=$(this.obProduct).find("input[name=quantity]").val()),this.checkPriceRange(this.set_quantity),$(this.obProduct).find(".not_matrix").hide(),$(this.obProduct).find(".with_matrix .price_value_block").html(getCurrentPrice(this.currentPrices[this.currentPriceSelected].PRICE,this.currentPrices[this.currentPriceSelected].CURRENCY,this.currentPrices[this.currentPriceSelected].PRINT_PRICE)),$(this.obProduct).find(".with_matrix .discount")&&$(this.obProduct).find(".with_matrix .discount").html(getCurrentPrice(this.currentPrices[this.currentPriceSelected].BASE_PRICE,this.currentPrices[this.currentPriceSelected].CURRENCY,this.currentPrices[this.currentPriceSelected].PRINT_BASE_PRICE)),"Y"==this.params.SHOW_DISCOUNT_PERCENT_NUMBER&&(0<this.currentPrices[this.currentPriceSelected].PERCENT&&this.currentPrices[this.currentPriceSelected].PERCENT<100?($(this.obProduct).find(".with_matrix .sale_block .sale_wrapper .value").length||$('<div class="value"></div>').insertBefore($(this.obProduct).find(".with_matrix .sale_block .sale_wrapper .text")),$(this.obProduct).find(".with_matrix .sale_block .sale_wrapper .value").html("-<span>"+this.currentPrices[this.currentPriceSelected].PERCENT+"</span>%")):$(this.obProduct).find(".with_matrix .sale_block .sale_wrapper .value").length&&$(this.obProduct).find(".with_matrix .sale_block .sale_wrapper .value").remove()),$(this.obProduct).find(".with_matrix .sale_block .text .values_wrapper").html(getCurrentPrice(this.currentPrices[this.currentPriceSelected].DISCOUNT,this.currentPrices[this.currentPriceSelected].CURRENCY,this.currentPrices[this.currentPriceSelected].PRINT_DISCOUNT)),"NOT_SHOW"in this.params&&"Y"!=this.params.NOT_SHOW&&$(this.obProduct).find(".with_matrix").show(),"Y"==arMaxOptions.THEME.SHOW_TOTAL_SUMM&&void 0!==this.currentPrices[this.currentPriceSelected]&&setPriceItem($(this.obProduct),this.set_quantity,this.currentPrices[this.currentPriceSelected].PRICE)},checkPriceRange:function(t){if(void 0!==t&&"Q"==this.currentPriceMode){var e,i=!1;for(var r in this.currentQuantityRanges)if(this.currentQuantityRanges.hasOwnProperty(r)&&(e=this.currentQuantityRanges[r],parseInt(t)>=parseInt(e.SORT_FROM)&&("INF"==e.SORT_TO||parseInt(t)<=parseInt(e.SORT_TO)))){i=!0,this.currentQuantityRangeSelected=e.HASH;break}for(var c in!i&&(e=this.getMinPriceRange())&&(this.currentQuantityRangeSelected=e.HASH),this.currentPrices)if(this.currentPrices.hasOwnProperty(c)&&this.currentPrices[c].QUANTITY_HASH==this.currentQuantityRangeSelected){this.currentPriceSelected=c;break}}},getMinPriceRange:function(){var t;for(var e in this.currentQuantityRanges)this.currentQuantityRanges.hasOwnProperty(e)&&(!t||parseInt(this.currentQuantityRanges[e].SORT_FROM)<parseInt(t.SORT_FROM))&&(t=this.currentQuantityRanges[e]);return t}})}(window);