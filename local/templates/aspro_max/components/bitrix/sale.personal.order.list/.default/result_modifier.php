<?
foreach ($arResult['ORDERS'] as $k => $s) {

    $df = false;
    foreach ($s['PAYMENT'] as $v) {
        if ($v['ACTION_FILE'] == 'inner') {
            $sum_inner = $v['SUM'];
        }
        if ($v['PAY_SYSTEM_ID'] != 14 && $v['ACTION_FILE'] != 'inner') {
            $df = true;
        }
    }
    if ($df) {
        foreach ($s['PAYMENT'] as $sd => $v) {
            if ($v['ACTION_FILE'] != 'inner') {
                $arResult['ORDERS'][$k]['PAYMENT'][$sd]['FORMATED_SUM'] = CurrencyFormat($s['ORDER']['PRICE'] - $sum_inner, 'RUB');
            }
        }
    }
}
