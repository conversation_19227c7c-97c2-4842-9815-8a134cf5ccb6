<? if (!defined("B_PROLOG_INCLUDED") || B_PROLOG_INCLUDED !== true) die();
$frame = $this->createFrame()->begin("");
$templateData = array(
	//'TEMPLATE_THEME' => $this->GetFolder().'/themes/'.$arParams['TEMPLATE_THEME'].'/style.css',
	'TEMPLATE_CLASS' => 'bx_' . $arParams['TEMPLATE_THEME']
);
$injectId = $arParams['UNIQ_COMPONENT_ID'];

if (isset($arResult['REQUEST_ITEMS'])) {
	// code to receive recommendations from the cloud
	CJSCore::Init(array('ajax'));

	// component parameters
	$signer = new \Bitrix\Main\Security\Sign\Signer;
	$signedParameters = $signer->sign(
		base64_encode(serialize($arResult['_ORIGINAL_PARAMS'])),
		'bx.bd.products.recommendation'
	);
	$signedTemplate = $signer->sign($arResult['RCM_TEMPLATE'], 'bx.bd.products.recommendation');

?>

	<div id="<?= $injectId ?>"></div>

	<script type="text/javascript">
		BX.ready(function() {
			bx_rcm_get_from_cloud(
				'<?= CUtil::JSEscape($injectId) ?>',
				<?= CUtil::PhpToJSObject($arResult['RCM_PARAMS']) ?>, {
					'parameters': '<?= CUtil::JSEscape($signedParameters) ?>',
					'template': '<?= CUtil::JSEscape($signedTemplate) ?>',
					'site_id': '<?= CUtil::JSEscape(SITE_ID) ?>',
					'rcm': 'yes'
				}
			);
		});
	</script>
<?
	$frame->end();
	return;

	// \ end of the code to receive recommendations from the cloud
}
if ($arResult['ITEMS']) { ?>
	<? $arResult['RID'] = ($arResult['RID'] ? $arResult['RID'] : (\Bitrix\Main\Context::getCurrent()->getRequest()->get('RID') != 'undefined' ? \Bitrix\Main\Context::getCurrent()->getRequest()->get('RID') : '')); ?>
	<input type="hidden" name="bigdata_recommendation_id" value="<?= htmlspecialcharsbx($arResult['RID']) ?>">
	<? $bRow = (isset($arParams['ROW']) && $arParams['ROW'] == 'Y'); ?>
	<? $bSlide = (isset($arParams['SLIDER']) && $arParams['SLIDER'] == 'Y'); ?>
	<? $bShowBtn = (isset($arParams['SHOW_BTN']) && $arParams['SHOW_BTN'] == 'Y'); ?>
	<div id="<?= $injectId ?>_items" class="bigdata_recommended_products_items">
		<? if ($arParams['TITLE_SLIDER']) : ?>
			<div class="font_md darken subtitle option-font-bold"><?= $arParams['TITLE_SLIDER']; ?></div>
		<? endif; ?>
		<div class="block-items<?= ($bRow ? ' flexbox flexbox--row flex-wrap' : ''); ?><?= ($bSlide ? ' owl-carousel owl-theme owl-bg-nav short-nav hidden-dots' : ''); ?> swipeignore" <? if ($bSlide) : ?>data-plugin-options='{"nav": true, "autoplay" : false, "autoplayTimeout" : "3000", "margin": -1, "smartSpeed":1000, <?= (count($arResult["ITEMS"]) > 4 ? "\"loop\": true," : "") ?> "responsiveClass": true, "responsive":{"0":{"items": 1},"600":{"items": 2},"768":{"items": 3},"992":{"items": 4}}}' <? endif; ?>>
			<? foreach ($arResult['ITEMS'] as $key => $arItem) { ?>
				<? $strMainID = $this->GetEditAreaId($arItem['ID'] . $key); ?>
				<div class="block-item bordered rounded3<?= ($bSlide ? '' : ' box-shadow'); ?>">
					<div class="block-item__wrapper<?= ($bShowBtn ? ' w-btn' : ''); ?> colored_theme_hover_bg-block" id="<?= $strMainID; ?>">
						<div class="block-item__inner flexbox flexbox--row">
							<?
							$totalCount = CMax::GetTotalCount($arItem, $arParams);
							$arQuantityData = CMax::GetQuantityArray($totalCount);
							$arItem["FRONT_CATALOG"] = "Y";
							$arItem["RID"] = $arResult["RID"];
							$arAddToBasketData = CMax::GetAddToBasketArray($arItem, $totalCount, $arParams["DEFAULT_COUNT"], $arParams["BASKET_URL"], true);

							$elementName = ((isset($arItem['IPROPERTY_VALUES']['ELEMENT_PAGE_TITLE']) && $arItem['IPROPERTY_VALUES']['ELEMENT_PAGE_TITLE']) ? $arItem['IPROPERTY_VALUES']['ELEMENT_PAGE_TITLE'] : $arItem['NAME']);

							$strMeasure = '';
							if ($arItem["OFFERS"]) {
								$strMeasure = $arItem["MIN_PRICE"]["CATALOG_MEASURE_NAME"];
							} else {
								if (($arParams["SHOW_MEASURE"] == "Y") && ($arItem["CATALOG_MEASURE"])) {
									$arMeasure = CCatalogMeasure::getList(array(), array("ID" => $arItem["CATALOG_MEASURE"]), false, false, array())->GetNext();
									$strMeasure = $arMeasure["SYMBOL_RUS"];
								}
							}
							$arItem["DETAIL_PAGE_URL"] .= ($arResult["RID"] ? '?RID=' . $arResult["RID"] : '');
							?>

							<div class="block-item__image block-item__image--wh80">
								<? $arItem["BIG_DATA"] = "Y"; ?>
								<? \Aspro\Functions\CAsproMaxItem::showImg($arParams, $arItem, false); ?>
							</div>
							<div class="block-item__info item_info">
								<div class="block-item__title">
									<a href="<?= $arItem["DETAIL_PAGE_URL"] ?>" class="dark-color font_xs"><span><?= $elementName ?></span></a>
								</div>
								<div class="block-item__cost cost prices clearfix price-flex">
									<? if ($arItem["OFFERS"]) : ?>
										<? \Aspro\Functions\CAsproMaxSku::showItemPrices($arParams, $arItem, $item_id, $min_price_id, array(), 'Y'); ?>
									<? else : ?>
										<?
										if (isset($arItem['PRICE_MATRIX']) && $arItem['PRICE_MATRIX']) // USE_PRICE_COUNT
										{ ?>
											<? if ($arItem['ITEM_PRICE_MODE'] == 'Q' && count($arItem['PRICE_MATRIX']['ROWS']) > 1) : ?>
												<?= CMax::showPriceRangeTop($arItem, $arParams, GetMessage("CATALOG_ECONOMY")); ?>
											<? endif; ?>
											<?= CMax::showPriceMatrix($arItem, $arParams, $strMeasure, $arAddToBasketData); ?>
										<?
										} elseif (isset($arItem["PRICES"])) {
											$price_sale = $arItem['MIN_PRICE']['DISCOUNT_VALUE'];
											$price = $arItem['MIN_PRICE']['VALUE'];
											$tt = false;
											if (count($arItem['PRICES']) > 1) {
												$pr = [];
												foreach ($arItem['PRICES'] as $pt) {
													$pr[] = $pt['VALUE'];
												}
												$price = max($pr);
												$price_sale = min($pr);
											}
										?>
											<? if ($price != $price_sale) { ?>
												<div class="price_matrix_block">
													<div class="price_group min">
														<div class="price_matrix_wrapper ">
															<div class="prices-wrapper">
																<div class="price font-bold font_mxs dd" data-currency="RUB" data-value="<?= $price_sale ?>">
																	<span><span class="values_wrapper"><span class="price_value"><?= $price_sale ?></span><span class="price_currency"> ₽</span><span class="price_measur ddd"></span></span><span class="price_measure"></span></span>
																</div>
															</div>
														</div>
													</div>
													<div class="price_group">
														<div class="price_name font_xs darken"></div>
														<div class="price_matrix_wrapper strike_block">
															<div class="prices-wrapper">
																<div class="price font-bold font_mxs dd" data-currency="RUB" data-value="<?= $price ?>">
																	<span><span class="values_wrapper"><span class="price_value"><?= $price ?></span><span class="price_currency"> ₽</span><span class="price_measur ddd"></span></span><span class="price_measure"></span></span>
																</div>
															</div>
														</div>
													</div>
												</div>
											<? } else { ?>
												<div class="with_matrix price_matrix_wrapper">
													<div class="prices-wrapper">
														<div class="price font-bold font_mxs">
															<div class="price_value_block values_wrapper">
																<span class="price_value"><?= $price ?></span><span class="price_currency"> ₽</span><span class="price_measur ddd"></span>
															</div><span class="price_measure"></span>
														</div>
													</div>
												</div>
											<? } ?>
											<? //\Aspro\Functions\CAsproMaxItem::showItemPrices($arParams, $arItem["PRICES"], $strMeasure, $min_price_id, 'Y');
											?>
										<? } ?>
									<? endif; ?>

                                    <div class="icons-basket-wrapper offer_buy_block ce_cmp_hidden ajax_load block container-block-recommendations">
                                        <div class="button_block block-recommendations">
                                            <span data-value="525" data-currency="RUB" class="btn-exlg to-cart btn btn-default transition_bg animate-load has-ripple" data-item="<?= $arItem['ID'] ?>" data-float_ratio="1" data-ratio="1" data-bakset_div="bx_basket_div_601877" data-props="" data-part_props="N" data-add_props="Y" data-empty_props="Y" data-offers="" data-iblockid="26" data-quantity="1">
                                                <i class="svg inline  svg-inline-fw ncolor colored" aria-hidden="true" title="В корзину">
                                                    <svg class="" width="19" height="16" viewBox="0 0 19 16">
                                                        <path data-name="Ellipse 2 copy 9" class="cls-1" d="M956.047,952.005l-0.939,1.009-11.394-.008-0.952-1-0.953-6h-2.857a0.862,0.862,0,0,1-.952-1,1.025,1.025,0,0,1,1.164-1h2.327c0.3,0,.6.006,0.6,0.006a1.208,1.208,0,0,1,1.336.918L943.817,947h12.23L957,948v1Zm-11.916-3,0.349,2h10.007l0.593-2Zm1.863,5a3,3,0,1,1-3,3A3,3,0,0,1,945.994,954.005ZM946,958a1,1,0,1,0-1-1A1,1,0,0,0,946,958Zm7.011-4a3,3,0,1,1-3,3A3,3,0,0,1,953.011,954.005ZM953,958a1,1,0,1,0-1-1A1,1,0,0,0,953,958Z" transform="translate(-938 -944)"></path>
                                                    </svg>
                                                </i>
                                            </span>
                                            <a rel="nofollow" href="/basket/" class="btn-exlg in-cart btn btn-default transition_bg " data-item="<?= $arItem['ID'] ?>" style="display:none;">
                                                <i class="svg inline  svg-inline-fw ncolor colored" aria-hidden="true" title="В корзине">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18">
                                                        <path data-name="Rounded Rectangle 906 copy 3" class="cls-1" d="M1005.97,4556.22l-1.01,4.02a0.031,0.031,0,0,0-.01.02,0.87,0.87,0,0,1-.14.29,0.423,0.423,0,0,1-.05.07,0.7,0.7,0,0,1-.2.18,0.359,0.359,0,0,1-.1.07,0.656,0.656,0,0,1-.21.08,1.127,1.127,0,0,1-.18.03,0.185,0.185,0,0,1-.07.02H993c-0.03,0-.056-0.02-0.086-0.02a1.137,1.137,0,0,1-.184-0.04,0.779,0.779,0,0,1-.207-0.08c-0.031-.02-0.059-0.04-0.088-0.06a0.879,0.879,0,0,1-.223-0.22s-0.007-.01-0.011-0.01a1,1,0,0,1-.172-0.43l-1.541-6.14H988a1,1,0,1,1,0-2h3.188a0.3,0.3,0,0,1,.092.02,0.964,0.964,0,0,1,.923.76l1.561,6.22h9.447l0.82-3.25a1,1,0,0,1,1.21-.73A0.982,0.982,0,0,1,1005.97,4556.22Zm-7.267.47c0,0.01,0,.01,0,0.01a1,1,0,0,1-1.414,0l-2.016-2.03a0.982,0.982,0,0,1,0-1.4,1,1,0,0,1,1.414,0l1.305,1.31,4.3-4.3a1,1,0,0,1,1.41,0,1.008,1.008,0,0,1,0,1.42ZM995,4562a3,3,0,1,1-3,3A3,3,0,0,1,995,4562Zm0,4a1,1,0,1,0-1-1A1,1,0,0,0,995,4566Zm7-4a3,3,0,1,1-3,3A3,3,0,0,1,1002,4562Zm0,4a1,1,0,1,0-1-1A1,1,0,0,0,1002,4566Z" transform="translate(-987 -4550)"></path>
                                                    </svg>
                                                </i>
                                            </a>
                                        </div>
                                    </div>

								</div>

								<? \Aspro\Functions\CAsproMax::showBonusBlockList($arItem); ?>

								<? if ($bShowBtn) : ?>
									<div class="more-btn"><a class="btn btn-transparent-border-color btn-xs colored_theme_hover_bg-el" rel="nofollow" href="<?= $arItem["DETAIL_PAGE_URL"] ?>" data-item="<?= $arItem["ID"] ?>"><?= Getmessage("CVP_TPL_MESS_BTN_DETAIL") ?></a></div>
								<? endif; ?>
							</div>
						</div>
					</div>
				</div>
			<? } ?>
		</div>
	</div>
	<? \Aspro\Functions\CAsproMax::showBonusComponentList($arResult); ?>
<? }
$frame->end(); ?>