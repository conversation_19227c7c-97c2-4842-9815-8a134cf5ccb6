<?
$MESS["IBLOCK_STOCK_NAME"] = "stock info block ID";
$MESS["IBLOCK_SERVICES_NAME"] = "Service Information Block ID";
$MESS["IBLOCK_TIZERS_NAME"] = "Teaser info block ID";
$MESS["IBLOCK_LINK_NEWS_NAME"] = "News Infoblock ID";
$MESS["IBLOCK_LINK_REVIEWS_NAME"] = "Feedback info block ID";
$MESS["VACANCY_IBLOCK_ID_TITLE"] = "Job Information Block ID";
$MESS["STAFF_IBLOCK_ID_TITLE"] = "Employee Information Block ID";

$MESS["SEF_MODE_STOCK_SECTIONS"] = "Share Infoblock Root";
$MESS["SEF_MODE_STOCK_ELEMENT"] = "Stock Elements";

$MESS["IBLOCK_ADVT_TYPE"] = "Ad Infoblock Type";
$MESS["IBLOCK_ADVT_NAME"] = "Ad Infoblock ID";
$MESS["IBLOCK_ADVT_SECTION_NAME"] = "Product section ID for the product";
$MESS["IBLOCK_ADVT_SECTION_NAME_SECT"] = "ID of the advertising section for the section";

$MESS["SEF_MODE_BRAND_SECTIONS"] = "Brand Infoblock Root";
$MESS["SEF_MODE_BRAND_ELEMENT"] = "Brand Elements";

$MESS["IBLOCK_REVIEWS_TYPE"] = "Type of feedback info block";
$MESS["IBLOCK_REVIEWS_NAME"] = "Feedback info block ID";
$MESS["BLOG_IBLOCK_ID_TITLE"] = "Article info block ID";
$MESS["VIEW_BLOCK_TYPE"] = "Display related products in separate blocks";
$MESS["STAFF_VIEW_TYPE_TITLE"] = "Employee Display Type";

$MESS["SKU_DISPLAY_LOCATION"] = "Place of display of product offers";
$MESS["SKU_DISPLAY_LOCATION_RIGHT"] = "to the right of the product photo";
$MESS["SKU_DISPLAY_LOCATION_BOTTOM"] = "under the product photo";
$MESS["SKU_DISPLAY_LOCATION_TIP"] = "The display of product offers to the right of the photo is suitable for a small list, if there are many offers, it will be more convenient to place them below";
$MESS["OFFER_SHOW_PREVIEW_PICTURE_PROPS_TITLE"] = "Display the preview image of the proposal in the properties for selection";

$MESS["SHOW_QUANTITY"] = "Show product availability";
$MESS["SECTION_TOP_BLOCK_TITLE"] = "Block Title";
$MESS["SECTION_TOP_BLOCK_TITLE_VALUE"] = "Best Offers";
$MESS["VIEWED_ELEMENT_COUNT"] = "The number of displayed viewed products";
$MESS["VIEWED_BLOCK_TITLE"] = "Block title with viewed items";
$MESS["VIEWED_TITLE"] = "Previously you watched";
$MESS["CP_BC_TPL_USE_CUSTOM_RESIZE"] = "Use image resizing in the gallery from the info block settings";
$MESS["USE_CUSTOM_RESIZE_TIP"] = "With this value set, the initial size of all pictures in the gallery will be taken from the settings for a detailed picture of the catalog information block";
$MESS["CP_BC_TPL_DETAIL_ADD_DETAIL_TO_SLIDER"] = "Add a detailed picture to the slider";
$MESS["SKU_DETAIL_ID"] = "Parameter defining SKU on the detailed";
$MESS["SKU_DETAIL_ID_TIP"] = "Parameter name for determining the offer on the product detail page";
$MESS["USE_ADDITIONAL_GALLERY"] = "Display block \" Photo Gallery \"";
$MESS["ADDITIONAL_GALLERY_TYPE"] = "Block Type \" Photo Gallery \"";
$MESS["ADDITIONAL_GALLERY_TYPE_BIG"] = "large";
$MESS["ADDITIONAL_GALLERY_TYPE_SMALL"] = "small";
$MESS["ADDITIONAL_GALLERY_PROPERTY_CODE"] = "Property with \" Photo Gallery \"";
$MESS["ADDITIONAL_GALLERY_OFFERS_PROPERTY_CODE"] = "Property of trade offers with \" Photo Gallery \"";
$MESS["BLOCK_ADDITIONAL_GALLERY_NAME"] = "Block Title \" Photo Gallery \"";
$MESS["BLOG_URL"] = "Blog ID for extended reviews";
$MESS["T_REVIEW_COMMENT_REQUIRED"] = "The text of the feedback is required";
$MESS["T_REVIEW_FILTER_BUTTONS"] = "Reviews filtering buttons";
$MESS['T_REAL_CUSTOMER_TEXT'] = 'Signature text "Real Buyer" for review';

$MESS["DEFAULT_LIST_TEMPLATE"] = "Show products in the default section";
$MESS["DEFAULT_LIST_TEMPLATE_TABLE"] = "table";
$MESS["DEFAULT_LIST_TEMPLATE_LIST"] = "list";
$MESS["DEFAULT_LIST_TEMPLATE_BLOCK"] = "tile";
$MESS["USE_RATING"] = "Use rating";
$MESS["MAX_AMOUNT"] = 'The value above which a lot is displayed';
$MESS["USE_ONLY_MAX_AMOUNT"] = 'Replace with the expression only the values вЂ‹вЂ‹of "many" and "custom" ';
$MESS["LIST_DISPLAY_POPUP_IMAGE"] = 'Show a pop-up photo of the product in the template "Table"';
$MESS["LIST_DEFAULT_CATALOG_TEMPLATE"] = 'Show directory by default';
$MESS["LIST_DEFAULT_CATALOG_TEMPLATE_LIST"] = 'list';
$MESS["LIST_DEFAULT_CATALOG_TEMPLATE_TABLE"] = 'table';
$MESS["LIST_DEFAULT_CATALOG_TEMPLATE_BLOCK"] = 'tile';
$MESS["SECTION_DISPLAY_PROPERTY"] = 'Section property with default template';
$MESS["MAX_COUNT_TIP"] = "The maximum (if there is enough stock in the warehouse) number of items available for selection in the basket from the productвЂ™s card. 0 - the list is limited by stock balances";
$MESS["MAX_BASKET_PRODUCTS"] = "The maximum number of products available for selection (0 - unlimited)";
$MESS["DEFAULT_BASKET_PRODUCTS"] = "The number of items to add to the basket by default";
$MESS["DISPLAY_WISH_BUTTONS"] = "Show 'postpone' button";
$MESS["DEFAULT_COUNT"] = "The amount added to the basket by default";
$MESS["SHOW_BRAND_PICTURE"] = "Show brand logo";
$MESS["PROPERTIES_DISPLAY_LOCATION"] = "Show product properties";
$MESS["PROPERTIES_DISPLAY_LOCATION_DESCRIPTION"] = "in description";
$MESS["PROPERTIES_DISPLAY_LOCATION_TAB"] = "in a separate bookmark";
$MESS["PROPERTIES_DISPLAY_TYPE"] = "Show properties";
$MESS["PROPERTIES_DISPLAY_TYPE_BLOCK"] = "tile";
$MESS["PROPERTIES_DISPLAY_TYPE_TABLE"] = "table";
$MESS["SHOW_ADDITIONAL_TAB"] = "Show the 'Advanced' tab";
$MESS["SHOW_ASK_BLOCK"] = "Show block 'Ask a question'";
$MESS["ASK_FORM_ID"] = "Form ID 'Ask a Question'";

$MESS["SHOW_MEASURE"] = "Show units of measure";
$MESS["SHOW_QUANTITY_COUNT"] = "Show product availability by quantity";
$MESS["SHOW_HINTS"] = "Show tooltips for properties";
$MESS["USE_SHARE"] = "Show block \" Share \"";

$MESS["BIGDATA_NORMAL_TITLE"] = "Template for personal recommendations for a detailed catalog page with tabs and without tabs";
$MESS["BIGDATA_EXT_TITLE"] = "Template for personal recommendations for the detailed catalog page";

$MESS["SORT_BUTTONS"] = "Sort Buttons";
$MESS["SORT_BUTTONS_SORT"] = "by sorting index";
$MESS["SORT_BUTTONS_POPULARITY"] = "by popularity";
$MESS["SORT_BUTTONS_NAME"] = "alphabetically";
$MESS["SORT_BUTTONS_PRICE"] = "for the price";
$MESS["SORT_BUTTONS_QUANTITY"] = "by availability";

$MESS["SORT_PRICES"] = "Sort by price";
$MESS["SORT_PRICES_MINIMUM_PRICE"] = "Lowest Price";
$MESS["SORT_PRICES_MAXIMUM_PRICE"] = "Maximum Price";
$MESS["SORT_PRICES_REGION_PRICE"] = "Price for sorting by region";
$MESS["SORT_REGION_PRICE"] = "The price for sorting in the region by default";
$MESS["FILTER_BUTTONS_PHOTO"] = "Only with the photo";
$MESS["FILTER_BUTTONS_TEXT"] = "Only with the text";
$MESS["FILTER_BUTTONS_RATING"] = "By ranking";

$MESS["SHOW_SECTION_PREVIEW"] = "Show section description";
$MESS["SHOW_SECTION_ROOT_PREVIEW"] = "Show subsection description";
$MESS["SHOW_SUBSECTION_DESC"] = "Show description of subsections";
$MESS["SHOW_SECTION_PREVIEW_PROPERTY"] = "Take a description from";
$MESS["SHOW_SUBSECTION_PREVIEW_PROPERTY"] = "Take the description for the subsections from";
$MESS["SHOW_SECTION_PREVIEW_PROPERTY_DESCRIPTION"] = "section descriptions";
$MESS["SHOW_SECTION_PREVIEW_PROPERTY_UF"] = "section descriptions";

$MESS["SHOW_SECTION_PREVIEW"] = "Show SEO description inside the section";
$MESS["SECTION_PREVIEW_PROPERTY"] = "Property with SEO section description";

$MESS["SHOW_COUNTER_LIST"] = "Show the discount validity period in the list";
$MESS["DISPLAY_ELEMENT_COUNT_TITLE"] = "Display the number of elements for properties in the filter";
$MESS["SHOW_DISCOUNT_TIME_EACH_SKU"] = "Display the discount validity period for each trading offer";
$MESS["SHOW_DISCOUNT_TIME_EACH_SKU_TIP"] = "The option is valid for type SKU 1";
$MESS["SHOW_COUNTER_LIST_TIP"] = "The option is valid for type SKU 1";

$MESS["SHOW_SECTION_PICTURES"] = "Show pictures of subsections";
$MESS["SHOW_SECTION_DESC"] = "Show section description";
$MESS["SHOW_ROOT_SECTION_PICTURE"] = "Show section pictures in the root of the directory";
$MESS["SHOW_SECTION_SIBLINGS"] = "Show the list of sections";
$MESS["SHOW_KIT_PARTS"] = "Show package contents";
$MESS["SHOW_KIT_PARTS_PRICES"] = "Show prices of kit parts";

$MESS["IBLOCK_BANNERS_TYPE"] = "Banner Infoblock Type";
$MESS["IBLOCK_BANNERS_ID"] = "Banner Infoblock ID";
$MESS["IBLOCK_BANNERS_TYPE_ID"] = "Information block ID with banner types";
$MESS["IBLOCK_SMALL_BANNERS_TYPE_ID"] = "The type of banners to display in the directory";

$MESS["OFFERS_SETTINGS"] = "Properties for selecting offers";
$MESS["CP_BC_TPL_ADD_PICT_PROP"] = "Additional picture of the main product";
$MESS["CP_BC_TPL_LABEL_PROP"] = "Product Tag Property";
$MESS["CP_BC_TPL_PRODUCT_DISPLAY_MODE"] = "Display scheme";
$MESS["CP_BC_TPL_OFFER_ADD_PICT_PROP"] = "Additional offer pictures";

$MESS["CP_BC_TPL_PROP_EMPTY"] = "not selected";

$MESS["CP_BC_TPL_SHOW_DISCOUNT_PERCENT"] = "Show savings";
$MESS["CP_BC_TPL_SHOW_DISCOUNT_PERCENT_NUMBER"] = "Show percentage of savings";
$MESS["SHOW_DISCOUNT_TIME"] = "Display discount expiration date";
$MESS["CP_BC_TPL_SHOW_OLD_PRICE"] = "Show old price";
$MESS["DETAIL_OFFERS_LIMIT"] = "Maximum number of offers to show (0 - all)";

$MESS["AJAX_FILTER_CATALOG_TITLE"] = "Use ajax filter";
$MESS["USE_FILTER_PRICE_TITLE"] = "Use price for filter";
$MESS["OFFER_HIDE_NAME_PROPS_TITLE"] = "Hide property names for quotes";

$MESS["CP_BC_TPL_USE_BIG_DATA"] = "Show personalized recommendations";
$MESS["CP_BC_TPL_BIG_DATA_RCM_TYPE"] = "Recommendation Type";
$MESS["CP_BC_TPL_RCM_BESTSELLERS"] = "Top Selling";
$MESS["CP_BC_TPL_RCM_PERSONAL"] = "Personal recommendations";
$MESS["CP_BC_TPL_RCM_SOLD_WITH"] = "Selling with this item";
$MESS["CP_BC_TPL_RCM_VIEWED_WITH"] = "Browsed with this item";
$MESS["CP_BC_TPL_RCM_SIMILAR"] = "Related Products";
$MESS["CP_BC_TPL_RCM_SIMILAR_ANY"] = "Selling / Viewed / Similar Products";
$MESS["CP_BC_TPL_RCM_PERSONAL_WBEST"] = "Best Selling / Personal";
$MESS["CP_BC_TPL_RCM_RAND"] = "Any recommendation";

$MESS["USE_BIG_DATA_TIP"] = "Show personalized recommendations on catalog pages";
$MESS["SHOW_DISCOUNT_PERCENT_TIP"] = "Withdraw savings if there is a discount";
$MESS["SHOW_OLD_PRICE_TIP"] = "Show old price if there is a discount";

$MESS["ADD_PICT_PROP_TIP"] = "Property of additional product images";
$MESS["OFFER_ADD_PICT_PROP_TIP"] = "Property of additional offer pictures (if any)";
$MESS["AJAX_FILTER_CATALOG_TIP"] = "When this option is enabled, the output of filtering results will occur without reloading the page";
$MESS["OFFER_HIDE_NAME_PROPS_TIP"] = "When this option is enabled, the property headers of trading offers will be hidden";

$MESS["SHOW_ONE_CLICK_BUY"] = "Display button \" 1-click purchase \"in the product card";
$MESS["DISPLAY_ELEMENT_SLIDER"] = "The number of elements in the sliders";
$MESS["DISPLAY_ELEMENT_SLIDER_TIP"] = "The number of displayed elements in the blocks: Accessories, Personal recommendations, Related products";

$MESS["SALE_STIKER"] = "Property with stock sticker";
$MESS["SHOW_RATING"] = "Show rating";

$MESS["DETAIL_EXPANDABLES_TITLE"] = "Block title \" Accessories \"";
$MESS["DETAIL_EXPANDABLES_VALUE"] = "Accessories";
$MESS["DETAIL_ASSOCIATED_TITLE"] = "Block Title \" Related Products \"";
$MESS["DETAIL_ASSOCIATED_VALUE"] = "Related Products";

$MESS["DETAIL_PICTURE_MODE_IMG"] = "regular";
$MESS["DETAIL_PICTURE_MODE_POPUP"] = "popup";
$MESS["DETAIL_PICTURE_MODE_MAGNIFIER"] = "magnifier";
$MESS["CP_BCE_TPL_DETAIL_PICTURE_MODE"] = "Detailed picture display mode";
$MESS["SHOW_UNABLE_SKU_PROPS"] = "Show inaccessible property values вЂ‹вЂ‹for TP";
$MESS["DETAIL_DOCS_PROP_TTILE"] = "Property with \" Documents \"";
$MESS["STIKERS_PROP_TITLE"] = "Property with stickers";
$MESS["T_ELEMENT_DETAIL_TYPE_VIEW"] = "Product detail page template";
$MESS["T_LIST_ELEMENTS_TYPE_VIEW"] = "Product List Page Template";
$MESS["T_LIST_SECTIONS_TYPE_VIEW"] = "Section list page template";
$MESS["FROM_MODULE_PARAMS"] = "From the settings of the control center";

$MESS["TAB_OFFERS_NAME_TITLE"] = "Block Title \" Prices \"";
$MESS["TAB_KOMPLECT_NAME_TITLE"] = "Block title \" Package contents \"";
$MESS["TAB_STAFF_NAME_TITLE"] = "Block title \" Employees \"";
$MESS["TAB_VACANCY_NAME_TITLE"] = "Block Title \" Jobs \"";
$MESS["TAB_NABOR_NAME_TITLE"] = "Block title \" Buy set \"";
$MESS["TAB_DESCR_NAME_TITLE"] = "Block title \" Description \"";
$MESS["TAB_CHAR_NAME_TITLE"] = "Block title \" Characteristics \"";
$MESS["TAB_VIDEO_NAME_TITLE"] = "Block title \" Video \"";
$MESS["TAB_REVIEW_NAME_TITLE"] = "Block Title \" Reviews \"";
$MESS["TAB_FAQ_NAME_TITLE"] = "Block title \" Ask a question \"";
$MESS["TAB_STOCK_NAME_TITLE"] = "Block Title \" Availability \"";
$MESS["TAB_NEWS_NAME_TITLE"] = "Block title \" News \"";
$MESS["TAB_DOPS_NAME_TITLE"] = "Block title \" Optional \"";

$MESS["BLOCK_SERVICES_NAME_TITLE"] = "Block title \" Services \"";
$MESS["BLOCK_DOCS_NAME_TITLE"] = "Block title \" Documents \"";
$MESS["SHOW_CHEAPER_FORM"] = "Show form \" Found cheaper \"";
$MESS["SHOW_SEND_GIFT"] = "Show form \" I want a gift \"";
$MESS["CHEAPER_FORM_NAME"] = "Link text \" Found cheaper \"";
$MESS["SEND_GIFT_FORM_NAME"] = "Link text \" I want a gift \"";
$MESS["LANDING_TITLE"] = "Block title \" Popular categories \" for landing pages";
$MESS["LANDING_SECTION_COUNT"] = "The number of visible elements in the block \" Popular categories \" for landing pages";
$MESS["LANDING_SEARCH_TITLE"] = "Block title \" Related queries \" for landing pages";
$MESS["LANDING_SEARCH_COUNT"] = "The number of visible elements in the block \" Similar queries \" for landing pages";
$MESS["SHOW_ARTICLE_SKU"] = "Display the article number if the TP does not have its article value";
$MESS["SHOW_MEASURE_WITH_RATIO"] = "Display unit of measure with a coefficient when displaying the minimum price for goods with quotations";
$MESS["SHOW_MEASURE_WITH_RATIO_TIP"] = "When the option with goods with sales offers is marked, the minimum price will display the unit of measure along with the coefficient (if the coefficient of the unit of measure is not equal to 1)";
$MESS["SHOW_SORT_RANK_BUTTON_TITLE"] = "Display the sorting button by relevance";

$MESS["RESTART"] = "Search without morphology (if there is no search result)";
$MESS["NO_WORD_LOGIC"] = "Disable word processing as logical operators";
$MESS["USE_LANGUAGE_GUESS"] = "Enable automatic keyboard layout detection)";

$MESS["STORES_FILTER_TITLE"] = "Sort warehouses";
$MESS["STORES_FILTER_NAME_TITLE"] = "name";
$MESS["STORES_FILTER_SORT_TITLE"] = "sort";
$MESS["STORES_FILTER_AMOUNT_TITLE"] = "availability";
$MESS["STORES_FILTER_ORDER_TITLE"] = "Sort direction";
$MESS["STORES_FILTER_ORDER_ASC_TITLE"] = "increase";
$MESS["STORES_FILTER_ORDER_DESC_TITLE"] = "decrease";

$MESS["TITLE_SLIDER"] = "Block header with personalized recommendations";
$MESS["TITLE_SLIDER_VALUE"] = "Recommended";

$MESS["BLOCK_LANDINGS_NAME_TITLE"] = "Block Title \" Recommended Collections \"";
$MESS["BLOCK_BLOG_NAME_TITLE"] = "Block title \" Useful \"";

$MESS["VALUE_HOW_BUY"] = "How to buy";
$MESS["VALUE_DELIVERY"] = "Delivery";
$MESS["VALUE_PAYMENT"] = "Payment";
$MESS["TITLE_HOW_BUY"] = "Tab title \" How to buy \"";
$MESS["TITLE_DELIVERY"] = "Tab title \" Delivery \"";
$MESS["TITLE_PAYMENT"] = "Title taba \"Payment \" ";
$MESS["SHOW_HOW_BUY"] = "Display tab \" How to buy \"";
$MESS["SHOW_DELIVERY"] = "Display tab \" Delivery \"";
$MESS["SHOW_PAYMENT"] = "Show tab \" Payment \"";
$MESS["SHOW_GARANTY"] = "Display the block \" Warranty conditions \" in the detailed card type5";
$MESS["TITLE_GARANTY"] = "Block Title \" Warranty Terms \"";
$MESS["VALUE_GARANTY"] = "Warranty Terms";
$MESS["SHOW_BUY_DELIVERY"] = "Display block \" Payment and delivery \" in the detailed card type5";
$MESS["TITLE_BUY_DELIVERY"] = "Block title \" Payment and delivery \" ";
$MESS["VALUE_BUY_DELIVERY"] = "Payment and delivery";
$MESS["RECOMEND_COUNT_TITLE"] = "The number of elements in the block \" Recommended \"";
$MESS["VISIBLE_PROP_COUNT_TITLE"] = "Number of properties on top";

$MESS["ALT_TITLE_GET_TITLE"] = "Take alt and title for pictures from";
$MESS["ALT_TITLE_GET_SEO"] = "seo";
$MESS["ALT_TITLE_GET_NORMAL"] = "description";
$MESS["BUNDLE_ITEMS_COUNT_TITLE"] = "The number of displayed items in the set";

$MESS["SECTION_BG_TITLE"] = "Property with the background image of the section";

$MESS["RESTART_TIP"] = "If this option is checked, the search will be performed first taking into account the morphology. If nothing is found, the search will be launched without taking into account the morphology.";
$MESS["NO_WORD_LOGIC_TIP"] = "When the option is checked, words (and, or, not) will not be used as logical operators.";
$MESS["USE_LANGUAGE_GUESS_TIP"] = "When this option is checked, the layout in which the search query is typed will be automatically determined.";
$MESS["VISIBLE_PROP_COUNT_TIP"] = "This parameter affects the displayed number of product properties in the upper block";

$MESS["USE_DETAIL_PREDICTION_TITLE"] = "Use discount predictions";
$MESS["SHOW_LANDINGS_TITLE"] = "Show block \" Popular categories \"";
$MESS["LANDING_POSITION_TITLE"] = "Block location \" Popular categories \"";
$MESS["LANDING_POSITION_BEFORE_PRODUCTS"] = "Before the list of products";
$MESS["LANDING_POSITION_AFTER_PRODUCTS"] = "After the list of products";
$MESS["LANDING_POSITION_AFTER_DETAIL_TEXT"] = "After a detailed description";
$MESS["LANDING_TITLE"] = "Block title \" Popular categories \"";
$MESS["LANDING_SECTION_COUNT"] = "The number of visible elements in the block \" Popular categories \" (>768px)";
$MESS["LANDING_SECTION_COUNT_MOBILE"] = "The number of visible elements in the block \" Popular categories \" (<768px)";
$MESS["SHOW_LANDINGS_SEARCH_TITLE"] = "Show block \" Related queries \"";
$MESS["LANDING_SEARCH_TITLE"] = "Block Title \" Related Queries \"";
$MESS["LANDING_SEARCH_COUNT"] = "The number of visible elements in the block \" Related Queries \"";
$MESS["T_LANDING_IBLOCK_ID"] = "Landing Page Information Block ID";

$MESS["SHOW_SMARTSEO_TAGS_TITLE"] = "Display tag cloud from Smart SEO";
$MESS["SMARTSEO_TAGS_COUNT"] = "Number of visible elements in the tag cloud from Smart SEO (>768px)";
$MESS["SMARTSEO_TAGS_COUNT_MOBILE"] = "Number of visible elements in the tag cloud from Smart SEO (<768px)";
$MESS["SMARTSEO_TAGS_BY_GROUPS"] = "Group elements in a tag cloud from Smart SEO by rule conditions";
$MESS['SMARTSEO_TAGS_SHOW_DEACTIVATED'] = 'Display disabled tag cloud from Smart SEO elements';
$MESS['SMARTSEO_TAGS_SORT'] = 'Tag cloud from Smart SEO element sorting field';
$MESS['SMARTSEO_TAGS_SORT_NAME'] = 'name';
$MESS['SMARTSEO_TAGS_SORT_SORT'] = 'sort';

$MESS['SHOW_GALLERY_NAME'] = "Display Gallery";
$MESS['MAX_GALLERY_ITEMS_NAME'] = "The number of pictures in the gallery";
$MESS['T_SHOW_PROPS'] = "Display properties in a tile";
$MESS['AJAX_CONTROLS_TITLE'] = "Ajax change sorting and display types";

$MESS["CP_BC_TPL_PRODUCT_BLOCKS_ORDER"] = "Block display order (with tabs)";
$MESS["CP_BC_TPL_PRODUCT_BLOCKS_TAB_ORDER"] = "The order in which blocks are displayed in tabs";
$MESS["CP_BC_TPL_PRODUCT_BLOCKS_ALL_ORDER"] = "The order in which blocks are displayed (without tabs)";

$MESS["ASK_TAB_TITLE"] = "Button title \" Ask a question \"";
$MESS["DETAIL_LINKED_GOODS_SLIDER_TITLE"] = "Display related products with a slider";
$MESS["DETAIL_LINKED_GOODS_TABS_TITLE"] = "Display related products by tabs";

$MESS["CP_BC_TPL_PRODUCT_BLOCK_TIZERS"] = "Teasers";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_SALE"] = "Publications";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_TAB"] = "Tabs";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_GALLERY"] = "Gallery";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_COMMENTS"] = "Comments";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_BRAND"] = "Manufacturer";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_SERVICES"] = "Services";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_NEWS"] = "News";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_GOODS"] = "Products";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_DESC"] = "Description";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_CHAR"] = "Features";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_FAQ"] = "Question and Answer";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_PROJECTS"] = "Projects";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_VIDEO"] = "Video";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_DOCS"] = "Documents";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_TARIFS"] = "Rates";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_PARTNERS"] = "Partners";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_REVIEWS"] = "Reviews";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_STAFF"] = "Employees";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_BLOG"] = "Articles";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_VACANCYS"] = "Jobs";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_SERTIFICATES"] = "Licenses and certificates";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_OFFERS"] = "Trading offers";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_STORES"] = "Availability";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_CUSTOM_TABS"] = "Additional tab";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_GIFTS"] = "Gifts";

$MESS["CP_BC_TPL_PRODUCT_BLOCK_COMPLECT"] = "Package";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_NABOR"] = "Set";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_TABS"] = "Tabs";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_HOW_BUY"] = "How to buy";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_PAYMENT"] = "Payment";
$MESS["CP_BC_TPL_PRODUCT_BLOCK_DELIVERY"] =" Delivery ";

$MESS["CP_BC_TPL_DETAIL_USE_COMMENTS"] = "Include product reviews";
$MESS["CP_BC_TPL_DETAIL_BLOG_EMAIL_NOTIFY"] = "Email recall notification";
$MESS["T_COMMENTS_COUNT"] = "Number of comments per page";
$MESS["CP_BC_TPL_MAX_IMAGE_SIZE"] = "Maximum size of attached pictures";
$MESS["BIGDATA_SHOW_FROM_SECTION"] = "Show from current section";

$MESS["IBLOCK_SORT_ASC"] = "ascending";
$MESS["IBLOCK_SORT_DESC"] = "descending";
$MESS["LINKED_ELEMENT_TAB_SORT_FIELD"] = "By what field do we sort related products";
$MESS["LINKED_ELEMENT_TAB_SORT_ORDER"] = "Sorting order related products";
$MESS["LINKED_ELEMENT_TAB_SORT_FIELD2"] = "Field for the second sort related products";
$MESS["LINKED_ELEMENT_TAB_SORT_ORDER2"] = "Second sorting order related products";

$MESS["SHOW_MORE_SUBSECTIONS"] = "Show sub-sections";
$MESS["SHOW_MORE_SUBSECTIONS_TIP"] = "When checked, nested sub-sections will be displayed one level lower";
$MESS["SHOW_SIDE_BLOCK_LAST_LEVEL"] = "Show the side menu in the last subsection of the directory";
$MESS["SHOW_SIDE_BLOCK_LAST_LEVEL_TIP"] = "With the checkbox checked, the side menu will be displayed only in the last section of the catalog. The setting works in conjunction with the condition \"Do not show \" in the parameter \"Show elements of the section \"";
$MESS["SHOW_SORT_IN_FILTER"] = "Show sorting in block with filter at resolution < 768px";

$MESS["TITLE_SLIDER_IN_SEARCH"] = "Block header with personalized recommendations";
$MESS["TITLE_SLIDER_IN_SEARCH_VALUE"] = "Recommended";
$MESS["CP_BC_TPL_USE_BIG_DATA_IN_SEARCH"] = "Show personalized recommendations in the absence of results";
$MESS["RECOMEND_IN_SEARCH_COUNT_TITLE"] = "Number of elements in the block \"Recommended\"";

$MESS["SORT_BUTTONS_CUSTOM"] = "default";

$MESS["CP_BC_TPL_PRODUCT_BLOCK_BUY_SERVICES"] = "Additional services";
$MESS["TAB_BUY_SERVICES_NAME_TITLE"] = "Block heading \"Additional services\"";
$MESS["COUNT_SERVICES_IN_ANNOUNCE"] = "Number of additional services on top";
$MESS["SHOW_ALL_SERVICES_IN_SLIDE"] = "Show other services in the announcement";

$MESS["DISPLAY_LINKED_PAGER_TITLE"] = "Show button show more for related products";
$MESS["DISPLAY_LINKED_ELEMENT_SLIDER_CROSSLINK_TITLE"] = "The number of items received for cross-selling";
$MESS["DETAIL_SET_PRODUCT_TITLE"] = "Block header \"Modules\"";
$MESS["DETAIL_SET_PRODUCT_VALUE"] = "Build a set"; 
$MESS["CP_BC_TPL_PRODUCT_BLOCK_MODULES"] = "Modules";
$MESS["MODULES_ELEMENT_COUNT"] = "Number of elements in a block \"Modules\"";
$MESS["VISIBLE_PROP_WITH_OFFER"] = "Take into account the properties of trading proposals in the output of properties from above";
$MESS["USE_LANDINGS_GROUP_TITLE"] = "Group landing pages";
$MESS["LANDINGS_GROUP_FROM_SEO_TITLE"] = "Get landing page group names from SEO";

$MESS['T_SHOW_KIT_ALL'] = "Show all goods of kit immediately";

$MESS["T_USE_COMPARE_GROUP"] = "Использовать группировку на странице сравнения";
?>