.sites {position: relative;}
.sites__select {cursor: pointer;}
.sites__select span .svg {top:0;}
.sites:hover .sites__select .svg {opacity: 1;}
.svg-inline-down.dpopdown {position:static; padding-left: 8px; padding-top: 2px;}
.sites__option {display: block; padding: 18px 22px 18px; color: var(--white_text_black); border-top: 1px solid #f0f0f0; border-color: var(--light_bg_black); }
.sites__option:hover {background-color: #fafafa; background-color: var(--light_bg_black);}
.sites__option--first {border-top: 0px;}
.sites__option--current {font-weight: bold;cursor: default;}
.sites__dropdown {position: absolute;top: 100%;opacity: 0;visibility: hidden;transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out; left:0px;}
/* .sites__dropdown--typeLang {left: 0px;}  */
.sites__dropdown--top {top: auto;bottom: 100%;padding-bottom: 10px;padding-top: 0px;}
.sites__dropdown--top .dropdown {display: flex;flex-direction: column-reverse;}
.sites__dropdown--top .sites__option--first {padding-top: 6.5px;padding-bottom: 0px;}
.sites__dropdown--top .sites__option--last{padding-top: 0px;padding-bottom: 6.5px;}
.sites:hover .sites__dropdown {opacity: 1;visibility: visible;}
.sites__dropdown .dropdown {margin: 7px 0px 0px; background-color: var(--card_bg_black); border-radius: 0 0 3px 3px; box-shadow: 0px 2px 10px 0px rgb(0 0 0 / 20%);}
.sites__icon {margin-right: 12px;margin-top: -1px;}
.sites__arrow {display: flex;margin-left: 7px;margin-top: 1px;}
.sites__current--upper{text-transform:uppercase;}
#mobilemenu .sites__select .svg {left: 20px;top: 23px;}
#mobilemenu .menu ul > li .dropdown  .sites__option {padding: 19px 20px 20px 19px;display: block;position: relative;}
#mobilemenu .sites__option {padding: 0}
.sites .sites__current {
  padding-left: 8px; 
  max-width: 170px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.long_banner .header_wrap:not(.light-menu-color) .logo_and_menu-row .sites:hover .sites__current {
  color: var(--theme-base-color); 
  /* color: var(--fill_dark_light_white_hover); */
}
.long_banner .light-menu-color .logo_and_menu-row:not(.logo_top_white) .sites__current {color:#fff}

#main:not(.theme-dark)
  .long_banner
  .header_wrap:not(.light-menu-color)
  .logo_and_menu-row
  .sites
  > *:hover
  .svg:not(.iconset_icon)
  svg
  use {
  fill: var(--theme-base-color);
  }