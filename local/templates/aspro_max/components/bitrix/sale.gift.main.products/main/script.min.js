BX.namespace("BX.Sale"),BX.Sale.GiftMainProductsClass=function(){var t=function(t){this.ajaxUrl="/bitrix/components/bitrix/sale.gift.main.products/ajax.php",this.contextAjaxData=t.contextAjaxData||{},this.mainProductState=t.mainProductState||null,this.injectId=t.injectId||null,this.isGift=!!t.isGift,this.productId=t.productId,this.offerId=t.offerId,this.offers=t.offers||[],this.setEvents(),document.location.hash.match(/as_gift/g)&&(this.isGift?this.enableGift():this.raiseNonGiftEvent()),BX.bindDelegate(BX(this.injectId),"click",{tagName:"a"},BX.proxy(this.clickNavLink,this))};return t.prototype.clickNavLink=function(t){if(this.onPageNavigationByLink(BX.proxy_context))return BX.PreventDefault(t)},t.prototype.setEvents=function(){BX.addCustomEvent("onCatalogStoreProductChange",BX.proxy(this.onCatalogStoreProductChange,this)),BX.addCustomEvent("onAddToBasketMainProduct",BX.proxy(this.onAddToBasketMainProduct,this))},t.prototype.unsubscribeEvents=function(){BX.removeCustomEvent("onCatalogStoreProductChange",BX.proxy(this.onCatalogStoreProductChange,this))},t.prototype.onAddToBasketMainProduct=function(t){this.enableGift()},t.prototype.onCatalogStoreProductChange=function(e){e!=this.offerId&&$(this.injectId).length&&BX.ajax({url:this.ajaxUrl,method:"POST",data:BX.merge(this.contextAjaxData,{offerId:e,mainProductState:this.mainProductState,SITE_ID:BX.message("SITE_ID")}),dataType:"html",processData:!1,start:!0,onsuccess:BX.delegate(function(t){this.offerId=e;var i=BX.processHTML(t);i.HTML?(this.unsubscribeEvents(),BX(this.injectId).innerHTML=i.HTML,BX.ajax.processScripts(i.SCRIPT)):document.location.hash.match(/as_gift/g)&&(this.isGift?this.raiseGiftEvent():this.raiseNonGiftEvent())},this)})},t.prototype.onPageNavigationByLink=function(t){return!!BX.delegate(function(t){return!(!BX.type.isElementNode(t)||!t.href)&&(0<=t.href.indexOf(this.ajaxUrl)||-1!==t.href.indexOf("PAGEN_"))},this)(t)&&(BX.ajax({url:t.href,method:"POST",data:BX.merge(this.contextAjaxData,{SITE_ID:BX.message("SITE_ID")}),dataType:"html",processData:!1,start:!0,onsuccess:BX.delegate(function(t){var i=BX.processHTML(t);i.HTML&&(this.unsubscribeEvents(),BX(this.injectId).innerHTML=i.HTML,BX.ajax.processScripts(i.SCRIPT))},this)}),!0)},t.prototype.enableGift=function(){this.isGift=!0,this.raiseGiftEvent()},t.prototype.raiseGiftEvent=function(){BX.onCustomEvent("onSaleProductIsGift",[this.productId,this.offerId])},t.prototype.raiseNonGiftEvent=function(){BX.onCustomEvent("onSaleProductIsNotGift",[this.productId,this.offerId])},t}(),BX.addCustomEvent("onSliderInitialized",function(t){try{ignoreResize.push(!0),t&&$(t.slider.currentTarget).closest(".bx_sale_gift_main_products").length&&($(".bx_sale_gift_main_products .catalog_block .catalog_item .item_info .item-title").sliceHeight({item:".bx_sale_gift_main_products .catalog_item:not(.big)",mobile:!0,autoslicecount:!1,slice:999}),$(".bx_sale_gift_main_products .catalog_block .catalog_item .item_info .sa_block").sliceHeight({item:".bx_sale_gift_main_products .catalog_item:not(.big)",mobile:!0,autoslicecount:!1,slice:999}),$(".bx_sale_gift_main_products .catalog_block .catalog_item .item_info .cost.prices").sliceHeight({item:".bx_sale_gift_main_products .catalog_item:not(.big)",mobile:!0,autoslicecount:!1,slice:999}),$(".bx_sale_gift_main_products .catalog_block .catalog_item").sliceHeight({classNull:".footer_button",item:".bx_sale_gift_main_products .catalog_item:not(.big)",mobile:!0,autoslicecount:!1,slice:999}))}catch(t){}finally{ignoreResize.pop()}});