function getSliderSectionsHTML(e){var i="",t="stories-popup__section-slider",s="stories-popup__section-slider-inner";return i+='<div class="'+t+'">',i+='<div class="'+t+'-loader"><svg width="48" height="48" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg" version="1.1"><path d="M 150,0 a 150,150 0 0,1 106.066,256.066 l -35.355,-35.355 a -100,-100 0 0,0 -70.711,-170.711 z" fill="#0000001a"><animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0 150 150" to="360 150 150" begin="0s" dur=".8s" fill="freeze" repeatCount="indefinite"></animateTransform></path></svg></div>',i+='<div class="'+s+'">',i+=e,i+="</div>",i+="</div>",i}function getSliderElementsHTML(e,i,t){var s="",n="stories-popup__element-slider"+(t.current?" stories-popup__element-slider--active":" stories-popup__element-slider--paused"),l="stories-popup__element-slider-header",o="stories-popup__element-slider-header-img",a="stories-popup__element-slider-header-name",r="stories-popup__element-slider-elements",d=5,c="",u="stories-popup__element-slider-panel",p="stories-popup__element-slider-panel-element";c+='<div class="'+u+'">';for(var m=0;m<i;m++)c+='<div class="'+p+(0==m?" "+p+"--active":"")+'"><div class="'+p+'-line" style="animation-duration: '+d+'s;"></div></div>';return c+="</div>",s+='<div class="'+n+'" data-section-id="'+t.ID+'">',s+='<div class="'+r+'">',s+=e,s+="</div>",s+=c,s+='<div class="'+l+'">',t.PICTURE&&(s+='<div class="'+o+'" style="background: url('+t.PICTURE+') no-repeat center;" ></div>'),s+='<div class="'+a+'">'+t.NAME+"</div>",s+="</div>",s+="</div>",s}function generateElementHtml(e,i){var t="",s="stories-popup__element"+(i>0?"":" stories-popup__element--active"),n="stories-popup__element-image",l="stories-popup__element-btn",o="stories-popup__element-slider-navs",a="stories-popup__element-slider-navs-prev",r="stories-popup__element-slider-navs-next";if(t+='<div class="'+s+'" data-id="'+e.ID+'">',t+='<div class="'+n+'" style="background: url('+e.PREVIEW_PICTURE+') no-repeat center;" ></div>',e.PROPERTY_BTN_TEXT_VALUE){var d=e.PROPERTY_BTN_LINK_VALUE?"a":"div",c=e.PROPERTY_BTN_LINK_VALUE?' href="'+e.PROPERTY_BTN_LINK_VALUE+'"':"";t+="<"+d+c+' class="btn '+l+" "+e.PROPERTY_BTN_CLASS_VALUE+'" >'+e.PROPERTY_BTN_TEXT_VALUE+"</"+d+">"}return t+='<div class="'+o+'"><div class="'+a+'"></div><div class="'+r+'"></div></div>',t+="</div>",t}function afterShowActions(e){sectionsSliderInit(e)}function sectionsSliderInit(e){var i=0,t=0;for(var s in e){if(e[s].current){t=i;break}i++}var n={popup:$(".stories-popup"),activeSlideIndex:t,activeSlideClass:"stories-popup__element-slider--active",pausedSlideClass:"stories-popup__element-slider--paused",slidesSelector:".stories-popup__element-slider",innerSelector:"> .stories-popup__section-slider-inner",activeElementClass:"stories-popup__element--active",elementsSelector:".stories-popup__element",elementNavPrevClass:"stories-popup__element-slider-navs-prev",elementNavNextClass:"stories-popup__element-slider-navs-next",elementPanelClass:"stories-popup__element-slider-panel-element",elementPanelActiveClass:"stories-popup__element-slider-panel-element--active",elementPanelFinishedClass:"stories-popup__element-slider-panel-element--finishied",changeSlideTime:5e3,transition:.25,activeSlideMargin:8,dragAmount:150,mobile:!1,breakpoints:{"(max-width: 1025px)":{activeSlideMargin:4},"(max-width: 566px)":{mobile:!0}}};InitAsproStoriesSectionSlider($(".stories-popup__section-slider"),n)}function generateSectionsHtml(e,i){var t="";t+=getPopupCloseIcon();var s="";for(var n in e){var l=e[n];l.ID==i.sectionId&&(l.current=!0),s+=generateElementsHtml(l)}return t+=getSliderSectionsHTML(s),t}function generateElementsHtml(e){var i="",t="",s=e.CHILDS;t="";for(var n in s){var l=s[n];t+=generateElementHtml(l,n)}return i+=getSliderElementsHTML(t,s.length,e),i}function getPopupCloseIcon(){var e="stories-popup__close",i='<div class="'+e+'"><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 1L13 13M13 1L1 13" stroke="white" stroke-width="2" stroke-linecap="round"/></svg></div>';return i}function InitAsproStoriesSectionSlider(e,i){e.asproStoriesSectionsSlider(i)}$(document).on("touchstart",".front_stories .item",function(e){window.touchStoriesX=e.originalEvent.changedTouches[0].clientX,window.touchStoriesY=e.originalEvent.changedTouches[0].clientY,setTimeout(function(){window.touchStoriesX=!1,window.touchStoriesY=!1},300)}),$(document).on("touchend",".front_stories .item",function(e){window.touchStoriesX==e.originalEvent.changedTouches[0].clientX&&window.touchStoriesY==e.originalEvent.changedTouches[0].clientY&&$(this).trigger("click")}),document.addEventListener("keydown",function(e){27==e.keyCode&&$(".stories-popup").click()}),$(document).on("click",".front_stories .item",function(){if(!window.asproStoriesLoading){window.asproStoriesLoading=!0;var e=$(this),i=$("body > .stories-popup");i.length||(i=$('<div class="stories-popup"></div>'));var t={sectionData:e.data(),sortData:e.closest(".front_stories").data()};$.ajax({url:arAsproOptions.SITE_DIR+"ajax/storiesInfo.php",data:t,dataType:"json",type:"POST",success:function(t){if(t&&t.error)console.log("Fail get stories! Reason: "+t.error);else if(t&&"object"==typeof t){var s=generateSectionsHtml(t,e.data());s&&(i.html(s),i.addClass("stories-popup--visible"),$("body").append(i),afterShowActions(t))}else console.log("Fail get stories! Reason: result is "+t);window.asproStoriesLoading=!1},error:function(e,i,t){console.log("Fail get stories! Reason: "+t),window.asproStoriesLoading=!1}})}}),$(document).on("click",".stories-popup__close",function(){var e=$(this),i=e.parents(".stories-popup");i.remove()}),$(document).on("click",".stories-popup",function(e){var i=$(this),t=$(e.target);if(t.hasClass("stories-popup")){var s=i.closest(".stories-popup");s.remove()}}),$(document).on("click",".front_stories .top_block a",function(e){e.preventDefault(),$(".front_stories .item").eq(0).trigger("click")}),$.fn.asproStoriesSectionsSlider=function(e){function i(e,i){var t=$(e),s=Object.assign({},i);for(var n in i.breakpoints)window.matchMedia(n).matches&&(s=Object.assign(s,i.breakpoints[n]));s.setStyles=function(){s.sliderInner.css({display:"flex","align-items":"center",transition:"margin-left "+s.transition+"s ease-in-out","margin-left":s.calculateMargin(s.activeSlideIndex)}),s.slider.css({overflow:"hidden"})},s.getCurrentSizes=function(){s.sliderWidth=s.slider.outerWidth(!0),s.sliderHeight=s.slider.outerHeight(!0),s.updateSlidesHeight()},s.calculateMargin=function(e){s.getCurrentSizes(),s.mobile?s.sliderInnerMargin=-s.slideWidth*(e+1)+s.sliderWidth/2+s.slideWidth/2:s.sliderInnerMargin=-s.slideWidth*(e+1)+s.sliderWidth/2+s.slideWidth/2-s.sliderWidth/100*s.activeSlideMargin},s.setCenter=function(e){s.calculateMargin(e),s.sliderInner.css({"margin-left":s.sliderInnerMargin})},s.closePopup=function(){s.popup.remove()},s.setActive=function(e){s.activeSlide.removeClass(s.activeSlideClass).addClass(s.pausedSlideClass),$(s.activeSlide.panelElements[s.activeElements[s.activeSlideIndex]]).removeClass(s.elementPanelActiveClass),s.activeSlideIndex=e,s.activeSlide=$(s.slides[s.activeSlideIndex]),s.activeSlide.addClass(s.activeSlideClass).removeClass(s.pausedSlideClass),s.activeSlide.elements=null,s.activeSlide.panelElements=null,s.updateElementsInfo(s.activeSlide),s.setActiveElement(s.activeSlide,0),s.setCenter(e)},s.updateElementsInfo=function(e){e.elements&&e.panelElements||(e.elements=e.find(s.elementsSelector),e.panelElements=e.find("."+s.elementPanelClass)),s.activeElements[e.index()]||(s.activeElements[e.index()]=0)},s.setNext=function(){s.activeSlideIndex+1<s.slides.length?s.setActive(s.activeSlideIndex+1):s.closePopup()},s.setPrev=function(){s.activeSlideIndex>0&&s.setActive(s.activeSlideIndex-1)},s.setNextElement=function(){s.updateElementsInfo(s.activeSlide),s.elementTimerContinue=null,s.activeElements[s.activeSlideIndex]+1<s.activeSlide.elements.length?s.setActiveElement(s.activeSlide,1):(s.clearSlide(s.activeSlide),s.setNext())},s.setPrevElement=function(){s.updateElementsInfo(s.activeSlide),s.activeElements[s.activeSlideIndex]>0?s.setActiveElement(s.activeSlide,-1):(s.clearSlide(s.activeSlide),s.setPrev())},s.clearSlide=function(e){},s.setActiveElement=function(e,i){s.elementTimer&&clearTimeout(s.elementTimer);var t=e.elements[s.activeElements[s.activeSlideIndex]],n=e.elements[s.activeElements[s.activeSlideIndex]+i];$(t).removeClass(s.activeElementClass),$(n).addClass(s.activeElementClass),s.activeElements[e.index()]+=i,e.panelElements.each(function(i,t){var n=$(t);i<s.activeElements[e.index()]?(n.addClass(s.elementPanelFinishedClass),n.removeClass(s.elementPanelActiveClass)):i==s.activeElements[e.index()]?(n.removeClass(s.elementPanelFinishedClass),n.addClass(s.elementPanelActiveClass)):(n.removeClass(s.elementPanelActiveClass),n.removeClass(s.elementPanelFinishedClass))}),window.asproStoriesSliderStopped||(s.elementTimerStart=Date.now(),s.elementTimer=setTimeout(function(){s.setNextElement()},s.changeSlideTime))},s.pauseSlide=function(e){var i=e.data("index");i=i||e.index(),e.addClass(s.pausedSlideClass),s.stoppedSlideIndex=i,s.elementTimer&&(clearTimeout(s.elementTimer),s.animationTimer=s.elementTimerContinue?s.elementTimerContinue:s.changeSlideTime,s.elementTimerContinue=s.animationTimer-(Date.now()-s.elementTimerStart))},s.playSlide=function(e){var i=e.data("index");i=i||e.index(),i==s.stoppedSlideIndex&&(e.removeClass(s.pausedSlideClass),s.stoppedSlideIndex=null,window.asproStoriesSliderStopped||(s.elementTimerStart=Date.now(),s.elementTimer=setTimeout(function(){s.setNextElement()},s.elementTimerContinue)))},s.addDragEvents=function(){s.slides.on("touchstart",function(e){var i=$(this),t=i.data("index");t=t||i.index(),t==s.activeSlideIndex&&(s.mobile||(s.touch.posPrev=e.originalEvent.changedTouches[0].pageX))}),s.slides.on("touchmove",function(e){var i=$(this),t=i.data("index");t=t||i.index(),t==s.activeSlideIndex&&(s.mobile||(s.touch.posCurrent=e.originalEvent.changedTouches[0].pageX-s.touch.posPrev,s.sliderInner.css({"margin-left":s.sliderInnerMargin+s.touch.posCurrent}),s.sliderInnerMargin=s.sliderInnerMargin+s.touch.posCurrent,s.touch.posPrev=e.originalEvent.changedTouches[0].pageX))}),s.slides.on("mousedown",function(e){var i=$(this),t=i.data("index");if(t=t||i.index(),t==s.activeSlideIndex&&!s.mobile){var n=e.pageX;function l(e){var t=e-n>0?e-n:n-e,l=t/s.dragAmount;if(e-n>0&&s.activeSlideIndex>0||e-n<0&&s.activeSlideIndex<s.slides.length){var o=e-n>0?i.prev():i.next(),a={"margin-left":s.activeSlideMargin-s.activeSlideMargin*l+"vw","margin-right":s.activeSlideMargin-s.activeSlideMargin*l+"vw",transform:"scale("+(1-.25*l)+")"},r={"margin-left":s.activeSlideMargin*l+"vw","margin-right":s.activeSlideMargin*l+"vw",transform:"scale("+(.75+.25*l)+")"};i.css(a),o.css(r),console.log(l)}}function o(e){l(e.pageX)}document.addEventListener("mousemove",o),$(document).on("mouseup",function(){document.removeEventListener("mousemove",o),this.onmouseup=null}),this.ondragstart=function(){return!1}}})},s.addEvents=function(){s.slides.on("click",function(){var e=$(this),i=e.data("index");i=i||e.index(),i!=s.activeSlideIndex&&s.setActive(i)}),s.slides.swiperight(function(){s.mobile&&s.setPrev()}),s.slides.swipeleft(function(){s.mobile&&s.setNext()}),s.slides.on("taphold",function(){var e=$(this);s.pauseSlide(e)}),s.slides.on("touchend",function(){var e=$(this);s.playSlide(e)}),s.slides.on("mousedown",function(){var e=$(this),i=1e3;s.mousedownTimer=setTimeout(function(){s.mouseHold=!0,s.pauseSlide(e)},i)}),s.slides.on("mousemove",function(){s.mousedownTimer&&(clearTimeout(s.mousedownTimer),s.mouseHold=!1)}),s.slides.on("mouseup",function(e){if(s.mouseHold){var i=$(this);s.playSlide(i)}else s.mousedownTimer&&(clearTimeout(s.mousedownTimer),s.mouseHold=!1)}),s.slides.on("click","."+s.elementNavPrevClass,function(e){if(s.mouseHold)s.mouseHold=!1;else{var i=$(this),t=i.closest(s.slidesSelector),n=t.data("index");n=n||t.index(),n==s.activeSlideIndex&&(s.setPrevElement(),e.preventDefault(),e.stopPropagation())}}),s.slides.on("click","."+s.elementNavNextClass,function(e){if(s.mouseHold)s.mouseHold=!1;else{var i=$(this),t=i.closest(s.slidesSelector),n=t.data("index");n=n||t.index(),n==s.activeSlideIndex&&(s.setNextElement(),e.preventDefault(),e.stopPropagation())}}),BX.addCustomEvent("onWindowResize",function(e){try{s.update()}catch(e){console.log(e)}})},s.updateSlidesHeight=function(){s.mobile?(s.slideWidth=s.sliderWidth,s.slides.css({width:""})):(s.slideWidth=(s.sliderHeight+80)/1.98,s.slides.css({width:s.slideWidth}))},s.update=function(){var e=s.activeSlideIndex;for(var t in s=Object.assign(s,i),i.breakpoints)window.matchMedia(t).matches&&(s=Object.assign(s,i.breakpoints[t]));s.activeSlideIndex=e,s.getCurrentSizes(),s.setCenter(s.activeSlideIndex)},s.init=function(e){s.slides=e.find(s.slidesSelector),s.activeSlide=$(s.slides[s.activeSlideIndex]),s.sliderInner=e.find(s.innerSelector),s.slider=e,s.sliderWidth=s.slider.outerWidth(!0),s.slides.addClass("swipeignore"),s.touch={},s.activeElements={},s.setStyles(),s.getCurrentSizes(),s.setCenter(s.activeSlideIndex),s.updateElementsInfo(s.activeSlide),s.setActiveElement(s.activeSlide,0),s.addEvents(),e.data("asproStoriesSlider",s)},s.init(t),setTimeout(function(){s.slider.addClass("aspro_slider_init")},1e3*s.transition+10)}var t=$(this);if(t.hasClass("aspro_slider_init"))return!1;t.each(function(t,s){i(s,e)})};