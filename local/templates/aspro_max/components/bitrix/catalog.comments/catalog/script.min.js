!function(t){t.JCCatalogSocnetsComments||(t.JCCatalogSocnetsComments=function(t){var s;if(this.errorCode=0,this.params={},this.serviceList={blog:!1,facebook:!1,vk:!1},this.settings={blog:{ajaxUrl:"",ajaxParams:{},contID:"bx-cat-soc-comments-blg"},facebook:{contID:"bx-cat-soc-comments-fb",contWidthID:"",parentContID:"soc_comments",facebookJSDK:"facebook-jssdk",facebookPath:""},vk:{}},this.services={blog:{obBlogCont:null},facebook:{obFBCont:null,obFBContWidth:null,obFBParentCont:null,obFBjSDK:null,currentWidth:0}},this.activeTabId="",this.currentTab=-1,this.tabsContId="",this.tabList=[],this.obTabList=[],"object"==typeof t){if(this.params=t,this.params.serviceList&&"object"==typeof this.params.serviceList)for(s in this.serviceList)this.serviceList.hasOwnProperty(s)&&this.params.serviceList[s]&&(this.serviceList[s]=!0);this.serviceList.blog&&this.initParams("blog"),this.serviceList.facebook&&this.initParams("facebook"),"object"==typeof this.params.tabs&&(this.activeTabId=this.params.tabs.activeTabId,this.tabsContId=this.params.tabs.tabsContId,this.tabList=this.params.tabs.tabList)}else this.errorCode=-1;0===this.errorCode&&BX.ready(BX.proxy(this.Init,this))},t.JCCatalogSocnetsComments.prototype.initParams=function(t){var s;if(this.params.settings&&"object"==typeof this.params.settings&&"object"==typeof this.params.settings[t])for(s in this.settings[t])this.settings[t].hasOwnProperty(s)&&this.params.settings[t][s]&&(this.settings[t][s]=this.params.settings[t][s])},t.JCCatalogSocnetsComments.prototype.Init=function(){if(this.tabList&&BX.type.isArray(this.tabList)&&0!==this.tabList.length){var t,s;for(t=0;t<this.tabList.length;t++){if(s=this.tabsContId+this.tabList[t],this.obTabList[t]={id:this.tabList[t],tabId:s,contId:s+"_cont",tab:BX(s),cont:BX(s+"_cont")},!this.obTabList[t].tab||!this.obTabList[t].cont){this.errorCode=-2;break}this.activeTabId===this.tabList[t]&&(this.currentTab=t),BX.bind(this.obTabList[t].tab,"click",BX.proxy(this.onClick,this))}this.serviceList.blog&&(this.services.blog.obBlogCont=BX(this.settings.blog.contID),this.services.blog.obBlogCont||(this.serviceList.blog=!1,this.errorCode=-16)),this.serviceList.facebook&&(this.services.facebook.obFBCont=BX(this.settings.facebook.contID),this.services.facebook.obFBCont?this.services.facebook.obFBContWidth=this.services.facebook.obFBCont.firstChild:(this.serviceList.facebook=!1,this.errorCode=-32),this.services.facebook.obFBParentCont=BX(this.settings.facebook.parentContID)),0===this.errorCode&&(this.showActiveTab(),this.serviceList.blog&&this.loadBlog(),this.serviceList.facebook&&this.loadFB()),this.params={}}else this.errorCode=-1},t.JCCatalogSocnetsComments.prototype.loadBlog=function(){var t;0===this.errorCode&&this.serviceList.blog&&0!==this.settings.blog.ajaxUrl.length&&((t=this.settings.blog.ajaxParams).sessid=BX.bitrix_sessid(),BX.ajax({timeout:30,method:"POST",dataType:"html",url:this.settings.blog.ajaxUrl,data:t,onsuccess:BX.proxy(this.loadBlogResult,this)}))},t.JCCatalogSocnetsComments.prototype.loadBlogResult=function(t){BX.type.isNotEmptyString(t)&&BX.adjust(this.services.blog.obBlogCont,{html:t})},t.JCCatalogSocnetsComments.prototype.loadFB=function(){var t,s,i,o,e,a,r;this.services.facebook.obFBParentCont&&this.services.facebook.obFBContWidth&&(t=parseInt(this.services.facebook.obFBParentCont.offsetWidth,10),isNaN(t)||(BX.adjust(this.services.facebook.obFBContWidth,{attrs:{"data-width":t-20}}),this.services.facebook.currentWidth=t),this.services.facebook.obFBjSDK||(this.services.facebook.obFBjSDK=!0,BX.defer(BX.proxy((s=document,i="script",o=this.settings.facebook.facebookJSDK,e=this.settings.facebook.facebookPath,r=s.getElementsByTagName(i)[0],void(s.getElementById(o)||((a=s.createElement(i)).id=o,a.src=e,r.parentNode.insertBefore(a,r)))),this))))},t.JCCatalogSocnetsComments.prototype.getFBParentWidth=function(){var t=0;return this.services.facebook.obFBParentCont&&(t=parseInt(this.services.facebook.obFBParentCont.offsetWidth,10),isNaN(t)&&(t=0)),t},t.JCCatalogSocnetsComments.prototype.setFBWidth=function(t){var s,i=null;this.serviceList.facebook&&this.services.facebook.currentWidth!==t&&20<t&&this.services.facebook.obFBContWidth&&this.services.facebook.obFBContWidth.firstChild&&this.services.facebook.obFBContWidth.firstChild.fitrstChild&&(i=this.services.facebook.obFBContWidth.firstChild.fitrstChild)&&(s=i.getAttribute("src").replace(/width=(\d+)/gi,"width="+t),BX.adjust(this.services.facebook.obFBContWidth,{attrs:{"data-width":t-20}}),this.services.facebook.currentWidth=t,BX.style(this.services.facebook.obFBContWidth.firstChild,"width",t+"px"),BX.adjust(i,{attrs:{src:s},style:{width:t+"px"}}))},t.JCCatalogSocnetsComments.prototype.onResize=function(){this.serviceList.facebook&&this.setFBWidth(this.getFBParentWidth())},t.JCCatalogSocnetsComments.prototype.onClick=function(){var t,s=BX.proxy_context,i=-1;for(t=0;t<this.obTabList.length;t++)if(s.id===this.obTabList[t].tabId){i=t;break}-1<i&&i!==this.currentTab&&(this.hideActiveTab(),this.currentTab=i,this.showActiveTab())},t.JCCatalogSocnetsComments.prototype.hideActiveTab=function(){BX.removeClass(this.obTabList[this.currentTab].tab,"active"),BX.addClass(this.obTabList[this.currentTab].cont,"tab-off"),BX.addClass(this.obTabList[this.currentTab].cont,"hidden")},t.JCCatalogSocnetsComments.prototype.showActiveTab=function(){BX.onCustomEvent("onAfterBXCatTabsSetActive_"+this.tabsContId,[{activeTab:this.obTabList[this.currentTab].id}]),BX.addClass(this.obTabList[this.currentTab].tab,"active"),BX.removeClass(this.obTabList[this.currentTab].cont,"tab-off"),BX.removeClass(this.obTabList[this.currentTab].cont,"hidden")})}(window);