.vacancy_desc {
  margin: 0 0 47px;
}
.vacancy_desc .image img {
  width: 100%;
}
.vacancy_desc .properties {
  margin: 0;
  padding: 40px 39px 25px;
  background: #fafafa;
  background-color: var(--card_bg_black);
}
.vacancy_desc .properties .button_wrap {
  position: relative;
  z-index: 1;
}
.vacancy_desc .properties > .wrap {
  position: relative;
  top: -6px;
  padding: 0 0 0 230px;
}
.vacancy_desc .properties > .wrap.wtform {
  padding-left: 0;
}
.vacancy_desc .properties .property {
  margin: 8px 0;
}
.vacancy_desc .properties .property .title-prop {
  margin: 0 0 3px;
}
.vacancy_desc .properties .property .value {
  font-size: 1em;
  line-height: 1.4375em;
}
.vacancy_desc .detailtext {
  margin: 20px 0 0;
}
@media (max-width: 600px) {
  .vacancy_desc .properties > .wrap {
    padding-left: 0;
  }
  .vacancy_desc .properties .button_wrap {
    float: none !important;
    margin: 0 0 35px;
  }
}
