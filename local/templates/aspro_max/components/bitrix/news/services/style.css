.buy_services .counter_wrapp .button_block:not(.to-cart) svg,
.services_buy_block .counter_wrapp .button_block:not(.to-cart) svg {
  display: none;
}
.buy_services .counter_wrapp .counter_block {
  background: #ffffff;
  background: var(--light_bg_black);
}

.order-block.buy_services .counter_wrapp .counter_block .text {
  margin-top: 0;
}
.order-block.buy_services .counter_wrapp.wquest {
  padding-right: 58px;
}
.order-block.buy_services .btn.question {
  margin-left: 6px;
}
.order-block.buy_services .btns-col {
  padding-left: 7px;
}
.order-block.buy_services .counter_wrapp {
  padding-top: 0;
}

/*services list*/
.services_buy_block .counter_wrapp.list > div {
  float: none;
  width: auto;
}
.services_buy_block .counter_block_inner {
  vertical-align: top;
  min-width: 114px;
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 10px;
}
.services_buy_block .button_block {
  min-width: 120px;
  margin-bottom: 10px;
}
.services_buy_block .counter_wrapp {
  white-space: normal;
  max-width: 100%;
  padding-top: 10px;
  margin-bottom: -10px;
}

/*.services_buy_block .button_block .btn-sm{font-size: 11px;}
.services_buy_block .counter_block:not(.big),
.services_buy_block .counter_block:not(.big) input[type="text"]{height: 36px;}*/

.order_service_in_list {
  margin-top: 10px;
}
.services_buy_block .counter_wrapp .button_block.wide .btn.in-cart {
  width: auto;
  display: inline-block;
}

/**/

@media (min-width: 1350px) {
  .with_left_block .order-block.buy_services .text-col {
    width: 66.6%;
  }
  .with_left_block .order-block.buy_services .btns-col {
    width: 33.3%;
  }
}

@media (min-width: 992px) and (max-width: 1349px) {
  .with_left_block .order-block.buy_services .text-col {
    width: 50%;
  }
  .with_left_block .order-block.buy_services .btns-col {
    width: 50%;
  }
}

@media (min-width: 992px) {
  .order-block.buy_services .text-col {
    width: 66.6%;
  }
  .order-block.buy_services .btns-col {
    width: 33.3%;
  }
}

@media (max-width: 991px) and (min-width: 768px) {
  .order-block.buy_services .text-col {
    width: 50%;
  }
  .order-block.buy_services .btns-col {
    width: 50%;
  }
}

@media (max-width: 767px) {
  .order-block.buy_services .btns-col .counter_block_inner {
    vertical-align: top;
    min-width: 132px;
    display: inline-block;
  }
  .order-block.buy_services .btns-col .button_block {
    min-width: 130px;
  }
  .order-block.buy_services .btns-col .counter_wrapp {
    white-space: normal;
    max-width: 100%;
  }
  .order-block.buy_services .counter_wrapp.wquest {
    padding-right: 0;
  }
  .order-block.buy_services .counter_wrapp.list > div {
    float: none;
    width: auto;
  }
  .order-block.buy_services .prices {
    padding-right: 0;
  }
  .order-block.buy_services .btns-col .quest_btn {
    padding-right: 6px;
  }
  .order-block.buy_services .btns-col {
    padding-left: 7px;
    padding-right: 7px;
  }
  .order-block.buy_services .btns-col .counter_wrapp.list.big {
    margin-left: 0;
  }

  .order-block.buy_services .button_block.wide {
    display: inline-block;
  }
  .order-block.buy_services .counter_wrapp .button_block.wide .btn.in-cart {
    width: auto;
    display: inline-block;
  }
}
