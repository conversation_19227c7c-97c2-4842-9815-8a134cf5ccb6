$(document).ready(function(){$(".sidebar_menu .sidebar_menu_inner .menu-wrapper").mCustomScrollbar({mouseWheel:{scrollAmount:150,preventDefault:!0}}),$(".sidebar_menu .sidebar_menu_inner .menu-wrapper .menu_top_block .menu > li.v_hover > .dropdown-block > .dropdown-inner").mCustomScrollbarDeferred({mouseWheel:{scrollAmount:150,preventDefault:!0}}),$(document).on("mouseenter",".menu-wrapper .menu_top_block .menu > li.v_hover",function(){var e=$(this),s=e.find("> .dropdown-block"),c=e.find(".dropdown-block .dropdown-inner"),d=$(window).height();c.css("max-height","none"),s.find(".mCustomScrollBox").css("max-height","none");var o=BX.pos(s[0],!0);o.height?e.hasClass("m_line")&&d-o.top<200&&d<o.bottom&&(s.removeAttr("style"),s.css("margin-top","-"+(o.bottom-d+e.height())+"px"),o=BX.pos(e.find(".dropdown-block")[0],!0)):c.css("max-height","none"),s.velocity("stop").velocity({opacity:"1",display:"block"},{duration:170,delay:200,begin:function(){var e=$("#headerfixed.fixed"),o=0,t=Number.parseInt(s.css("margin-top"));e.length&&(o=e[0].getBoundingClientRect().height);var n=s[0].getBoundingClientRect();n.y<o&&s.css("top",-n.y+o-t),(n=s[0].getBoundingClientRect()).bottom>d&&(s.css("bottom","0"),n.height>d-o&&s.css("top",o-t));var i=s[0].getBoundingClientRect().height-s.find("> .title")[0].getBoundingClientRect().height;c[0].getBoundingClientRect().height>i&&c.css("max-height",i)},complete:function(){$("body").addClass("menu-hovered"),$(".shadow-block").length||$('<div class="shadow-block"></div>').appendTo($("body")),$(".shadow-block").velocity("stop").velocity("fadeIn",200)}}),e.one("mouseleave",function(){s.velocity("stop").velocity({opacity:"0"},{duration:100,delay:10,complete:function(){s.css("top",""),s.css("bottom",""),s.css("margin-top",""),$(".shadow-block").velocity("stop").velocity("fadeOut",{duration:200,complete:function(){$("body").removeClass("menu-hovered")}})}})})})});