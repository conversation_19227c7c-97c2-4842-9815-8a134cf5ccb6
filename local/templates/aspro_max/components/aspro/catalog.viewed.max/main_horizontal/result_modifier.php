<?
$filteredViewedProducts = [];
$productsIds = array_column($arResult["ITEMS"], "ID");
$resStore = CCatalogStoreProduct::GetList([], ['PRODUCT_ID' => $productsIds, 'STORE_ID' => $arParams["STORE_ID"]]);
while ($storeLine = $resStore->GetNext()) {
    if ($storeLine['AMOUNT'] && $storeLine['AMOUNT'] > 0) {
        $availableProductKey = array_search($storeLine['PRODUCT_ID'], $productsIds);
        $filteredViewedProducts[] = $arResult["ITEMS"][$availableProductKey];
    }
}
$arResult["ITEMS"] = $filteredViewedProducts;
?>