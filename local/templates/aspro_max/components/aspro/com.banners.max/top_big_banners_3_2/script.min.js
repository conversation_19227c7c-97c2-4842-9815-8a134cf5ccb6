function checkNavColor(e){var a=e.find(".flex-active-slide").data("nav_color");e.find(".flex-active-slide").data("text_color");"dark"==a?e.find(".flex-control-nav").addClass("flex-dark"):e.find(".flex-control-nav").removeClass("flex-dark");var i={slider:e};BX.onCustomEvent("onSlide",[i])}function checkHeight(){}$(document).ready(function(){if($(".top_slider_wrapp .flexslider").length){var e={controlNav:!0,animationLoop:!0,pauseOnHover:!0,simulateTouch:!0,autoHeight:!0};if(void 0!==arMaxOptions.THEME){var a=Math.abs(parseInt(arMaxOptions.THEME.BIGBANNER_SLIDESSHOWSPEED)),i=Math.abs(parseInt(arMaxOptions.THEME.BIGBANNER_ANIMATIONSPEED)),o=0;e.slideshow=!(!a||!arMaxOptions.THEME.BIGBANNER_ANIMATIONTYPE.length),e.animation="FADE"===arMaxOptions.THEME.BIGBANNER_ANIMATIONTYPE?"fade":"slide",i>=0&&(e.animationSpeed=i),a>=0&&(e.slideshowSpeed=a),"FADE"!==arMaxOptions.THEME.BIGBANNER_ANIMATIONTYPE&&(e.direction="SLIDE_VERTICAL"===arMaxOptions.THEME.BIGBANNER_ANIMATIONTYPE?"vertical":"horizontal"),$(".top_slider_wrapp .flexslider").find("ul.slides li.box").length<2&&(e.animationLoop=!1),"CURRENT_BANNER_INDEX"in arMaxOptions&&arMaxOptions.CURRENT_BANNER_INDEX&&(o=arMaxOptions.CURRENT_BANNER_INDEX-1,o<$(".top_slider_wrapp .flexslider .slides > li").length&&(e.startAt=o,e.slideshow=!1)),e.start=function(e){checkNavColor(e);var a=e.find(".box.wvideo.flex-active-slide");a.length&&"1"==a.data("video_autoplay")&&BX.onCustomEvent("onSlide",[{slider:e}]),e.count<=1&&e.find(".flex-direction-nav li").addClass("flex-disabled"),$(e).find(".flex-control-nav").css("opacity",1),e.find(".flex-active-slide").removeClass("bx-context-toolbar-empty-area")},e.after=function(e){InitLazyLoad()},e.before=function(e){setTimeout(function(){checkNavColor(e),InitLazyLoad(),e.find(".flex-active-slide").removeClass("bx-context-toolbar-empty-area")},100)}}$(".top_slider_wrapp .flexslider").appear(function(){var a=$(this);a.flexslider(e)},{accX:0,accY:150})}checkHeight(),BX.addCustomEvent("onWindowResize",function(e){try{ignoreResize.push(!0),checkHeight(),CoverPlayerHtml()}catch(e){}finally{ignoreResize.pop()}})});