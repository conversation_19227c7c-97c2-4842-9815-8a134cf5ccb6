<?
$MESS["IBLOCK_PRICES"] = "Цены";
$MESS["IBLOCK_TYPE"] = "Тип инфоблока";
$MESS["IBLOCK_IBLOCK"] = "Инфоблок";
$MESS["IBLOCK_PROPERTY"] = "Свойства";
$MESS["IBLOCK_SORT_ASC"] = "по возрастанию";
$MESS["IBLOCK_SORT_DESC"] = "по убыванию";
$MESS["IBLOCK_SECTION_URL"] = "URL, ведущий на страницу с содержимым раздела";
$MESS["IBLOCK_LINE_ELEMENT_COUNT"] = "Количество элементов выводимых в одной строке таблицы";
$MESS["IBLOCK_ELEMENT_SORT_FIELD"] = "По какому полю сортируем элементы";
$MESS["IBLOCK_ELEMENT_SORT_ORDER"] = "Порядок сортировки элементов";
$MESS["IBLOCK_ELEMENT_SORT_FIELD2"] = "Поле для второй сортировки элементов";
$MESS["IBLOCK_ELEMENT_SORT_ORDER2"] = "Порядок второй сортировки элементов";
$MESS["IBLOCK_PRICE_CODE"] = "Тип цены";
$MESS["IBLOCK_BASKET_URL"] = "URL, ведущий на страницу с корзиной покупателя";
$MESS["IBLOCK_FILTER_NAME_IN"] = "Имя массива со значениями фильтра для фильтрации элементов";
$MESS["IBLOCK_CACHE_FILTER"] = "Кешировать при установленном фильтре";
$MESS["IBLOCK_SECTION_ID"] = "ID раздела";
$MESS["IBLOCK_SECTION_CODE"] = "Код раздела";
$MESS["IBLOCK_PAGE_ELEMENT_COUNT"] = "Количество элементов на странице";
$MESS["IBLOCK_ACTION_VARIABLE"] = "Название переменной, в которой передается действие";
$MESS["IBLOCK_PRODUCT_ID_VARIABLE"] = "Название переменной, в которой передается код товара для покупки";
$MESS["IBLOCK_SECTION_ID_VARIABLE"] = "Название переменной, в которой передается код группы";
$MESS["IBLOCK_USE_PRICE_COUNT"] = "Использовать вывод цен с диапазонами";
$MESS["IBLOCK_SHOW_PRICE_COUNT"] = "Выводить цены для количества";
$MESS["IBLOCK_DETAIL_URL"] = "URL, ведущий на страницу с содержимым элемента раздела";
$MESS["IBLOCK_VAT_INCLUDE"] = "Включать НДС в цену";
$MESS["T_IBLOCK_DESC_PAGER_CATALOG"] = "Товары";
$MESS["T_IBLOCK_DESC_DISPLAY_COMPARE"] = "Выводить кнопку сравнения";
$MESS["T_IBLOCK_DESC_KEYWORDS"] = "Установить ключевые слова страницы из свойства";
$MESS["T_IBLOCK_DESC_DESCRIPTION"] = "Установить описание страницы из свойства";
$MESS["CP_BCS_INCLUDE_SUBSECTIONS"] = "Показывать элементы подразделов раздела";
$MESS["CP_BCS_SHOW_ALL_WO_SECTION"] = "Показывать все элементы, если не указан раздел";
$MESS["CP_BCS_SET_STATUS_404"] = "Устанавливать статус 404, если не найдены элемент или раздел";
$MESS["CP_BCS_ADD_SECTIONS_CHAIN"] = "Включать раздел в цепочку навигации";
$MESS["CP_BCS_BROWSER_TITLE"] = "Установить заголовок окна браузера из свойства";
$MESS["CP_BCS_CACHE_GROUPS"] = "Учитывать права доступа";
$MESS["CP_BCS_PRODUCT_QUANTITY_VARIABLE"] = "Название переменной, в которой передается количество товара";
$MESS["CP_BCS_PRODUCT_PROPS_VARIABLE"] = "Название переменной, в которой передаются характеристики товара";
$MESS["CP_BCS_USE_PRODUCT_QUANTITY"] = "Разрешить указание количества товара";
$MESS["CP_BCS_PRODUCT_PROPERTIES"] = "Характеристики товара";
$MESS["CP_BCS_SECTION_USER_FIELDS"] = "Свойства раздела";
$MESS["SHOW_MEASURE_NAME"] = "Отображать единицы измерения";
$MESS["SHOW_OLD_PRICE_NAME"] = "Отображать старую цену";
$MESS["SHOW_DISCOUNT_PERCENT_NAME"] = "Отображать экономию";
$MESS["SHOW_DISCOUNT_PERCENT_NUMBER_NAME"] = "Отображать процент экономии";
$MESS["SHOW_DISCOUNT_TIME"] = "Отображать срок действия скидки";
$MESS["TABS_CODE"] = "Свойства табов";
$MESS["FILTER_PROP_CODE"] = "Свойство отбора";
$MESS["SHOW_RATING"] = "Отображать рейтинг";
$MESS["SALE_STIKER"] = "Свойство со стикером акций";
$MESS["DISPLAY_WISH_BUTTONS_NAME"] = "Показывать добавление в отложенные";
$MESS["CP_BCS_OFFERS_FIELD_CODE"] = "Поля предложений";
$MESS["CP_BCS_OFFERS_PROPERTY_CODE"] = "Свойства предложений";
$MESS["CP_BCS_OFFERS_SORT_FIELD"] = "По какому полю сортируем предложения товара";
$MESS["CP_BCS_OFFERS_SORT_ORDER"] = "Порядок сортировки предложений товара";
$MESS["CP_BCS_OFFERS_SORT_FIELD2"] = "Поле для второй сортировки предложений товара";
$MESS["CP_BCS_OFFERS_SORT_ORDER2"] = "Порядок второй сортировки предложений товара";
$MESS["CP_BCS_OFFERS_CART_PROPERTIES"] = "Свойства предложений, добавляемые в корзину";
$MESS["CP_BCS_CONVERT_CURRENCY"] = "Показывать цены в одной валюте";
$MESS["CP_BCS_CURRENCY_ID"] = "Валюта, в которую будут сконвертированы цены";
$MESS["CP_BCS_OFFERS_LIMIT"] = "Максимальное количество предложений для показа (0 - все)";
$MESS["CP_BCS_INCLUDE_SUBSECTIONS_ALL"] = "всех подразделов";
$MESS["CP_BCS_INCLUDE_SUBSECTIONS_ACTIVE"] = "активных подразделов";
$MESS["CP_BCS_INCLUDE_SUBSECTIONS_NO"] = "не показывать";
$MESS["T_TITLE_BLOCK"] = "Заголовок блока";
$MESS ['T_TITLE_BLOCK_DETAIL_NAME'] = "Заголовок блока в детальном элементе";
$MESS ['T_TITLE_BLOCK_ALL'] = "Заголовок на все новости";
$MESS ['BLOCK_NAME'] = "Новости";
$MESS ['BLOCK_ALL_NAME'] = "Все новости";
$MESS ['T_ALL_URL'] = "Ссылка на все новости";

$MESS["IBLOCK_TYPE_TIP"] = "Из выпадающего списка выбирается один из созданных в системе типов инфоблоков. После нажатия кнопки <i><b>ок</b></i> будут подгружены инфоблоки, созданные для выбранного типа.";
$MESS["IBLOCK_ID_TIP"] = "Выбирается один из инфоблоков установленного типа. Если пункт (другое)->, то необходимо указать ID инфоблока в поле рядом.";
$MESS["SECTION_ID_TIP"] = "Поле содержит код, в котором передается ID раздела. По умолчанию поле содержит ={\$_REQUEST[\"SECTION_ID\"]}.";
$MESS["ELEMENT_SORT_FIELD_TIP"] = "Указывается поле, по которому будет происходить сортировка элементов текущего раздела.";
$MESS["ELEMENT_SORT_ORDER_TIP"] = "В каком направлении будут отсортированы элементы, по возрастанию или по убыванию.";
$MESS["FILTER_NAME_TIP"] = "Задается имя переменной, в которую передается массив параметров из фильтра. Служит для определения выходящих из фильтра элементов. Поле может быть оставлено пустым, тогда используется значение по умолчанию.";
$MESS["SECTION_URL_TIP"] = "Указывается путь к странице с описанием раздела инфоблока.";
$MESS["DETAIL_URL_TIP"] = "Указывается путь к странице с детальным описанием элемента инфоблока.";
$MESS["BASKET_URL_TIP"] = "Указывается путь к странице с корзиной покупателя.";
$MESS["ACTION_VARIABLE_TIP"] = "В данном поле указывается имя переменной, в которой передается действие: ADD_TO_COMPARE_LIST, ADD2BASKET и т.д. Значение поля по умолчанию <i>action</i>.";
$MESS["PRODUCT_ID_VARIABLE_TIP"] = "Имя переменной, в которой будет передаваться ID товара.";
$MESS["SECTION_ID_VARIABLE_TIP"] = "Имя переменной, в которой будет передаваться ID раздела инфоблока.";
$MESS["DISPLAY_PANEL_TIP"] = "При установленной опции кнопки будут отображены в режиме редактирования сайта на административной панели и в наборе кнопок области редактирования данного компонента.";
$MESS["DISPLAY_COMPARE_TIP"] = "Если опция отмечена, то будет выведена кнопка <i>Сравнить</i>, с помощью которой элемент добавляется в список сравнения.";
$MESS["SET_TITLE_TIP"] = "При установленной опции в качестве заголовка страницы будет установлено название раздела.";
$MESS["PAGE_ELEMENT_COUNT_TIP"] = "Данное число определяет количество элементов на одной странице. Остальные элементы будут выведены с помощью постраничной навигации.";
$MESS["LINE_ELEMENT_COUNT_TIP"] = "Число определяет количество элементов в одной строке при выводе элементов раздела.";
$MESS["PROPERTY_CODE_TIP"] = "Среди свойств инфоблока можно выбрать те, которые будут отображены при показе элементов. При выборе пункта (не выбрано)->  и без указания кодов свойств в строках ниже, свойства выведены не будут.";
$MESS["PRICE_CODE_TIP"] = "Устанавливается, какой из типов цен будут выведен в каталоге. Если ни один из типов не выбран, то цена и кнопки <i>Купить</i> и <i>В корзину</i> не будет показаны.";
$MESS["USE_PRICE_COUNT_TIP"] = "При отмеченной опции выводятся цены всех типов на товары.";
$MESS["SHOW_PRICE_COUNT_TIP"] = "Можно установить количество, для которого будет выведена цена, например, 1 или 10, в зависимости от специфики товара.";
$MESS["CACHE_TYPE_TIP"] = "<i>Авто</i>: действует при включенном кешировании в течение заданного времени;<br /><i>Кешировать</i>: для кеширования необходимо определить только время кеширования;<br /><i>Не кешировать</i>: кеширования нет в любом случае.";
$MESS["CACHE_TIME_TIP"] = "Поле служит для указания времени кеширования в секундах.";
$MESS["CACHE_FILTER_TIP"] = "При установленной опции каждый результат, полученный из фильтра, будет кешироваться.";
$MESS["DISPLAY_TOP_PAGER_TIP"] = "При отмеченной опции навигация по страницам будет выведена наверху страницы.";
$MESS["DISPLAY_BOTTOM_PAGER_TIP"] = "При отмеченной опции навигация по страницам будет выведена внизу страницы.";
$MESS["PAGER_TITLE_TIP"] = "В данном поле указывается название категорий, по которым происходит перемещение по элементам.";
$MESS["PAGER_SHOW_ALWAYS_TIP"] = "Если данный флаг отмечен, то постраничная навигация будет выводиться, даже если все элементы помещаются на одной странице.";
$MESS["PAGER_TEMPLATE_TIP"] = "В данном поле указывается имя шаблона постраничной навигации. Если поле пусто, то выбирается шаблон по умолчанию (.default). Также в системе задан шаблон <i>orange</i>.";
$MESS["PAGER_DESC_NUMBERING_TIP"] = "Механизм используют, если при добавлении элемента инфоблока, он всегда попадает наверх списка. Таким образом, меняется лишь последняя страница. Все предыдущие можно надолго закешировать.";
$MESS["PAGER_DESC_NUMBERING_CACHE_TIME_TIP"] = "Время кеширования первых страниц в секундах при использовании обратной навигации.";
$MESS["META_KEYWORDS_TIP"] = "Установить ключевые слова страницы из свойства.";
$MESS["META_DESCRIPTION_TIP"] = "Установить описание страницы из свойства.";
$MESS["INCLUDE_SUBSECTIONS_TIP"] = "При отмеченной опции будут показаны элементы подразделов раздела.";
$MESS["AJAX_MODE_TIP"] = "Включение для компонента режима AJAX.";
$MESS["AJAX_OPTION_SHADOW_TIP"] = "При выполнении перехода будет затенена область, которая должна измениться.";
$MESS["AJAX_OPTION_JUMP_TIP"] = "Если пользователь совершит AJAX-переход, то по окончании загрузки произойдет прокрутка к началу компонента.";
$MESS["AJAX_OPTION_STYLE_TIP"] = "При совершении AJAX-переходов будет происходить подгрузка и обработка списка стилей, вызванных компонентом.";
$MESS["AJAX_OPTION_HISTORY_TIP"] = "Когда пользователь выполняет AJAX-переходы, то при включенной опции можно использовать кнопки браузера \"Назад\" и \"Вперед\".";
$MESS["PRICE_VAT_INCLUDE_TIP"] = "При отмеченной опции цены будут показаны с учетом НДС.";
$MESS["QUANTITY_FLOAT_TIP"] = "При отмеченной опции в корзину можно будет добавлять дробное количество товара";
$MESS["HIDE_NOT_AVAILABLE_TIP"] = "При отмеченной опции будут скрыты товары, для которых общее количество на складах меньше либо равно нулю, включен количественный учет и не разрешена покупка при отсутствии товара";

$MESS["CP_BC_PARTIAL_PRODUCT_PROPERTIES"] = "Разрешить добавлять в корзину товары, у которых заполнены не все характеристики";
$MESS["CP_BC_ADD_PROPERTIES_TO_BASKET"] = "Добавлять в корзину свойства товаров";
$MESS["SHOW_DISCOUNT_TIME_EACH_SKU"] = "Отображать срок действия скидки для каждого торгового предложения";
$MESS["SHOW_BUY_BTN"] = "Отображать кнопку \"В корзину\"";
$MESS["SHOW_BUY_BTN_TIP"] = "При включенной опции, вместо кнопки \"Подробнее\" будет отображена кнопка \"В корзину\". Для торговых предложений необходимо еще установить значения в параметре \"Свойства предложений\"";

$MESS["SHOW_MEASURE_WITH_RATIO"] = "Выводить единицу измерения с коэффициентом при отображении минимальной цены для товаров с торговыми предложениями";
$MESS["SHOW_MEASURE_WITH_RATIO_TIP"] = "При отмеченной опции у товаров с торговыми предложениями у минимальной цены будет отображаться единица измерения вместе с коэффициентом (если коэффициент единицы измерения не равен 1)";
?>