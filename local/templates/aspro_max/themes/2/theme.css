.colored_theme_bg,
.colored_theme_bg_before:before,
.colored_theme_hover_bg_before:hover:before,
.colored_theme_bg_before:before,
.colored_theme_hover_bg:hover,
.colored_theme_hover_bg-block:hover .colored_theme_hover_bg-el,
.video-block .image .play:after,
.landings-list__item--active,
.item-accordion-wrapper.opened .accordion-head:before {
  background-color: #f35c50;
  border-color: #f35c50;
}
.colored_theme_bg_hovered_hover:hover {
  background-color: #f5776d;
}
.bx-ie .colored_theme_hover_bg-block:hover .colored_theme_hover_bg-el-svg svg path {
  fill: #f35c50;
}
.colored_theme_n_hover_bg-svg-stroke:not(:hover) svg * {
  stroke: #f35c50 !important;
}
.colored_theme_hover:hover {
  background-color: #f35c50 !important;
}
.EXTENDED .blog-comment-meta .rating-vote a.active * {
  fill: #f35c50 !important;
}
.EXTENDED .blog-comment-meta a {
  color: #f35c50 !important;
}
a.scroll-to-top.ROUND_COLOR,
a.scroll-to-top.RECT_COLOR {
  background-color: #f35c50;
}
a.scroll-to-top.ROUND_COLOR:hover,
a.scroll-to-top.RECT_COLOR:hover {
  background-color: #da958d;
}
.cluster_custom svg .cls-cluster2,
.cls-marker2 {
  fill: #f35c50 !important;
}
.bx-ie .color-theme-hover:hover,
.bx-ie .color-theme-hover:hover * {
  color: #f35c50 !important;
}
.bx-ie #main .basket_hover_block .tabs_content .foot > .pull-left:hover .svg-inline-closes * {
  fill: #f35c50 !important;
}
#main .basket_hover_block .tabs_content .items .item .remove-cell:hover * {
  fill: #f35c50 !important;
}
.mega_fixed_menu .right_block .contact_wrap .person_wrap .counters .count {
  background-color: #f35c50;
}
.uploader:hover {
  background: #f35c50;
  border-color: #f35c50;
}
.uploader:hover .resetfile:before {
  background-color: #f5776d;
}
.basket_print i {
  background-color: #f24537;
}
.bx-ie .btn_basket_heading:hover .title,
.bx-ie .basket-checkout-block-share:hover .title {
  color: #f35c50;
}
.rss_feed_icon {
  background: #f34241;
  color: #000;
}
.start_promo .item i.title a,
.start_promo .item i.price a,
.start_promo .item i.title span,
.start_promo .item i.price span,
.start_promo .item span.main_wrap i:after,
.start_promo .item p span,
.wrap_tizer .wrap_outer,
.wrap_tizer .wrap_outer .outer_text,
.wrap_tizer .wrap_outer .inner_text {
  background: #f4574a;
}
.start_promo .item i.title span,
.start_promo .item i.price span {
  box-shadow: -4px 1px 0px 4px #f4574a, 4px 1px 0px 4px #f4574a;
}
.start_promo .item i.price span {
  box-shadow: -4px 0px 0px 4px #f4574a, 4px 0px 0px 4px #f4574a;
}
.module-map .infoBox .close_info:hover {
  background-color: #f34241;
}
#header ul.menu.full > li.search_row #search-submit-button:hover,
#header ul.menu.full > li.search_row #search-submit-button.hover {
  border: transparent;
}
#header .catalog_menu ul.menu > li,
#header ul.menu.adaptive:not(.opened) li.menu_opener,
#header ul.menu.full.opened li:not(.search_row):hover,
#header ul.menu.full.opened li.current {
  border-bottom: 1px solid #ed2e0f;
}
#header .catalog_menu ul.menu > li:not(.current):not(.stretch):hover {
  border-bottom: 1px solid #f01a11;
  border-right: 1px solid #f01a11;
}
#header ul.menu.full.opened li:not(.search_row):hover,
#header ul.menu.full.opened li.current {
  border-top: 1px solid #f4574a;
}
#header .catalog_menu ul.menu > li.current > a {
  color: #fff;
  border-left: 1px solid #f35245;
}
#header .catalog_menu ul.menu > li {
  border-right: 1px solid #ed2e0f;
}
.header-cart-block .cart span.icon i {
  background-position: 0px -84px;
}
ul.menu li .child .child_wrapp,
.authorization-cols .form-block {
  border-top: 2px solid #f34241;
}
.catalog_detail ul.tabs.main_tabs {
  border-bottom: 2px solid #f34241;
}
ul.menu li b.space,
.breadcrumbs .drop b.space,
.hint .triangle {
  border-bottom: 6px solid #f34241;
}
.basket_button,
.basket_button span,
.button30,
.button30 span,
.popup .soc-avt .row a {
  color: #fff;
}
.bx-ie .breadcrumbs__item:hover .breadcrumbs__link .breadcrumbs__item-name {
  color: #f35c50;
}
.forgot:hover {
  color: #f35c50 !important;
}
.bx-ie .delivery_note:hover .title {
  color: #f35c50;
}
.bx-ie .delivery_note:hover .svg * {
  fill: #f35c50;
}
.basket_hover_block .tabs_content .buttons .basket_back a:hover {
  background: #f5776d;
}
#main .basket_hover_block .cart-empty .cart-empty__info .btn:hover {
  background: #f5776d;
}
#bx-soa-order-form .btn.btn-default {
  background: #f35c50;
  border-color: #f35c50;
}
#bx-soa-order-form .btn.btn-default:hover {
  background: #f5776d;
  border-color: #f5776d;
}
#mobilefilter .bx_filter .bx_filter_button_box .bx_filter_parameters_box_container {
  background: #f35c50;
  border-color: #f35c50;
}
.sidebar_menu .menu_top_block.catalog_block ul.dropdown li.v_bottom .parent:hover .svg-inline-right {
  background: #f35c50 !important;
}
.sidebar_menu .menu_top_block.catalog_block ul.dropdown li.v_bottom .parent:hover .svg-inline-down {
  background: #f35c50 !important;
}
ul.tabs li.cur .triangle,
.view-list .view-header i.triangle {
  border-top: 5px solid #f24537 !important;
}
.basket_button.add span {
  background-position: 0px -6px;
}
.basket_button.read_more span {
  background-position: 1px -448px;
}
.basket_button.added span {
  background-position: 0px -45px;
}
.front_slider_wrapp .extended_pagination > li.active i.triangle {
  border-right-color: #f24537;
}
.catalog_detail .extended_info a.compare_item i {
  background-position: -32px -65px;
}
.catalog_detail .extended_info a.wish_item i {
  background-position: 3px -72px;
}
.display_list a.compare_item i {
  background-position: -32px -3px;
}
.display_list a.wish_item i {
  background-position: -3px -2px;
}
.display_list a.compare_item:hover i,
.display_list a.compare_item.added i {
  background-position: -32px -65px;
}
.display_list a.wish_item:hover i,
.display_list a.wish_item.added i {
  background-position: -3px -63px;
}
.popup .form .form_head {
  border-bottom-color: #f34241;
}
.item_slider .thumbs i.triangle {
  border-bottom: 5px solid #f34241;
}
blockquote:before {
  background-color: #f34241;
}
.module-gallery-list li .fancy_hover:hover {
  border: 2px solid #f34241;
}
.smartfilter .bx_ui_slider_track .bx_ui_slider_range,
.bx_filter .bx_ui_slider_pricebar_V:after {
  background: #f35c50;
}
.catalog_block .catalog_item .basket_button.read_more span {
  background-position: 1px -408px;
}
.catalog_block .catalog_item .basket_button.to-cart span {
  background-position: 0 -7px;
}
.catalog_block .catalog_item:hover .basket_button span {
  color: #fff;
  background-position: 0px -87px;
}
.catalog_block .catalog_item:hover .basket_button.read_more span {
  background-position: 1px -448px !important;
}
.catalog_item .basket_button.in-cart span,
.basket_button.in-cart span {
  background-position: 0px -127px !important;
}
.bx-ie .catalog_item:hover .item-title a,
.bx-ie .item:hover .info a,
.bx-ie .item:hover .name a,
.bx-ie .item:hover .title a,
.bx-ie .item .title a:hover {
  color: #f24537;
}
.style-switcher .left-block .section-block .subitems .subsection-block.active:before {
  background-color: #f35c50;
}
.bx-ie .style-switcher .right-block .action_block .header-inner:hover {
  color: #f35c50;
}
.bx-ie .style-switcher .right-block .action_block .header-inner:hover svg path {
  fill: #f35c50;
}
.style-switcher .presets .presets_subtabs .presets_subtab.active:after,
.style-switcher .presets .presets_block .item.active .inner,
.presets .presets_block .conf .preset-block .checked,
.dynamic_left_side .cl:hover,
.style-switcher > .close_block .svg:hover {
  background: #f35c50;
}
.style-switcher .contents.wizard .variant.active .checkbox {
  border-color: #f35c50;
}
@media (min-width: 501px) and (max-width: 700px) {
  .style-switcher > .close_block .svg {
    background: #f35c50;
  }
}
.presets .presets_block .conf .preset-block.current .image {
  box-shadow: 0px 0px 0px 1px inset #f35c50;
  border-color: #f35c50;
}
.bx-ie .contents.parametrs .dynamic_left_side .items_inner .bottom_description a svg path,
.bx-ie .colored_theme_svg svg path,
.bx-ie .colored_theme_svg,
.bx-ie .muted:not(.ncolor):hover svg path {
  fill: #f35c50;
}
.more-item-info.opened,
.more-item-info:hover,
.catalog_item:hover .more-item-info {
  background-color: #f35c50;
}
.more-item-info.opened,
.more-item-info:hover,
.catalog_item:hover .more-item-info {
  border-color: #f35c50;
}
.item_block.slide:hover .arrow-block {
  background-color: #f35c50;
  border-color: #f35c50;
}
#mobilemenu .expanded>.dropdown .menu_back:hover .svg-inline-back_arrow * {
  fill: #f35c50;
}
.catalog_item:hover .basket_button.added span {
  color: #fff;
}
.bx_filter_container #modef .triangle,
.left_block .internal_sections_list .child_wrapp .triangle {
  border-right: 6px solid #f34241;
}
#order_form_div .sale_order_table .ps_logo img:hover,
#order_form_div .sale_order_table .ps_logo input[type=radio]:checked + label img,
#order_form_div .sale_order_table.delivery label:hover img,
#order_form_div .sale_order_table.delivery input[type=radio]:checked + label img,
#order_form_div div.ps_logo.selected img,
#order_form_div .account label:hover img,
#order_form_div .account img.active {
  border: 2px solid #f34241;
}
.front_slider_wrapp .extended_pagination > li.active span,
a.b-share-popup__item:hover .b-share-popup__item__text {
  color: #fff !important;
}
ul.tabs li.cur span,
ul.tabs li.cur:hover span,
.front_slider_wrapp a.read_more {
  color: #fff;
}
a i.arrow b {
  background-position: -36px 0px;
}
.top-h-row .search #search-submit-button:hover i,
.top-h-row .search #search-submit-button.hover i {
  background-position: top center;
}
.catalog_detail a.compare_item:hover .icon i {
  background-position: -32px -65px;
}
.catalog_detail a.wish_item:hover .icon i {
  background-position: 2px -45px;
}
.button30.ask_question span.show_form,
.button30.ask_question span.hide_form {
  background-position: 5px -605px;
}
.button30.ask_question span.hide_form {
  color: #000;
}
.button30.ask_question span.hide_form:hover {
  color: #fff;
}
.question-list .q .ic b {
  background-position: -38px -188px;
}
.question-list .q.op .ic b {
  background-position: -8px -188px;
}
.number_list a.current span {
  color: #fff !important;
}
.basket_button span,
.compare_button span {
  background-position: 0px -87px;
}
.bx_filter_container_title .hint.active .hint_icon,
.bx_filter_container_title .hint.active .hint_icon,
.bx_filter_container_title .hint.active .hint_icon:hover,
#header .basket_fly .opener .basket_count:not(.empty) .count {
  color: #fff;
}
.left_block .internal_sections_list li.item:hover > a,
.left_block .internal_sections_list li.item.cur > a {
  color: #fff;
}
.left_block .internal_sections_list li * {
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
  transition: none;
}
a.compare_item i {
  background-position: -31px -1px;
}
a.wish_item i {
  background-position: -2px -1px;
}
a.compare_item:hover i {
  background-position: -31px -33px;
}
a.wish_item:hover i {
  background-position: -2px -32px;
}
a.compare_item.added i {
  background-position: -31px -63px;
}
a.wish_item.added i {
  background-position: -2px -62px;
}
.module_products_list .basket_button.to-cart span {
  background-position: 0 -7px;
}
.module_products_list .basket_button.to-cart:hover span {
  background-position: 0px -87px;
}
.basket_button.in-cart span {
  background-position: 0px -127px;
}
.popup .close:hover i,
.popup .grey .close i,
.popup-window-close-icon.popup-window-titlebar-close-icon:hover:after {
  background-position: -1px -59px;
}
.card_popup_frame.popup .but_row a.to_basket .icon i {
  background-position: -5px -170px;
}
.soc-serv-main .button30 span,
.reviews-expanded .button30.add_review span,
.catalog_detail .basket_button.one_click span,
.button30.review_preview span,
.popup .grey .pop-up-title {
  color: #000;
  text-shadow: 0 1px 0px rgba(255,255,255,0.5);
  -moz-text-shadow: 0 1px 0px rgba(255,255,255,0.5);
  -o-text-shadow: 0 1px 0px rgba(255,255,255,0.5);
  -webkit-text-shadow: 0 1px 0px rgba(255,255,255,0.5);
}
.module-order-history .status.delivered {
  color: #fff;
}
.jobs_wrapp .item a:not(.opened):hover .icon i,
.staff_wrapp .section .section_title:not(.opened):hover .icon i {
  background-position: -31px -33px;
}
.jobs_wrapp .item a.opened:hover .icon i,
.staff_wrapp .section .section_title.opened:hover .icon i {
  background-position: 0 -34px;
}
.button30.add_review span {
  background-position: 4px -286px;
}
.button30.send_review span {
  background-position: 4px -365px;
}
.card_popup_frame.popup .but_row a.to_delay .icon i {
  background-position: -48px -257px;
}
.compare_button span {
  background-position: -32px -64px;
}
#header .basket_fly .opener .basket_count .icon i {
  background-position: -7px -89px;
}
#header .basket_fly .opener .basket_count.empty:hover .icon i {
  background-position: -7px -47px;
}
#header .basket_fly .opener .basket_count.empty .icon i {
  background-position: -7px -5px;
}
#header .basket_fly .opener .wish_count .icon i {
  background-position: -48px -424px;
}
#header .basket_fly .opener .wish_count:hover .icon i {
  background-position: -48px -383px;
}
ul.menu li a {
  color: #000;
}
.catalog_menu ul.menu li a {
  color: #fff;
}
a i.arrow.down b {
  background-position: -36px -35px;
}
.catalog_item:hover .basket_button span {
  color: #fff;
}
ul.specials_slider .basket_button span {
  background-position: 0 -7px;
}
ul.specials_slider .basket_button:hover span,
ul.specials_slider li:hover .basket_button span {
  background-position: 0px -87px;
}
ul.specials_slider  .basket_button.read_more span {
  background-position: 1px -408px;
}
ul.specials_slider li:hover .basket_button.read_more span {
  background-position: 1px -448px !important;
}
#header .basket_fly .opener .wish_count.empty:hover .icon i {
  background-position: -48px -341px;
}
#header .basket_fly .opener .wish_count.empty .icon i {
  background-position: -48px -299px;
}
.flex-direction-nav li:hover a,
.flex-direction-nav li:hover .js-click {
  background-color: #f35c50;
}
.colored-svg {
  stroke: #f35c50;
}
#basket-root .basket-items-list-header-filter-item.active:before {
  background-color: #f35c50;
}
#basket-root .basket-item-scu-item:hover,
#basket-root .basket-item-scu-item.selected,
#basket-root .basket-item-scu-item.not-available:hover {
  border-color: #f35c50;
}
#basket-root .basket-items-list .basket-item-block-info .basket-items-list-item-warning-container .alert.alert-warning a[data-entity="basket-item-remove-delayed"]:before {
  background-color: #f35c50;
}
#basket-root .basket-item-property-scu-text .basket-item-scu-item.selected,
.basket-coupon-alert .close-link:hover:after,
.basket-coupon-alert .close-link:hover:before,
#basket-root .basket-item-amount-btn-plus:hover:before,
#basket-root .basket-item-amount-btn-plus:hover:after,
.basket-item-amount-btn-minus:hover:after {
  background-color: #f35c50;
}
#basket-root .basket-checkout-section .btn.btn-default:hover,
body #basket-root .basket-checkout-section .btn.btn-default:active,
body #basket-root .basket-checkout-section .btn.btn-default:focus {
  border-color: #f5776d;
  background-color: #f5776d;
}
#basket-root #basket-warning {
  border-color: #f35c50;
}
#basket-root .basket-checkout-section .btn-default.basket-btn-checkout:not(:hover),
#basket-root .basket-checkout-section .btn-default.basket-btn-checkout:not(:active),
#basket-root .basket-checkout-section .btn-default.basket-btn-checkout:not(:focus) {
  background-color: #f35c50;
  border-color: #f35c50;
}
.sale-products-gift .product-item-scu-item-text-block:hover,
.sale-products-gift .product-item-scu-item-color-block:hover,
.sale-products-gift .product-item-scu-item-text-container.selected .product-item-scu-item-text-block,
.sale-products-gift .product-item-scu-item-color-container.selected .product-item-scu-item-color-block {
  outline-color: #f35c50;
}
.sale-products-gift .product-item-small-card .product-item-container .product-item .product-item-button-container .btn:not(:hover) {
  color: #f35c50;
  border-color: rgba(243,92,80,0.35);
}
.sale-products-gift .product-item-small-card .product-item-container .product-item .product-item-button-container .btn:hover {
  background-color: #f35c50;
  border-color: #f35c50;
}
#bx-soa-order .bx-soa-section .bx-soa-section-title:before {
  background-color: #f35c50;
}
#bx-soa-order div[class*=bx-sls] .quick-locations .quick-location-tag,
#bx-soa-order-form .bx-soa-pickup-list-item:not(.bx-selected) .bx-soa-pickup-l-item-btn .btn.btn-default:not(:hover) {
  border-color: rgba(243,92,80,0.35);
  color: #f35c50;
}
#bx-soa-order .bx-soa-more .bx-soa-more-btn .pull-left {
  border-color: rgba(243,92,80,0.35);
}
#bx-soa-order div[class*=bx-sls] .quick-locations .quick-location-tag:hover {
  background-color: #f35c50;
  color: #fff;
}
#bx-soa-order .bx-soa .form-group .radio-inline.checked label:before,
#bx-soa-order .bx-soa .form-group .radio-inline label:hover:before,
#bx-soa-order .bx-soa-pp-company:hover .bx-soa-pp-company-graf-container:before,
#bx-soa-order .bx-soa-pp-company.bx-selected .bx-soa-pp-company-graf-container:before {
  background-color: #f35c50;
}
#bx-soa-order .bx-soa-pp-item-container .bx-soa-pp-company.bx-selected .bx-soa-pp-company-graf-container,
#bx-soa-order .bx-soa-pp-item-container .bx-soa-pp-company:hover .bx-soa-pp-company-graf-container,
#bx-soa-order .bx-soa-pp-company.bx-selected .bx-soa-pp-company-graf-container .bx-soa-pp-company-image,
#bx-soa-order .bx-soa-pp-company:hover .bx-soa-pp-company-graf-container .bx-soa-pp-company-image {
  border-color: #f35c50;
}
#bx-soa-order .bx-soa-pp-desc-container .bx-soa-pp-company:before,
#bx-soa-order .bx-soa-pp-desc-container .checkbox label:hover:before,
#bx-soa-order .bx-soa-pp-desc-container .checkbox label.checked:before {
  background-color: #f35c50;
}
#bx-soa-order .bx-soa-pp-item-container .bx-soa-pp-company:hover .bx-soa-pp-company-smalltitle,
#bx-soa-order .bx-soa-more .bx-soa-more-btn .pull-left {
  color: #f35c50;
}
div[class*=bx-sls] .dropdown-block:hover .bx-ui-sls-clear:before,
div[class*=bx-sls] .dropdown-block:hover .bx-ui-sls-clear:after,
div[class*=bx-sls] .bx-ui-sls-clear:hover:before,
div[class*=bx-sls] .bx-ui-sls-clear:hover:after,
#bx-soa-order .bx-soa-coupon-item .bx-soa-coupon-remove:hover:before,
#bx-soa-order .bx-soa-coupon-item .bx-soa-coupon-remove:hover:after {
  background-color: #f35c50 !important;
}
.set_block .item .item_inner .image .quantity,
body .sale_order_full_table:first-of-type:before {
  background-color: #f35c50;
}
@media all and (max-width: 950px) {
  .header_wrap.colored #header .center_block .main-nav {
    background: #f4574a;
  }
}
@media all and (max-width: 768px) {
  body .color-controls .flex-control-paging li a.flex-active,
  body .color-controls .flex-control-paging li:hover a:not(.touch) {
    background: #f4574a;
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzAwYWFlMiIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwMDhmYzciIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
    background: -moz-linear-gradient(top,#f4574a 0%,#f24732 100%);
    background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,#f4574a),color-stop(100%,#f24732));
    background: -webkit-linear-gradient(top,#f4574a 0%,#f24732 100%);
    background: -o-linear-gradient(top,#f4574a 0%,#f24732 100%);
    background: -ms-linear-gradient(top,#f4574a 0%,#f24732 100%);
    background: linear-gradient(to bottom,#f4574a 0%,#f24732 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='@bcolor_00aae2',endColorstr='@bcolor_008fc7',GradientType=0);
  }
}
@media all and (max-width: 600px) {
  #header ul.menu.full.opened > li,
  .has_menu #header .center_block .main-nav {
    background-color: #f4574a;
  }
  .has_menu #header .center_block .main-nav:hover {
    background: #e39693 none repeat scroll 0 0;
  }
  #header ul.menu.full.opened li.current {
    background: #f24732;
  }
}
@media all and (max-width: 550px) {
  body #footer .footer_inner .line {
    border-top: 4px solid #f34241;
  }
  .basket_wrapp .empty_cart:not(.bcart) .wraps_icon_block.basket .count span,
  .basket_wrapp .basket_empty .wraps_icon_block.basket .count span {
    color: #FFF;
    background: #f4574a;
  }
}
#header .wrapper_middle_menu.wrap_menu ul.mobile_menu li:hover>a,
#header .wrapper_middle_menu.wrap_menu ul.mobile_menu li.opened>a,
#header .wrapper_middle_menu.wrap_menu ul.mobile_menu li.current > a {
  background: #e39693;
}
#header .wrapper_middle_menu.wrap_menu ul.menu.opened > li {
  background: #f24537;
}
body .basket-link .js-basket-block .count,
.top_slider_wrapp .slides .wraps_buttons .wrap.added {
  background: #f35c50;
}
.button.transparent,
.h_color_white .header_wrap .menu > li.current > a span,
.h_color_white.wrapper #header ul.menu li .child .child_wrapp,
.m_color_white.wrapper #header ul.menu li .child .child_wrapp,
.basket_normal .popup.card_popup_frame .basket_popup_wrapp,
.basket_sort ul.tabs li.cur div,
.smartfilter .bx_ui_slider_track .bx_ui_slider_handle,
.bx_filter .button,
.bx_filter_vertical input[type="checkbox"]:checked + label.sku,
.sku_props .bx_item_detail_size ul li.active,
.bx_size_scroller_container .bx_size ul li.bx_active,
.bx_filter .bx_filter_param_label.active .bx_filter_param_btn,
.bx_catalog_item_scu .bx_item_detail_scu ul li.active span.cnt,
.item_slider .thumbs li.current,
.popup-window.popup-window-titlebar .popup-window-top-row .popup-window-left-column,
.popup-window.popup-window-titlebar .popup-window-top-row .popup-window-center-column,
.popup-window.popup-window-titlebar .popup-window-top-row .popup-window-right-column,
.popup-window.popup-window-titlebar-light .popup-window-top-row .popup-window-left-column,
.popup-window.popup-window-titlebar-light .popup-window-top-row .popup-window-center-column,
.popup-window.popup-window-titlebar-light .popup-window-top-row .popup-window-right-column,
.bx_element input[type=radio]:checked + label .bx_logotype,
.bx_element label.selected .bx_logotype {
  border-color: #f35c50;
}
.bx-ie a,
.bx-ie .link,
.button.transparent,
.button.transparent:hover,
.specials_slider_wrapp ul.tabs>li span,
.basket_wrapp .basket_block .link:hover+.wraps_icon_block+.text .title,
.stores .stores_list .item a span,
.phones .order_wrap_btn,
.footer_bottom .all_menu_block ul li a:hover,
.footer_bottom .submenu_top .menu_item a:hover,
.avtorization-call.enter:hover span,
.top-h-row .h-user-block a:hover,
ul.menu .child li.menu_item a,
#header .catalog_menu ul.menu>li.current>a,
ul.menu .child .depth3 a,
ul.menu .child li.menu_title a:hover,
#header ul.menu li .child.submenu .child_wrapp a:not(.title),
.img_block_capcha .reload,
.h_color_white .center_block .menu.full>li:hover>a,
.m_color_white #header .wrapper_middle_menu .menu.bottom>li:hover>a,
.m_color_white #header .wrapper_middle_menu .menu.bottom>li.hover>a,
body #header ul.menu li .child.line .child_wrapp>a:hover,
.basket_sort ul.tabs li .wrap_li>span *,
.basket_sort ul.tabs li .wrap_li>span,
.left_block .internal_sections_list .child_wrapp .child li.cur>a,
.left_block .internal_sections_list .child_wrapp .child li.depth3 a.menu_item.cur,
.left_block .internal_sections_list .child_wrapp a:hover,
.bx-ie .breadcrumbs a:hover span,
.tabs-head li.current span,
.reviews-post-reply-buttons a:hover,
h4.hover,
.button.transparent.white:hover,
.button.white_bg,
.bx-ie .item .child_container .child_wrapp .menu_item.current a,
.bx-ie .item .child_container .child_wrapp .menu_item:hover a {
  color: #f24537;
}
button.button,
input.button,
.button,
#header ul.menu.full.opened li:not(.search_row):hover,
#header ul.menu.full > li.search_row #search-submit-button:hover,
table.title-search-result td.title-search-all a .icon,
.flex-direction-nav li:hover,
.flex-control-nav li .flex-active:before,
.flex-control-nav li a:hover:before,
.top_slider_wrapp .flex-direction-nav li:hover,
.like_icons .wish_item.added,
.like_icons .compare_item.added,
.light .stores .all_map,
.block_wr.dark,
.header_wrap #header .middle-h-row .logo_wrapp .logo a,
.h_color_colored .header_wrap #header,
.registraion-page .top p:after,
.has_menu.m_color_colored #header .catalog_menu,
.module-cart .remove-cell a:hover,
.cart_shell .remove-cell a:hover,
.bx_item_set_hor .bx_item_set_hor_item a.remove,
.bx_filter .bx_filter_parameters_box_title:before,
.bx_filter_vertical input[type="checkbox"] + label:before,
.filter input[type="checkbox"] + label:before,
.filter input[type="radio"] + label:before,
.bx_filter label.pal:hover .bx_filter_param_btn,
.bx_filter .bx_filter_param_label.active .bx_filter_param_btn,
.bx_filter_vertical input[type="checkbox"]:checked + label.sku,
.left_block .internal_sections_list li.cur,
.left_block .internal_sections_list li:hover,
.left_block .internal_sections_list .title .inner_block:hover:before,
.left_block .internal_sections_list .child_wrapp .child li.depth3 a.menu_item.cur:before,
.left_block .internal_sections_list .child_wrapp .child li.depth3 a.menu_item:hover:before,
.left_block .internal_sections_list .child_wrapp ul.child li.cur:after,
.left_block .internal_sections_list .child_wrapp ul.child li:hover:after,
body #content .props_list tr td > span:before,
body #content .props_list tr td > .txt:before,
.module-pagination .nums .cur,
.more_text_ajax:before,
.captcha_reload:before,
.sku_props .bx_item_detail_size ul li.active,
.bx_size_scroller_container .bx_size ul li.bx_active,
.bx_catalog_item_scu .bx_item_detail_scu ul li.active span:not(.cnt_item),
.ik_select_list .ik_select_list_inner ul li.ik_select_active,
.bx_filter_select_popup ul li label.selected,
.filter_opener.opened,
.wr_scrollbar .scrollbar .handle,
a i.arrow,
.popup-window-close-icon.popup-window-titlebar-close-icon:hover,
.bx_kit_item .bx_kit_item_add,
.catalog_detail .offers_table td.opener:hover .opener_icon,
.opener_icon:hover,
.char_name .hint .icon:hover,
.char_name .hint.active .icon,
.location-block-wrapper .bx-sls .bx-ui-sls-clear:before,
h4:hover .opener_icon,
.bg_block:before,
#order_form_div input[type="submit"],
.module-order-history .item_name:hover .icon,
.module-order-history .item_name:hover .icon,
.faq.list .item .q:hover .opener_icon,
.jobs_wrapp .item .name tr:hover .opener_icon,
.owl-carousel.owl-theme.owl-bg-nav .owl-nav button:hover:not(.disabled) {
  color: #FFF;
  background: #f4574a;
}
.basket-share-detail__head .char_name .props_list .hint .icon:hover,
.basket-share-detail__head .char_name .props_list .hint.active .icon,
.basket-share-detail__head .char_name .props_list .hint.active .icon {
  color: #FFF;
  background: #f4574a;
  border-color: #f4574a;
}
.bigs .flex-direction-nav li {
  color: #FFF;
  background: #f4574a;
}
.btn.btn-default {
  background-color: #f35c50;
  border-color: #f35c50;
  color: #ffffff;
}
.btn.btn-default:hover,
.btn.btn-default:active,
.btn.btn-default:focus {
  background-color: #f5776d;
  border-color: #f5776d;
}
.btn.btn-default.white:not(.grey) {
  color: #f35c50 !important;
  border: 1px solid #f35c50;
  background: #ffffff;
}
.btn.btn-default.white:hover,
.btn-default.white:active,
.btn-default.white:focus {
  color: #ffffff !important;
  border-color: #f35c50;
  background: #f35c50;
}
.btn.btn-default.white.white-bg:hover,
.btn-default.white.white-bg:active,
.btn-default.white.white-bg:focus {
  color: #f35c50 !important;
  border-color: #f35c50;
  background: #fff;
}
.btn.btn-primary {
  background-color: #00b290;
  border-color: #00b290;
}
.btn.btn-primary:hover,
.btn.btn-primary:active,
.btn.btn-primary:focus {
  background-color: #30c4a8;
  border-color: #30c4a8;
}
.btn.btn-success {
  background-color: #84bc29;
  border-color: #84bc29;
}
.btn.btn-success:hover,
.btn.btn-success:active,
.btn.btn-success:focus {
  background-color: #9dca53;
  border-color: #9dca53;
}
.btn.btn-info {
  background-color: #0ca9e3;
  border-color: #0ca9e3;
}
.btn.btn-info:hover,
.btn.btn-info:active,
.btn.btn-info:focus {
  background-color: #39baec;
  border-color: #39baec;
}
.btn.btn-warning {
  background-color: #f38b04;
  border-color: #f38b04;
}
.btn.btn-warning:hover,
.btn.btn-warning:active,
.btn.btn-warning:focus {
  background-color: #f8a132;
  border-color: #f8a132;
}
.btn.btn-danger {
  background-color: #dc130d;
  border-color: #dc130d;
}
.btn.btn-danger:hover,
.btn.btn-danger:active,
.btn.btn-danger:focus {
  background-color: #e63f3a;
  border-color: #e63f3a;
}
.btn.btn-transparent {
  background-color: rgba(255,255,255,0);
  border-color: rgba(255,255,255,0);
}
.btn.btn-transparent:hover,
.btn.btn-transparent:active,
.btn.btn-transparent:focus {
  background-color: #f5776d;
  border-color: #f5776d;
}
.btn.btn-transparent:hover {
  color: #ffffff;
}
.btn.btn-responsive-nav {
  background-color: #f35c50;
}
.btn.btn-responsive-nav:hover {
  color: #f35c50;
  border: 1px solid #f35c50;
  background-color: #ffffff;
  padding: 4px 7px 2px;
}
.btn.btn-link {
  color: #f35c50;
  border-color: rgba(243,92,80,0.35);
}
.btn.btn-link:hover {
  color: #f5776d;
  border-color: transparent;
}
.btn.btn-link:active {
  color: #e21f0f;
}
.btn.btn-default.wc.vert .fa {
  background-color: #f35c50;
}
.btn.btn-default.wc.vert:hover .fa {
  background-color: #f5776d;
}
.btn.btn-transparent-border:hover {
  background-color: #f35c50;
  border: 1px solid #f35c50;
}
.bx-ie .btn.btn-transparent-border-color {
  border-color: rgba(243,92,80,0.35);
  color: #f35c50;
}
.bx-ie .btn.btn-transparent-border-color:hover {
  background-color: #f35c50;
  border-color: #f35c50;
}
@media (max-width: 767px) {
  .top_slider_wrapp.view_2 .btn.btn-transparent-border {
    border-color: rgba(243,92,80,0.35);
    color: #f35c50;
  }
  .top_slider_wrapp.view_2 .btn.btn-transparent-border:hover {
    background-color: #f35c50;
    border-color: #f35c50;
  }
  .top_slider_wrapp.view_2 .btn.btn-default.white:not(:hover) {
    border-color: rgba(243,92,80,0.35);
    color: #f35c50;
    color: #f35c50 !important;
  }
  .top_slider_wrapp.view_2 .btn.btn-default.white:hover {
    background-color: #f35c50;
    border-color: #f35c50;
  }
  .top_slider_wrapp.view_2 .btn.btn-lg.white-border:not(:hover) {
    border-color: rgba(243,92,80,0.35);
    color: #f35c50;
    color: #f35c50 !important;
  }
  .top_slider_wrapp.view_2 .btn.btn-lg.white-border:hover {
    background-color: #f35c50;
    border-color: #f35c50;
  }
}
.half_block.top_big_banners .btn.btn-transparent-border:not(:hover) {
  color: #f35c50;
  border-color: #f35c50;
}
.bx-ie .catalog_section_list .separator {
  color: rgba(243,92,80,0.35);
}
.btn-inline {
  color: #f35c50;
}
.btn-inline.black:hover {
  color: #f35c50;
}
.btn-inline.rounded:hover i {
  background: #f35c50;
}
.popup-window-content-white .popup-window-buttons .btn,
.basket-icons-wrapper__btn .btn.in-cart,
.basket-icons-wrapper__btn .btn.in-subscribe {
  background-color: #f35c50;
  border-color: #f35c50;
}
.popup-window-content-white .popup-window-buttons .btn:hover,
body .item:hover .cost .icons-basket-wrapper .btn,
.item .icons-basket-wrapper .btn.in-cart,
.basket-icons-wrapper__btn:hover .btn.in-cart,
.basket-icons-wrapper__btn:active .btn,
.basket-icons-wrapper__btn:focus .btn,
.item .icons-basket-wrapper .btn.in-subscribe {
  background-color: #f5776d;
  border-color: #f5776d;
}
._active .section-gallery-wrapper__item-nav:before {
  background-color: #f35c50;
}
.basket-icons-wrapper .like_icons > div span.in {
  border-color: #f35c50;
}
.basket-icons-wrapper .like_icons > div span:hover {
  border-color: #f5776d;
}
body .fa.big-icon {
  background: #f35c50;
}
body .fa.big-icon.grey {
  color: #f35c50;
  background: #efefef;
}
.icon-text {
  color: #fff;
  background: #f35c50;
}
.icon-text .fa {
  color: #fff;
}
.icon-text.grey {
  color: #222;
  background: #efefef;
}
.icon-text.grey .fa {
  color: #f35c50;
}
.introtext {
  border-bottom-color: #f35c50;
}
.preview-text-detail .colored_line {
  background-color: #f35c50;
}
.sort_display a.current {
  background-color: #f35c50;
}
.view_sale_block .quantity_block .values .item {
  background-color: #f35c50;
}
footer .info .email a:hover {
  color: #f35c50;
}
.bx-ie .logo.colored svg .icon-path,
.bx-ie .svg.colored * {
  fill: #f35c50;
}
.product-item-detail-tabs-container-fixed .product-item-detail-tabs-list li a:before {
  background-color: #f35c50;
}
.bx_soc_comments_div .bx-catalog-tab-list1 li.active a {
  color: #f35c50;
}
.bx_soc_comments_div .bx-catalog-tab-list1 li.active a svg path {
  fill: #f35c50;
}
#mobileheader .mobileheader-v2 {
  background: #f35c50;
}
#mobilemenu .menu ul>li.selected a:before {
  background: #f35c50;
}
#mobilemenu .contacts a:hover {
  color: #f35c50;
}
#mobilemenu .menu>ul>li.counters .count {
  background: #f35c50;
}
#headerfixed .menu-block.rows:hover {
  background-color: #f35c50;
}
.bx-ie #headerfixed .menu-block .navs ul li.active>a,
.bx-ie #headerfixed .menu-block .navs ul li a:hover,
.bx-ie #headerfixed .menu-block .navs ul li a:focus {
  color: #f35c50;
}
.top-block.colored {
  background: #f35c50;
}
.top-block .social-icons li a:hover:before {
  background-color: #f35c50;
}
.top-block .top-block-item  .inline-search-show:hover>span {
  color: #f35c50;
}
.logo.colored img,
header .line-row,
#mobileheader .logo.colored img {
  background-color: #f35c50;
}
.logo-row .top-callback .phone .fa,
.logo-row .top-callback .email .fa {
  background-color: #f35c50;
}
.logo-row .top-callback .email,
.logo-row .top-callback .email a:hover {
  color: #f5776d;
}
.inline-search-block.fixed.big .search .close-block:hover .close-icons {
  background-color: #f5776d;
}
.menu-row.bgcolored .menu-only {
  background: #f35c50;
}
.bx-ie .wrapper1:not(.light-menu-color) .header_wrap:not(.light-menu-color) .logo_and_menu-row .wrap_icon .top-btn:hover .title {
  color: #f35c50;
}
.search-tags-cloud .tags a:hover {
  background: #f35c50;
}
.logo.colored a img {
  background-color: #f4574a;
}
.bx-ie .dark-color:hover,
.bx-ie a.colored,
.bx-ie .colored,
.bx-ie body#main .colored_theme_text,
.bx-ie body#main .colored_theme_text_with_hover:not(:hover),
.bx-ie body#main .colored_theme_hover_text:hover,
.bx-ie .top-block .menu >li>a:hover {
  color: #f35c50;
}
.bx-ie .colored_theme_block_text:hover .colored_theme_el_text {
  color: #f35c50 !important;
}
.dark-color:hover svg:not(.not_fill) rect,
.dark-color:hover svg:not(.not_fill) circle {
  stroke: #f35c50;
}
.bx-ie body#main .colored_theme_text_with_hover:not(:hover) .svg svg path,
.bx-ie body#main .colored_theme_hover_text:hover svg path,
.bx-ie .fancybox-is-open svg:hover path {
  fill: #f35c50;
}
.bx_filter.compact .bx_filter_parameters_box.set .bx_filter_parameters_box_title.title,
.filter_title.active-filter .svg:before {
  background: #f35c50;
}
.smartfilter .bx_ui_slider_track .bx_ui_slider_handle:after {
  background: #f35c50;
}
#basket-root .basket-item-actions-remove:hover:after,
#basket-root .basket-item-actions-remove:hover:before,
#basket-root .basket-items-list-item-clear-btn:hover:after,
#basket-root .basket-items-list-item-clear-btn:hover:before,
#basket-root .basket-items-list-wrapper .basket-clear:hover:after,
#basket-root .basket-items-list-wrapper .basket-clear:hover:before {
  background: #f35c50;
}
.catalog_detail .bx-modal-container .btn.btn-add,
.personal_wrapper form input[type=submit] {
  color: #FFF;
  background: #f35c50;
}
.catalog_detail .bx-modal-container .btn.btn-add:hover,
.personal_wrapper form input[type=submit]:hover {
  background: #f5776d;
}
.colored #header .catalog_menu ul.menu > li,
header .wrap_menu {
  background: #f24f42;
}
.tabs-head li.current:before {
  background-color: #f4574a;
}
.like_icons .compare_item.added:not(.btn) i,
.like_icons .wish_item.added:not(.btn) i {
  background-color: #f4574a;
}
.icon_error_block:after {
  background-color: #f4574a !important;
}
.specials_slider_wrapp ul.tabs li.cur span,
.tab_slider_wrapp ul.tabs li.cur span,
.popup .popup-intro {
  border-bottom-color: #f35c50;
}
.button:hover,
.like_icons .compare_item.added:hover,
.like_icons .wish_item.added:hover,
.light .stores .all_map:hover {
  background: #da9188;
}
html:not(.bx-touch) .like_icons .compare_item.added:not(.btn):hover i,
html:not(.bx-touch) .like_icons .wish_item.added:not(.btn):hover i {
  background-color: #da9188;
}
.wrapper_middle_menu.wrap_menu.mobile:hover {
  background: #da9188;
}
.header_wrap #header .catalog_menu.menu_colored .inc_menu  ul.menu > li:not(.current):hover > a {
  background: #f4695e;
}
.phone_block .phone_wrap .icons {
  color: #f24537;
}
.bx-ie header .menu_top_block li.catalog>.dropdown>li:hover>a,
.bx-ie header .menu_top_block li.catalog>.dropdown>li>.dropdown>li.current>a:not(.section) {
  color: #f24537;
}
.bx-ie header .menu_top_block li .dropdown>li.current>a.section1,
.bx-ie .menu_top_block li .dropdown>li>a.section1:hover,
.bx-ie #header .catalog_menu.menu_light ul.menu>li.current>a,
.bx-ie #header .catalog_menu.menu_light ul.menu>li:hover>a {
  color: #f24537;
}
.menu_top_block .dropdown> li.v_bottom a:hover .svg.svg-inline-down,
.left_menu > li.has-childs.v_bottom a:hover .svg.svg-inline-down {
  background-color: #f35c50;
}
.mega_fixed_menu .mega-menu table .wrap > .dropdown-menu .dropdown-submenu > a:hover>.arrow>i {
  background-color: #f35c50;
}
.mega_fixed_menu .svg.svg-close:hover * {
  fill: #f35c50;
}
.inline-search-block.bg-colored-block.show {
  background-color: #f35c50;
}
.menu-row.bgcolored .wrap_icon:hover {
  background-color: #f5776d;
}
.logo_and_menu-row .burger:hover .svg-burger {
  background-color: #f5776d;
}
body .basket-link.basket-count .count {
  background-color: #f35c50;
}
.logo_and_menu-row .basket-link:hover .title {
  color: #f35c50;
}
.bx-ie .top-block-item  .basket-link:hover  .title {
  color: #f35c50;
}
.top-btn:hover .svg:not(.inline) {
  background-color: #f35c50;
}
.bx-is .top-btn .svg.inline:hover svg path,
.bx-is .top-btn:hover .svg.inline svg path,
.bx-is .svg.inline:not(.colored):hover svg path,
.bx-is a:hover .svg.inline path {
  fill: #f35c50;
}
.fill_bg_n .menu-row.bgcolored,
.fill_bg_y .menu-row.bgcolored > .maxwidth-theme {
  background-color: #f35c50;
}
.menu-row.bgcolored .mega-menu table td:hover {
  background-color: #f5776d;
}
.menu-row.bgcolored .mega-menu table td.active {
  background-color: #f0382a;
}
body .has-secion-banner .fix-logo .menu-row .menu-only-wr {
  border-left-color: #f0382a;
}
.mega-menu ul.nav li.active,
.mega-menu ul.nav li:hover {
  background-color: #f35c50;
}
.bx-ie .mega-menu table td.active .wrap>a,
.bx-ie .mega-menu table td:not(.search-item):hover .wrap>a {
  color: #f35c50;
}
.bx-ie .mega-menu table td.active .wrap>a .svg path {
  fill: #f35c50;
}
.topmenu-LIGHT .mega-menu table td.active .wrap > .more-items span:before,
.topmenu-LIGHT .mega-menu table td:not(.search-item):hover .wrap > .more-items span:before {
  background-color: #f35c50;
}
.mega-menu table td .wrap > a .line-wrapper .line {
  background-color: #f35c50;
}
.topmenu-LIGHT .mega-menu table .tail {
  border-bottom: 6px solid #f24a3d;
}
.bx-ie .mega-menu table .wide_menu .dropdown-menu li>a:hover,
.bx-ie .mega-menu table .wide_menu .dropdown-submenu:hover>a,
.bx-ie .mega-menu table .dropdown-submenu:hover>a:after,
.bx-ie .mega-menu table .dropdown-menu li.active>a,
.bx-ie .mega-menu table .dropdown-submenu.active>a,
.bx-ie .mega-menu table .dropdown-submenu.active>a:after {
  color: #f35c50;
}
body .mega-menu table td .wrap > .dropdown-menu > li:first-of-type:before {
  background-color: #f35c50;
}
.menu-row.middle-block.bglight .mega-menu table td.icon.sale_icon .wrap > a:before {
  background-color: #f35c50;
}
.top_big_banners.half_block .flexslider .banner_title .head-title:after {
  background-color: #f35c50;
}
.bx-ie body .wrapper1 .menu-wrapper .menu_top_block.catalog_block .menu > li:not(.current):hover > a,
.bx-ie body .wrapper1 .menu-wrapper .menu_top_block.catalog_block .menu li:hover>a,
.bx-ie body .wrapper1 .menu-wrapper .menu_top_block.catalog_block .menu li.current>a,
.bx-ie .top-block .menu.topest li.current>a {
  color: #f35c50;
}
.bx-ie .top-block .menu.topest li.current>a .svg path,
.bx-ie body .wrapper1 .menu-wrapper .menu_top_block.catalog_block .menu>li:not(.current):hover>a .svg path,
.bx-ie .wrapper1.sticky_menu .menu-wrapper .menu_top_block.catalog_block .menu>li.current a .svg svg path {
  fill: #f35c50;
}
.bx-ie .wrapper1.sticky_menu .menu-wrapper .menu_top_block.catalog_block .menu>li .image .svg-inline-cat_icons * {
  fill: #f35c50;
}
.bx-ie .wrapper1.sticky_menu .menu-wrapper .menu_top_block.catalog_block .menu>li .image .svg-inline-picture * {
  fill: #f35c50;
}
.tabs .nav-tabs li.active:after {
  background: #f35c50;
}
.catalog_detail .active .title-tab-heading,
.catalog_detail .title-tab-heading:focus,
.catalog_detail .title-tab-heading:active,
.catalog_detail .title-tab-heading:hover {
  color: #f35c50;
}
.style-switcher .switch:hover,
.style-switcher .switch_presets:hover {
  background: #f35c50;
}
.style-switcher .header .header-inner:hover,
.style-switcher .header .header-inner:active,
.style-switcher .header .header-inner:focus {
  background-color: #f35c50;
  border-color: #f35c50;
}
.style-switcher .header .save_btn:hover {
  background: #f35c50;
}
.style-switcher .options .link-item.preset-block:hover .info .icon,
.style-switcher .options .link-item.preset-block.current .info .icon {
  border-color: #f35c50;
  background: #f35c50;
}
.style-switcher .options .preset-block.current {
  border-color: #f35c50;
}
.style-switcher .options > span.current:not(.block),
.style-switcher .options .link-item.current:not(.block):not(.preset-block) {
  background: #f35c50;
}
.style-switcher .options .link-item.current.block {
  border-color: #f35c50;
}
.style-switcher .tooltip-link:hover {
  background: #f35c50;
  border-color: #f35c50;
}
.style-switcher .options > div.base_color.current > span,
.style-switcher .options > div.base_color.current > span:hover {
  border-color: #f35c50;
}
.style-switcher .left-block .section-block.active:before {
  background: #f35c50;
}
.style-switcher .block-title .dotted-block {
  color: #f35c50;
}
.uploader:hover .action {
  background-color: #f5776d;
  border-color: #f5776d;
}
p.drop-caps:first-child:first-letter {
  color: #f35c50;
}
p.drop-caps.secundary:first-child:first-letter {
  background-color: #f35c50;
}
.label-info {
  background-color: #f35c50;
}
section.toggle label {
  color: #f35c50;
  border-color: #f35c50;
}
section.toggle.active > label {
  background-color: #f35c50;
  border-color: #f35c50;
}
.progress-bar-primary {
  background-color: #f35c50;
}
.slider-container .tp-caption a:not(.btn):hover {
  color: #f68076;
}
.tp-bullets .bullet.selected,
.tp-bullets .bullet:hover {
  background: #f35c50 !important;
}
.fa.colored {
  background: #f35c50;
}
.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
  background: #f35c50;
}
.page-top-wrapper.color {
  background: #f35c50;
}
.viewed_block .item_block:hover a {
  color: #f35c50;
}
.basket_fill_COLOR .wrap_cont .opener {
  background: #f35c50;
}
.basket_fill_COLOR .wrap_cont .opener >div:hover,
.basket_fill_COLOR .header-cart .basket_fly .opener >div.cur,
.basket_bottom_block.basket_fill_COLOR .maxwidth-theme .top-block-item .inner-table-block a:not(.basket):hover {
  background: #f5776d;
}
.basket_fill_COLOR.basket_bottom_block {
  background: #f35c50;
}
.basket_bottom_block .maxwidth-theme .top-block-item .inner-table-block a.basket {
  background: #f24f42;
}
#footer .footer_inner .line,
.page_not_found td.image img {
  background: #f68076;
}
.bx-ie .header-cart .basket_fly .opener >div.cur svg path {
  fill: #f35c50;
}
.header-cart .basket_fly .items .item .buy_block .counter_block .plus:hover:before,
.header-cart .basket_fly .items .item .buy_block .counter_block .plus:hover:after,
.header-cart .basket_fly .items .item .buy_block .counter_block .minus:hover:after {
  background-color: #f35c50;
}
.subscribe-form input.send_btn {
  background-color: #f35245;
}
.bx_ordercart .bx_ordercart_order_pay .bx_bt_button {
  background: #f35c50;
}
.left_block .subscribe-form .wrap_bg {
  background-color: #f35245;
}
.news_blocks .info_block .item:before {
  background-color: #f35245;
}
.bx_ordercart .bx_ordercart_order_pay .bx_bt_button:hover {
  background: #f5776d;
}
a .zoom {
  background: #f35c50;
}
.bx_ordercart .bx_item_detail_size_small_noadaptive .bx_size ul li.bx_active a {
  background: #da9188;
  color: #fff;
}
.bx_ordercart .bx_item_detail_size_small_noadaptive .bx_size ul li:not(.bx_active):hover a {
  color: #f24537;
}
.bx-ie a.dark_link:hover,
.bx-ie .dark_link:hover span,
.bx-ie .dl:hover a,
.bx-ie .selected a.dark_link,
.bx-ie a.muted:hover,
.bx-ie .hover_color_theme:hover {
  color: #f24537 !important;
}
.bx-ie .menu_top_block.catalog_block .dropdown>li.full>.dropdown>li>a:hover span,
.bx-ie body .menu_top_block.catalog_block .v_bottom li.current>a,
.bx-ie header .menu_top_block.catalogfirst li.full>.dropdown>li:not(.current)>a {
  color: #f24537;
}
.middle_phone .phone_wrap .icons,
.top_slider_wrapp .flexslider .slides > li:after {
  background-color: #f35245;
}
.bg_color_theme {
  background: #f35c50;
}
.text-color-theme,
.item-views.news2 .compact .item-wrapper .item .inner-text .title a:hover,
.bottom_nav.mobile_slider .btn.btn-transparent-border-color:hover {
  color: #f35c50;
}
.basket_normal .basket_wrapp .wraps_icon_block.basket .count span {
  background-color: #f35245;
}
.bx_filter .bx_filter_parameters_box_title:hover:after {
  color: #f24537;
}
.bx_filter .bx_filter_param_label.active .bx_filter_btn_color_icon,
.bx_catalog_item_scu ul li.active .cnt_item,
.bx_scu_scroller_container ul li.bx_active .cnt_item {
  box-shadow: 0 0 0 2px #f35245;
}
.bx_filter_vertical input[type="checkbox"]:not(:checked) + label.sku:hover span,
.sku_props .bx_item_detail_size ul li:not(.active):hover span,
.bx_item_list_you_looked_horizontal .bx_item_detail_size ul li:hover span {
  color: #f24537;
}
.bx_filter_vertical input[type="checkbox"] + label.sku:hover,
.sku_props .bx_item_detail_size ul li:hover,
.bx_item_list_you_looked_horizontal .bx_item_detail_size ul li:hover {
  border-color: #f24537;
}
body .bx_filter.compact .bx_filter_parameters_box.set .title.bx_filter_parameters_box_title {
  border: 1px solid #f35c50;
}
.more_text_ajax:after {
  background-color: #f35245;
}
.bx_ordercart .bx_sort_container a.current:after {
  background: #e39693;
}
.bx_ordercart .bx_ordercart_order_pay .bx_ordercart_order_pay_center .checkout {
  color: #f35c50;
  border: 1px solid #f35c50;
  background: #ffffff;
}
.bx_ordercart .bx_ordercart_order_pay .bx_ordercart_order_pay_center .checkout:hover {
  color: #fff;
  border-color: #f35c50;
  background: #f35c50;
}
.button.transparent:not(.text):hover {
  border-color: #e39693;
  background: #e39693;
  color: #fff;
}
.button.transparent.grey_br:hover {
  border-color: #e39693;
  background: #fff;
}
.bx_ordercart tbody td.control a:first-of-type:hover,
.bx_ordercart #basket_items_delayed tbody td.control a:last-of-type:hover,
.bx_ordercart #basket_items_subscribed  tbody td.control a:first-of-type:hover,
.bx_ordercart #basket_items_not_available  tbody td.control a:last-of-type:hover,
.bx_ordercart .bx_ordercart_order_pay .bx_ordercart_coupon:not(:first-of-type) > span:hover {
  background-color: #e39693;
}
.bx_ordercart .bx_sort_container a,
.bx_item_list_you_looked_horizontal .bx_catalog_item .bx_catalog_item_title a:hover,
.bx_item_list_you_looked_horizontal .bx_catalog_item:hover .bx_catalog_item_title a {
  color: #f35245;
}
.bx_item_list_you_looked_horizontal .bx_catalog_item_controls .bx_catalog_item_controls_blocktwo a,
.bx_item_list_you_looked_horizontal .bx_catalog_item_controls .bx_catalog_item_controls_blockone a {
  color: #FFF;
  background: #f35245;
}
.bx_item_list_you_looked_horizontal .bx_catalog_item_controls .bx_catalog_item_controls_blocktwo a:hover,
.bx_item_list_you_looked_horizontal .bx_catalog_item_controls .bx_catalog_item_controls_blockone a {
  background: #f5776d;
}
.sale_order_full_table input[name="BuyButton"] {
  background-color: #f4574a;
}
.sale_order_full_table input[name="BuyButton"] {
  background-color: #f5776d;
}
.bx_ordercart .bx_item_detail_size_small_noadaptive ul li:active a,
.bx_ordercart .bx_item_detail_size_small_noadaptive ul li.bx_active a,
.bx_ordercart .bx_item_detail_size_small_noadaptive ul li:hover a {
  border-color: #f5776d;
}
.color_link {
  color: #f24537 !important;
}
.detail .gallery-block .small-gallery ul.items li.flex-active-slide,
.detail .gallery-block .small-gallery ul.items li:hover {
  border-color: #f35c50;
}
.page_error_block svg path {
  fill: #f35c50;
}
.sections_wrapper svg path {
  fill: #f35c50;
}
.sale-personal-section-index-block-ico i,
.personal_wrapper .sale-personal-account-wallet-container .sale-personal-account-wallet-list-item:before {
  background-color: #f35c50;
}
.sale-personal-section-index-block-ico i.fa {
  color: #f35c50;
}
.sale-acountpay-fixedpay-list .sale-acountpay-fixedpay-item {
  border-color: #e39693;
  color: #f24537;
}
.sale-acountpay-fixedpay-list .sale-acountpay-fixedpay-item:hover {
  background-color: #e39693;
}
.bx-sap .sale-acountpay-pp-company.bx-selected .sale-acountpay-pp-company-graf-container,
.bx-sap .sale-acountpay-pp-company:hover .sale-acountpay-pp-company-graf-container,
.sale-order-payment-change-pp-company:hover .sale-order-payment-change-pp-company-image {
  border-color: #e39693 !important;
}
.bx-sap .sale-acountpay-pp-company:not(.bx-selected):hover .sale-acountpay-pp-company-smalltitle,
.sale-order-payment-change-pp-company:hover .sale-order-payment-change-pp-company-smalltitle {
  color: #f24537 !important;
}
.bx-sap .sale-acountpay-pp-company.bx-selected .sale-acountpay-pp-company-graf-container:before,
.bx-sap .sale-acountpay-pp-company:not(.bx-selected):hover .sale-acountpay-pp-company-graf-container:before {
  background-color: #f35c50;
  border: none;
}
.sale-personal-profile-list-container>tbody>tr>td:first-child b {
  color: #f35c50;
}
.personal_wrapper .sale-profile-detail-link-list a:before {
  background-color: #f35c50;
}
.personal_wrapper input[type=submit]:last-of-type,
.personal_wrapper .orders_wrapper .sale-order-list-inner-row:last-of-type > div:last-of-type a,
.personal_wrapper .orders_wrapper .sale-order-detail-about-order-inner-container-repeat .sale-order-detail-about-order-inner-container-repeat-cancel {
  border-color: #f35c50;
  color: #f35c50 !important;
}
.personal_wrapper input[type=submit]:hover:last-of-type,
.personal_wrapper .orders_wrapper .sale-order-list-inner-row:last-of-type > div:last-of-type a:hover,
.personal_wrapper .orders_wrapper .sale-order-detail-about-order-inner-container-repeat .sale-order-detail-about-order-inner-container-repeat-cancel:hover {
  background: #f35c50;
  color: #fff !important;
}
.personal_wrapper .orders_wrapper .row.col-md-12.col-sm-12 > a,
.sale-order-detail-about-order-inner-container-list-item-link {
  color: #f35c50;
}
.personal_wrapper .orders_wrapper .sale-order-list-inner-row > div >a {
  background: #f35c50;
}
.personal_wrapper .orders_wrapper .sale-order-list-inner-row > div >a:hover {
  background: #f5776d;
}
.personal_wrapper .orders_wrapper .sale-order-list-change-payment {
  color: #f35c50;
}
.personal_wrapper .orders_wrapper .sale-order-list-button {
  background-color: #f35c50;
}
.personal_wrapper .orders_wrapper .sale-order-list-button:hover {
  background-color: #f5776d;
}
.bx_my_order_cancel a,
.personal_wrapper .orders_wrapper .sale-order-list-inner-row>div>a.sale-order-list-cancel-payment {
  color: #f35c50 !important;
}
.personal_wrapper form[name=ShopForm] input[type=submit],
.personal_wrapper .sale-order-list-inner-row-template input[type=submit],
.personal_wrapper .sale-order-detail-inner-row-template input[type=submit],
.personal_wrapper .sale-order-payment-change-pp input[type=submit],
.personal_wrapper .orders_wrapper .sale-order-detail-about-order-inner-container-repeat .sale-order-detail-about-order-inner-container-repeat-button,
.personal_wrapper .sale-order-detail-payment-options-methods-button-element,
.personal_wrapper .sale-order-detail-payment-options-methods-button-element-new-window {
  background: #f35c50;
  color: #fff !important;
}
.personal_wrapper form[name=ShopForm] input[type=submit]:hover,
.personal_wrapper .sale-order-list-inner-row-template input[type=submit]:hover,
.personal_wrapper .sale-order-detail-inner-row-template input[type=submit]:hover,
.personal_wrapper .sale-order-payment-change-pp input[type=submit]:hover,
.personal_wrapper .orders_wrapper .sale-order-detail-about-order-inner-container-repeat .sale-order-detail-about-order-inner-container-repeat-button:hover,
.personal_wrapper .sale-order-detail-payment-options-methods-button-element:hover,
.personal_wrapper .sale-order-detail-payment-options-methods-button-element-new-window:hover {
  background: #f5776d;
  border-color: transparent;
}
.reviews.item-views.front .item .image:before {
  background-color: #f35c50;
}
.item-views .item .bottom-props .value {
  background-color: #f35c50;
}
.bx-ie footer .light .bottom-menu .item .title a:hover,
.bx-ie footer .light .bottom-menu .wrap .item .title a:hover,
.bx-ie footer .light .info .email a:hover,
.bx-ie footer .light .info .address a:hover {
  color: #f35c50;
}
footer .info .subscribe_button .btn:hover {
  background-color: #f5776d;
  border-color: #f5776d;
}
.catalog-delivery-error-icon svg path,
.catalog-delivery-error-icon svg circle {
  fill: #f35c50;
}
.catalog-delivery-item.open .catalog-delivery-item-head:before {
  background-color: #f57667;
}
.catalog-delivery-item .catalog-delivery-item-head:hover .catalog-delivery-item-opener {
  background-color: #f57667;
  border-color: #f57667;
}
.catalog-delivery-item.open .catalog-delivery-item-head:hover .catalog-delivery-item-opener {
  background-color: #f4998c;
  border-color: #f4998c;
}
.catalog-delivery .bx-sls .quick-location-tag {
  border-color: #ffffff;
  color: #f79084;
}
.catalog-delivery .bx-sls .quick-location-tag:hover {
  background-color: #f57667;
  border-color: #f57667;
}
.bottom-icons-panel__content-link--active {
  color: #f35c50 !important;
}
.bottom-icons-panel__content-link--active svg path {
  fill: #f35c50 !important;
}
.bottom-icons-panel__content-link--active svg rect,
.bottom-icons-panel__content-link--active svg circle {
  stroke: #f35c50 !important;
}
.services_order_item .services_order_item_title:before,
.services_top_hover_item .services_top_hover_item_title:before {
  background-color: #f57667;
}
