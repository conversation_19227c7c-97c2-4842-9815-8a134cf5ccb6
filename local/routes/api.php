<?php

use Bitrix\Main\Application;
use Bitrix\Main\Routing\RoutingConfigurator;

use DmitrievLabControllers\CouponController;
use DmitrievLabControllers\ExchangeController;
use DmitrievLabControllers\FavoriteController;
use DmitrievLabControllers\LoyaltyController;
use DmitrievLabControllers\CartController;
use DmitrievLabControllers\OrderController;
use DmitrievLabControllers\SeoController;
use DmitrievLabControllers\UserController;
use DmitrievLabControllers\DeliveryAddressController;
use DmitrievLabControllers\DeliverySuggestionsController;
use DmitrievLabControllers\RefundProductsController;
use DmitrievLabControllers\PaymentController;
use DmitrievLabControllers\ProfileController;

include Application::getDocumentRoot() . '/local/Controllers/LoyaltyController.php';
include Application::getDocumentRoot() . '/local/Controllers/CartController.php';
include Application::getDocumentRoot() . '/local/Controllers/FavoriteController.php';
include Application::getDocumentRoot() . '/local/Controllers/UserController.php';
include Application::getDocumentRoot() . '/local/Controllers/OrderController.php';
include Application::getDocumentRoot() . '/local/Controllers/CouponController.php';
include Application::getDocumentRoot() . '/local/Controllers/ExchangeController.php';
include Application::getDocumentRoot() . '/local/Controllers/DeliveryAddressController.php';
include Application::getDocumentRoot() . '/local/Controllers/DeliverySuggestionsController.php';
include Application::getDocumentRoot() . '/local/Controllers/RefundProductsController.php';
include Application::getDocumentRoot() . '/local/Controllers/PaymentController.php';
include Application::getDocumentRoot() . '/local/Controllers/ProfileController.php';
include Application::getDocumentRoot() . '/local/Controllers/SeoController.php';

return function (RoutingConfigurator $routes) {
    $routes->prefix('LoyaltyApi')->group(function (RoutingConfigurator $routes) {
        $routes->get('cardIsFilled', [LoyaltyController::class, 'cardIsFilled']);
        $routes->get('hintData', [LoyaltyController::class, 'hintData']);
        $routes->get('bonuses', [LoyaltyController::class, 'getBonusesInfo']);
        $routes->get('bonusesQr', [LoyaltyController::class, 'getBonusesQr']);
        $routes->post('plasticCard', [LoyaltyController::class, 'registrationPlasticCard']);
        $routes->post('virtualCard', [LoyaltyController::class, 'registrationVirtualCard']);
    });

    $routes->prefix('CartApi')->group(function (RoutingConfigurator $routes) {
        $routes->post('quantity', [CartController::class, 'quantity']);
        $routes->post('remove', [CartController::class, 'remove']);
        $routes->post('add', [CartController::class, 'add']);
        $routes->post('clear', [CartController::class, 'clearBasket']);
        $routes->get('items', [CartController::class, 'getBasketItems']);
        $routes->get('info', [CartController::class, 'getBasketInfo']);
        $routes->get('crossSales', [CartController::class, 'getCrossSalesProducts']);
        $routes->get('deliveryInfo', [CartController::class, 'getDeliveryInfo']);
        $routes->get('count', [CartController::class, 'getCount']);
    });

    $routes->prefix('couponsApi')->group(function (RoutingConfigurator $routes) {
        $routes->get('all', [CouponController::class, 'all']);
        $routes->get('isActive', [CouponController::class, 'isActive']);
        $routes->post('add', [CouponController::class, 'add']);
        $routes->post('delete', [CouponController::class, 'delete']);
    });

    $routes->prefix('FavoriteApi')->group(function (RoutingConfigurator $routes) {
        $routes->get('items', [FavoriteController::class, 'getItems']);
        $routes->get('count', [FavoriteController::class, 'getCount']);
        $routes->post('add', [FavoriteController::class, 'add']);
        $routes->post('remove', [FavoriteController::class, 'remove']);
    });


    $routes->prefix('UserApi')->group(function (RoutingConfigurator $routes) {
        $routes->get('auth/check', [UserController::class, 'checkAuthorization']);
    });

    $routes->prefix('OrderApi')->group(function (RoutingConfigurator $routes) {
        $routes->get('intervals', [OrderController::class, 'getIntervals']);
        $routes->get('initData', [OrderController::class, 'initDataOrderData']);
        $routes->post('create', [OrderController::class, 'create']);
        $routes->post('info', [OrderController::class, 'orderInfo']);
    });

    $routes->prefix('ExchangeApi')->group(function (RoutingConfigurator $routes) {
        $routes->post('sync', [ExchangeController::class, 'sync']);
        $routes->post('products', [ExchangeController::class, 'products']);
        $routes->post('categories', [ExchangeController::class, 'categories']);
        $routes->post('orderBonuses', [ExchangeController::class, 'orderBonuses']);
    });

    $routes->prefix('DeliveryApi')->group(function (RoutingConfigurator $routes) {
        $routes->post('setAddress', [DeliveryAddressController::class, 'setAddress']);
        $routes->get('getAddress', [DeliveryAddressController::class, 'getAddress']);
        $routes->post('getAddressByGeo', [DeliveryAddressController::class, 'getAddressByGeo']);
        $routes->post('getSuggestions/suggest/address', [DeliverySuggestionsController::class, 'getSuggestionsByAddress']);
    });

    $routes->prefix('RefundApi')->group(function (RoutingConfigurator $routes) {
        $routes->post('refund', [RefundProductsController::class, 'refund']);
    });

    $routes->prefix('PaymentApi')->group(function (RoutingConfigurator $routes) {
        $routes->post('yookassa/webhook', [PaymentController::class, 'webhook']);
    });

    $routes->prefix('ProfileApi')->group(function (RoutingConfigurator $routes) {
        $routes->post('delete', [ProfileController::class, 'deleteAction']);
    });

    $routes->prefix('SeoApi')->group(function (RoutingConfigurator $routes) {
        $routes->get('sitemap', [SeoController::class, 'sitemap']);
    });
};