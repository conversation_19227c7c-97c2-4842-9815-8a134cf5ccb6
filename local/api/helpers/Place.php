<?php

use D<PERSON><PERSON>vLab\Helpers\Config;

class Place
{
    const EARTH_RADIUS = 6371;
    private string|null $address = null;
    private float $x = 0;
    private float $y = 0;

    const FIRST_STORE = [
        [40.85750680371071, 57.77208865868887],
        [40.909963875009986, 57.76674897679367],
        [40.94489696521995, 57.75303211559894],
        [40.966311721994856, 57.730931935594995],
        [40.990515976145204, 57.707187257649146],
        [40.99718903893925, 57.70981996136528],
        [41.004055494017386, 57.714275825473074],
        [41.0011904651309, 57.71839895531217],
        [41.0175841266299, 57.722073060411354],
        [41.01157182324037, 57.72621218779592],
        [41.02478974926576, 57.727957056201085],
        [41.035346923948374, 57.722171115606805],
        [41.04095793888673, 57.723688265247546],
        [41.03326861036278, 57.72952291398061],
        [41.038066020246575, 57.732721247436615],
        [41.02793799900635, 57.7388727171296],
        [41.01060019993407, 57.74424291775076],
        [41.013002831566794, 57.746351480428],
        [41.025877434838286, 57.745617184256176],
        [41.02698776193591, 57.74762295172282],
        [41.01385566659899, 57.747668843116436],
        [41.01814720102283, 57.75115641793935],
        [41.02930519052479, 57.74968800654684],
        [41.040707462825196, 57.75491867692064],
        [41.04044111757766, 57.76045502509323],
        [41.05241695000141, 57.77929771044596],
        [41.05799594475237, 57.78365329709825],
        [41.056965976490666, 57.78709154559415],
        [41.03308606441186, 57.78848085802498],
        [41.03179860408471, 57.79448531162691],
        [41.03119778926538, 57.80250496389686],
        [41.02734977381478, 57.80928924225628],
        [41.04159766810189, 57.81176304951588],
        [41.0478633083607, 57.82110700655],
        [41.04202682154428, 57.824358499871316],
        [41.038336101939784, 57.82252670885549],
        [41.031653691932966, 57.824727174635],
        [41.00061726068785, 57.82290863800564],
        [40.99478077387146, 57.81333611210299],
        [40.96343964226465, 57.81685406223388],
        [40.95537155754784, 57.81831973399284],
        [40.94661682732324, 57.819693746951614],
        [40.94387024529199, 57.81351027469451],
        [40.939578710868155, 57.81442641182531],
        [40.93934446848703, 57.82487098504348],
        [40.91720015086007, 57.82725213969466],
        [40.92166334666085, 57.83219711021846],
        [40.89359671152901, 57.8386977943172],
        [40.87291266111932, 57.831883498501405],
        [40.868356854386825, 57.82608780439494],
        [40.87253560762347, 57.82431986542154],
        [40.88601102571428, 57.81598445267708],
        [40.88906368467613, 57.806174889630014],
        [40.901766626570655, 57.8012263352451],
        [40.907045883547376, 57.79861534585125],
        [40.90524343908936, 57.79485738205547],
        [40.90050797863746, 57.79276568548417],
        [40.89698892040993, 57.79244484683136],
        [40.89578729077125, 57.79047391800905],
        [40.87810616894508, 57.786531736242026],
        [40.87003808422829, 57.78689846905634],
        [40.868664793212666, 57.791573984627334],
        [40.86480241223122, 57.79235317811897],
        [40.8619699995115, 57.79051975479006],
        [40.8605108778074, 57.787265198130875],
        [40.861455015380635, 57.78346020210097],
        [40.858708433349385, 57.77745390729837],
        [40.86094003124977, 57.77690366221093],
        [40.85750680371071, 57.77208865868887]
    ];

    const SECOND_STORE = [
        [40.744997291758004, 57.767866025951164],
        [40.74722825588042, 57.761320292976094],
        [40.749476669003265, 57.75459770180539],
        [40.75771641509703, 57.755790627963925],
        [40.757888076473975, 57.757855214454686],
        [40.76235127227476, 57.75904803268685],
        [40.77368092315366, 57.76051606241152],
        [40.78275061664563, 57.76106241244931],
        [40.78609801349621, 57.75982378511426],
        [40.78987456378918, 57.75950265255193],
        [40.796655188178825, 57.75886037883331],
        [40.801597423136194, 57.75849697012812],
        [40.81215459781881, 57.7590933735529],
        [40.813527888834436, 57.756524180717285],
        [40.81593114811179, 57.75170644996625],
        [40.829618904552824, 57.75012999658088],
        [40.84026190992392, 57.74223635634909],
        [40.84455344434773, 57.74251174501352],
        [40.84797417854744, 57.7406470080218],
        [40.830133933691364, 57.73653533668385],
        [40.83940364804683, 57.72726122236676],
        [40.84000446286617, 57.72583775595585],
        [40.84822685471439, 57.724143376349815],
        [40.85550685423927, 57.722044478038995],
        [40.8616866638096, 57.71841629868797],
        [40.863059954825225, 57.71483368744219],
        [40.8652915527256, 57.71419061697667],
        [40.87119534610335, 57.71567699960138],
        [40.88441327212874, 57.71728458800057],
        [40.89622611516212, 57.718518026358545],
        [40.911617154079586, 57.72002624187298],
        [40.92128446317055, 57.7215257183707],
        [40.927893426183246, 57.72524542480995],
        [40.931154992345355, 57.72868925523377],
        [40.93321492886879, 57.731030871982576],
        [40.93681981778482, 57.73341824614713],
        [40.94317128873207, 57.73470368980531],
        [40.951825198638275, 57.735612738622606],
        [40.94119095547758, 57.74441059322282],
        [40.93245151539082, 57.750708193038086],
        [40.90206745167009, 57.76291219736187],
        [40.88318470020527, 57.76438006950878],
        [40.86516025562517, 57.762820453365094],
        [40.84919574756854, 57.76621482551392],
        [40.79151752491228, 57.781531387416436],
        [40.79031589527361, 57.77914720151607],
        [40.78739765186541, 57.779514009632635],
        [40.77709796924822, 57.77465349852949],
        [40.744997291758004, 57.767866025951164]
    ];

    public function __construct(?string $address = null)
    {
        $this->address = $address;
    }

    public function getCoords(): void
    {
        $url = Config::get('DADATA_URL');


        $ch = curl_init();

        $headers = array();
        $headers[] = "Content-Type: application/json";
        $headers[] = "Accept: application/json";
        $headers[] = sprintf('Authorization: Token %s', Config::get('DADATA_TOKEN'));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, '{ "query": "' . $this->address . '", "count": 1 }');
        curl_setopt($ch, CURLOPT_POST, 1);

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3');

        $response = curl_exec($ch);

        curl_close($ch);

        $data = json_decode($response, true);

        if (isset($data['suggestions'][0]['data'])) {
            $this->y = $data['suggestions'][0]['data']['geo_lat'];
            $this->x = $data['suggestions'][0]['data']['geo_lon'];
        }
    }

    public function setLat(float $y): void
    {
        $this->y = $y;
    }

    public function setLon(float $x): void
    {
        $this->x = $x;
    }

    public function getLat(): float
    {
        return $this->y;
    }

    public function getLon(): float
    {
        return $this->x;
    }

    public static function getDistance(Place $from, Place $to): int
    {
        $latFrom = deg2rad($from->getLat());
        $lonFrom = deg2rad($from->getLon());
        $latTo = deg2rad($to->getLat());
        $lonTo = deg2rad($to->getLon());

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $angle = 2 * asin(sqrt(pow(sin($latDelta / 2), 2) +
            cos($latFrom) * cos($latTo) * pow(sin($lonDelta / 2), 2)));
        return $angle * Place::EARTH_RADIUS;
    }
}
