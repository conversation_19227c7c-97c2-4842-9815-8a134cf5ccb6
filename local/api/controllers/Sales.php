<?php

namespace Controllers;

use Bitrix\Catalog\PriceTable;
use CFile;
use CCatalogProduct;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Domains\Catalog\PriceTypes\StorePriceType;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Repository\IblockRepository;

class Sales
{
    public function all()
    {
        \Bitrix\Main\Loader::includeModule('iblock');

        $sales = [];

        $resSale = \CIBlockElement::GetList(
            [
                'SORT' => 'ASC'
            ],
            [
                'IBLOCK_ID' => IblockRepository::getIdByCode('aspro_max_stock'),
                'ACTIVE' => "Y",
            ],
            false,
            false,
            [
                'SORT',
                'ID',
                'ACTIVE_FROM',
                'ACTIVE_TO',
                'NAME',
                'PREVIEW_PICTURE',
                'DETAIL_PICTURE',
                'PREVIEW_TEXT',
                'DETAIL_TEXT',
                'PROPERTY_MOBILE_COLOR',
                'PROPERTY_MOBILE_IMAGE',
                'PROPERTY_MOBILE_DETAIL_IMAGE'
            ]
        );

        while ($sale = $resSale->fetch()) {
            $data = [
                'DETAIL_PICTURE' => CFile::getPath($sale['PROPERTY_MOBILE_DETAIL_IMAGE_VALUE']),
                'PREVIEW_PICTURE' => CFile::getPath($sale['PROPERTY_MOBILE_IMAGE_VALUE']),
                'COLOR' => $sale['PROPERTY_MOBILE_COLOR_VALUE'],
                'SORT' => $sale['SORT'],
                'ID' => $sale['ID'],
                'ACTIVE_FROM' => $sale['ACTIVE_FROM'],
                'ACTIVE_TO' => $sale['ACTIVE_TO'],
                'NAME' => $sale['NAME'],
                'PREVIEW_TEXT' => $sale['PREVIEW_TEXT'],
                'DETAIL_TEXT' => $sale['DETAIL_TEXT'],
            ];

            $sales[] = $data;
        }

        response()->json($sales);
    }

    public function one()
    {
        \Bitrix\Main\Loader::includeModule('iblock');
        $id = request()->get('sale_id');

        $sales = [];

        $resSale = \CIBlockElement::GetList(
            [
                'SORT' => 'ASC'
            ],
            [
                'IBLOCK_ID' => IblockRepository::getIdByCode('aspro_max_stock'),
                'ID' => $id,
                'ACTIVE' => "Y",
            ],
            false,
            false,
            [
                'SORT',
                'ID',
                'ACTIVE_FROM',
                'ACTIVE_TO',
                'NAME',
                'PREVIEW_PICTURE',
                'DETAIL_PICTURE',
                'PREVIEW_TEXT',
                'DETAIL_TEXT',
                'PROPERTY_MOBILE_COLOR',
                'PROPERTY_LINK_GOODS_FILTER',
                'PROPERTY_MOBILE_IMAGE',
                'PROPERTY_MOBILE_DETAIL_IMAGE'
            ]
        );

        while ($sale = $resSale->fetch()) {
            $sale['DETAIL_PICTURE'] = CFile::getPath($sale['PROPERTY_MOBILE_DETAIL_IMAGE_VALUE']);
            $sale['PREVIEW_PICTURE'] = CFile::getPath($sale['PROPERTY_MOBILE_IMAGE_VALUE']);
            $sale['COLOR'] = $sale['PROPERTY_MOBILE_COLOR_VALUE'];
            $sale['PROPERTY_LINK_GOODS_FILTER_VALUE'] = json_decode($sale['PROPERTY_LINK_GOODS_FILTER_VALUE']);
            $logic = $sale['PROPERTY_LINK_GOODS_FILTER_VALUE']->DATA->All;

            foreach ($sale['PROPERTY_LINK_GOODS_FILTER_VALUE']->CHILDREN as $value) {
                switch ($value->CLASS_ID) {
                    case 'CondIBSection':
                        $sections = $value->DATA->value;
                        break;
                    case 'CondIBElement':
                        $products = $value->DATA->value;
                        break;
                }
            }

            $products = \CIBlockElement::GetList(
                [
                    'SORT' => 'ASC'
                ],
                [
                    'IBLOCK_ID' => IblockRepository::getIdByCode('aspro_max_catalog'),
                    'ACTIVE' => "Y",
                    array(
                        "LOGIC" => $logic,
                        array("SECTION_ID" => $sections, "INCLUDE_SUBSECTIONS" => "Y"),
                        array("ID" => $products)
                    )
                ],
                false,
                false,
                ['ID', 'NAME', 'PREVIEW_PICTURE', 'DETAIL_PICTURE']
            );

            $priceTypes = (new StorePriceType())
                ->setStore(request()->get('store_id') ?? 5)
                ->getCurrentPrice();

            while ($product = $products->fetch()) {
                $product_info = CCatalogProduct::GetByID($product['ID']);


                $discountPrice = PriceTable::getList(
                    [
                        "select" => [
                            "PRICE"
                        ],
                        "filter" => [
                            "=PRODUCT_ID" => $product["ID"],
                            "CATALOG_GROUP_ID" => $priceTypes->getDiscountPriceType()->getId()
                        ]
                    ]
                )->fetch();

                $basePrice = PriceTable::getList(
                    [
                        "select" => [
                            "PRICE"
                        ],
                        "filter" => [
                            "=PRODUCT_ID" => $product["ID"],
                            "CATALOG_GROUP_ID" => $priceTypes->getBasePriceType()->getId()
                        ]
                    ]
                )->fetch();

                $product['MEASURE'] = \Controllers\Products::$measure[$product_info['MEASURE'] - 1];
                $product['WEIGHT'] = $product_info['WEIGHT'];
                $product['QUANTITY'] = $product_info['QUANTITY'];
                $product['PREVIEW_PICTURE'] = CFile::getPath($product['PREVIEW_PICTURE']);
                $product['DETAIL_PICTURE'] = CFile::getPath($product['DETAIL_PICTURE']);

                $product['PRICE'] = $basePrice['PRICE'];
                $product['PRICE_DISCOUNT'] = $discountPrice['PRICE'];

                $sale['PRODUCTS'][] = $product;
            }

            unset($sale['PROPERTY_MOBILE_DETAIL_IMAGE_VALUE_ID'], $sale['PROPERTY_MOBILE_DETAIL_IMAGE_VALUE'], $sale['PROPERTY_MOBILE_IMAGE_VALUE'], $sale['PROPERTY_MOBILE_IMAGE_VALUE_ID'], $sale['PROPERTY_MOBILE_COLOR_VALUE'], $sale['PROPERTY_MOBILE_COLOR_VALUE_ID'], $sale['PROPERTY_LINK_GOODS_FILTER_VALUE'], $sale['PROPERTY_LINK_GOODS_FILTER_VALUE_ID']);
            $sales[] = $sale;
        }

        response()->json($sales);
    }
}
