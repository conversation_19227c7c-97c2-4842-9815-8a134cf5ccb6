<?php

\Bitrix\Main\Loader::includeModule('iblock');

$dataNotice = [];

$resNotice = \Bitrix\Iblock\Elements\ElementNoticeTable::GetList([
    'select' => ['ID', 'DATE_CREATE', 'PREVIEW_TEXT', 'NAME'],
    'filter' => [
        'ACTIVE' => "Y"
    ],
    'order' => array('SORT' => 'ASC'),
]);

while ($item = $resNotice->fetch()) {
    $dataNotice[] = [
        'DATE_CREATE' => $item['DATE_CREATE']->toString(),
        'NAME' => $item['NAME'],
        'TEXT' => $item['PREVIEW_TEXT']
    ];
}

response()->json($dataNotice);
