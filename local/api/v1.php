<?php

define("STATISTIC_SKIP_ACTIVITY_CHECK", "true");
define('STOP_STATISTICS', true);
define('PUBLIC_AJAX_MODE', true);

require($_SERVER["DOCUMENT_ROOT"] . "/bitrix/modules/main/include/prolog_before.php");

header('Content-type: application/json');

require './Exchange.php';

$exchange = new Exchange();

$json = @file_get_contents('php://input');
$result = $exchange->prepareData($json, __FILE__);

if ($result['success'] === true) {
    $result = $exchange->goods();
}

echo json_encode($result);


