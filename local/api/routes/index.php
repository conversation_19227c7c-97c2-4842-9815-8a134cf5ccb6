<?php

include_once($_SERVER['DOCUMENT_ROOT'] . '/local/api/controllers/Categories.php');
include_once($_SERVER['DOCUMENT_ROOT'] . '/local/api/controllers/Products.php');
include_once($_SERVER['DOCUMENT_ROOT'] . '/local/api/controllers/User.php');
include_once($_SERVER['DOCUMENT_ROOT'] . '/local/api/controllers/Order.php');
include_once($_SERVER['DOCUMENT_ROOT'] . '/local/api/controllers/Sales.php');
include_once($_SERVER['DOCUMENT_ROOT'] . '/local/api/controllers/App.php');
include_once($_SERVER['DOCUMENT_ROOT'] . '/local/api/controllers/Store.php');

return [
    'GET' => [
        'help' => [
            'controller' => $_SERVER['DOCUMENT_ROOT'] . '/local/api/page_help.php',
            'url' => '/api/help',
            'description' => 'Данные для экрана "Покупателям"',
            'response' => [
                'NAME' => 'Название для экрана',
                'DETAIL_TEXT' => 'Контент в экране'
            ]
        ],
        'stories' => [
            'controller' => $_SERVER['DOCUMENT_ROOT'] . '/local/api/stories.php',
            'url' => '/api/stories',
            'description' => 'Истории на главной страницы',
            'response' => [
                'PREVIEW_PICTURE' => 'Картинка на превью',
                'DETAIL_PICTURE' => 'Картинка в истории',
                'NAME' => 'Название истории',
                'TEXT' => 'Текст в истории',
                'BUTTON_TEXT' => 'Текст на кнопке',
                'TEXT_COLOR' => 'Цвет на кнопке'
            ]
        ],
        'notice_list' => [
            'controller' => $_SERVER['DOCUMENT_ROOT'] . '/local/api/notice.php',
            'url' => '/api/notice_list',
            'description' => 'Уведомления для моб приложения',
        ],
        'page_delivery' => [
            'controller' => $_SERVER['DOCUMENT_ROOT'] . '/local/api/page_delivery.php',
            'url' => '/api/page_delivery',
            'description' => 'Условия доставки',
        ],
        'page_society' => [
            'controller' => $_SERVER['DOCUMENT_ROOT'] . '/local/api/page_society.php',
            'url' => '/api/page_society',
            'description' => 'Экран клуб "Дом Еды"',
            'response' => [
                'name' => 'Название страницы',
                'preview_text' => 'Текст вначале страницы',
                'detail_text' => 'Текст страницы',
                'active_img' => 'Изображение страницы',
                'background_img' => 'Фоновое изображение страницы',
                'banner_text' => 'Текст на баннере',
                'label_button' => 'Текст на кнопке'
            ]
        ],
        'categories' => [
            'controller' => '\Controllers\Categories@list',
            'url' => '/api/categories',
            'description' => 'Вывод всех категорий',
            'response' => [
                'ID' => 'id категории',
                'NAME' => 'Название категории',
                'CODE' => 'Символьный код категории',
                'DETAIL_PICTURE' => 'Картинка внутри категории',
                'PICTURE' => 'Превью категории',
                'SORT' => 'Сортировка по умолчанию',
                'childs' => 'Дочерние категории'
            ],
        ],
        'sales' => [
            'controller' => '\Controllers\Sales@all',
            'url' => '/api/sales',
            'description' => 'Вывод всех акций',
            'response' => [
                'ID' => 'id акции',
                'NAME' => 'Название акции',
                'DETAIL_PICTURE' => 'Картинка внутри акции',
                'PREVIEW_PICTURE' => 'Превью акции',
                'SORT' => 'Сортировка по умолчанию',
                "ACTIVE_FROM" => "Дата активации",
                "ACTIVE_FROM_X" => "Дата + время активации",
                "ACTIVE_TO" => "Дата завершения акции",
                "ACTIVE_TO" => "Дата завершения акции",
                "PREVIEW_TEXT" => "Вводный текст для акции",
                "DETAIL_TEXT" => "Детальный текст для акции",
                "DETAIL_TEXT_TYPE" => 'Тип детального текста',
                "PREVIEW_TEXT_TYPE" => 'Тип вводного текста',
            ],
        ],
        'banners' => [
            'controller' => $_SERVER['DOCUMENT_ROOT'] . '/local/api/banners.php',
            'url' => '/api/banners',
            'description' => 'Вывод баннеров на главной странице',
            'response' => [
                'ID' => 'id баннера',
                'NAME' => 'Основной заголовок баннера',
                'PREVIEW_TEXT' => 'Подзаголовок баннера',
                'DETAIL_PICTURE' => 'Изображение баннера',
                'SORT' => 'Сортировка по умолчанию',
                "MOBILE_IMG" => "Изображение для мобильных устройств",
                "PREVIEW_TEXT_TYPE" => "Тип подзаголовка",
                "BUTTON_TEXT" => "Текст кнопки",
                "BUTTON_LINK" => "Ссылка кнопки",
            ],
        ],
        'news' => [
            'controller' => $_SERVER['DOCUMENT_ROOT'] . '/local/api/news.php',
            'url' => '/api/news',
            'description' => 'Вывод всех новостей',
            'response' => [
                'ID' => 'id новости',
                "DATE_CREATE" => 'Дата создания',
                'NAME' => 'Название новости',
                'SORT' => 'Сортировка по умолчанию',
                'PREVIEW_PICTURE' => 'Превью новости',
                "PREVIEW_TEXT" => "Вводный текст для новости",
                'DETAIL_PICTURE' => 'Картинка внутри новости',
                "DETAIL_TEXT" => "Детальный текст для новости",
                "DETAIL_TEXT_TYPE" => 'Тип детального текста',
                "PREVIEW_TEXT_TYPE" => 'Тип вводного текста',
            ],
        ],
        'delivery_info' => [
            'controller' => '\Controllers\Order@deliveryInfo',
            'url' => '/api/delivery_info',
            'description' => 'Информация по цене заказа',
        ],
        'get_interval' => [
            'controller' => '\Controllers\Order@getInterval',
            'url' => '/api/get_interval',
        ],
        'routes' => [
            'controller' => $_SERVER['DOCUMENT_ROOT'] . '/local/api/routes.php'
        ],
        'get_bonus' => [
            'controller' => '\Controllers\Order@getOrderBonus',
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'get_site_status' => [
            'controller' => '\Controllers\App@getSiteStatus',
            'url' => '/api/get_site_status',
        ],
    ],
    'POST' => [
        'check_version' => [
            'controller' => '\Controllers\App@checkVersion',
            'url' => '/api/check_version',
            'parameters' => [
                'version' => [
                    'required' => true,
                    'type' => 'string',
                ],
            ],
        ],
        'get_order_init_data' => [
            'controller' => '\Controllers\Order@getOrderInitData',
            'url' => '/api/get_order_init_data',
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'parameters' => [
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                ],
            ],
        ],
        'get_store' => [
            'controller' => '\Controllers\Store@getStore',
            'url' => '/api/get_store',
            'parameters' => [
                'address' => [
                    'required' => true,
                    'type' => 'string',
                ],
            ],
        ],
        'get_store_id_by_Geo' => [
            'controller' => '\Controllers\Store@getStoreIdByGeo',
            'url' => '/api/get_store_id_by_Geo',
            'parameters' => [
                'lat' => [
                    'required' => true,
                    'type' => 'float',
                ],
                'lon' => [
                    'required' => true,
                    'type' => 'float',
                ],
            ],
        ],
        'set_token' => [
            'controller' => $_SERVER['DOCUMENT_ROOT'] . '/local/api/token.php',
            'parameters' => [
                'token' => [
                    'required' => true,
                    'type' => 'string',
                ],
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'recipes' => [
            'controller' => $_SERVER['DOCUMENT_ROOT'] . '/local/api/recipes.php',
            'url' => '/api/recipes',
            'description' => 'Вывод всех рецептов',
            'parameters' => [
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                ],
            ],
            'response' => [
                'ID' => 'id рецепта',
                'NAME' => 'Название рецепта',
                'DETAIL_PICTURE' => 'Картинка внутри рецепта',
                'PREVIEW_PICTURE' => 'Превью рецепта',
                'SORT' => 'Сортировка по умолчанию',
                "DATE_CREATE" => 'Дата создания',
                "PREVIEW_TEXT" => "Вводный текст для рецепта",
                "DETAIL_TEXT" => "Детальный текст для рецепта",
                "DETAIL_TEXT_TYPE" => 'Тип детального текста',
                "PREVIEW_TEXT_TYPE" => 'Тип вводного текста',
            ],
        ],
        'order_addition' => [
            'controller' => '\Controllers\Products@orderAddition',
            'url' => '/api/order_addition',
            'parameters' => [
                'store_id' => [
                    'required' => true,
                    'type' => 'integer',
                ],
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'sale' => [
            'controller' => '\Controllers\Sales@one',
            'url' => '/api/sale',
            'description' => 'Вывод отдельной акции по id',
            'parameters' => [
                'sale_id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'id акции',
                ],
            ],
            'response' => [
                'ID' => 'id акции',
                'NAME' => 'Название акции',
                'DETAIL_PICTURE' => 'Картинка внутри акции',
                'PREVIEW_PICTURE' => 'Превью акции',
                'SORT' => 'Сортировка по умолчанию',
                "ACTIVE_FROM" => "Дата активации",
                "ACTIVE_FROM_X" => "Дата + время активации",
                "ACTIVE_TO" => "Дата завершения акции",
                "ACTIVE_TO" => "Дата завершения акции",
                "PREVIEW_TEXT" => "Вводный текст для акции",
                "DETAIL_TEXT" => "Детальный текст для акции",
                "DETAIL_TEXT_TYPE" => 'Тип детального текста',
                "PREVIEW_TEXT_TYPE" => 'Тип вводного текста',
            ],
        ],
        'get_favourite' => [
            'controller' => '\Controllers\User@getFavourite',
            'url' => '/api/get_favourite',
            'description' => 'Получить избранное',
            'response' => [
                'ID' => 'id товара',
                'NAME' => 'Название товара',
                'DETAIL_PICTURE' => 'Картинка внутри товара',
                'PREVIEW_PICTURE' => 'Превью товра',
                'PRODUCT_PRICE' => 'Цена',
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'delete' => [
            'controller' => '\Controllers\User@delete',
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'add_favourite' => [
            'controller' => '\Controllers\User@addFavourite',
            'url' => '/api/add_favourite',
            'description' => 'Добавить в избранное',
            'parameters' => [
                'product_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'id товара',
                ]
            ],
            'response' => [
                'status' => 'success | error'
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'clear_favourite' => [
            'controller' => '\Controllers\User@clearFavourite',
            'url' => '/api/clear_favourite',
            'response' => [
                'status' => 'success | error'
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'remove_favourite' => [
            'controller' => '\Controllers\User@removeFavourite',
            'url' => '/api/remove_favourite',
            'description' => 'Удалить из избранного',
            'parameters' => [
                'product_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'id товара',
                ]
            ],
            'response' => [
                'status' => 'success | error'
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'add_basket' => [
            'controller' => '\Controllers\Order@addBasket',
            'url' => '/api/add_basket',
            'parameters' => [
                'product_id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'id товара',
                ],
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Адрес доставки',
                ],
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'remove_basket' => [
            'controller' => '\Controllers\Order@removeFromBasket',
            'url' => '/api/remove_basket',
            'parameters' => [
                'product_id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'id товара',
                ],
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Адрес доставки',
                ],
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'clear_basket' => [
            'controller' => '\Controllers\Order@clearBasket',
            'url' => '/api/clear_basket',
            'parameters' => [
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Адрес доставки',
                ],
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'update_basket' => [
            'controller' => '\Controllers\Order@updateUserBasket',
            'url' => '/api/update_basket',
            'parameters' => [
                'product_id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'id товара',
                ],
                'type' => [
                    'required' => true,
                    'type' => 'string',
                ],
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Адрес доставки',
                ],
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'get_basket' => [
            'controller' => '\Controllers\Order@getBasket',
            'url' => '/api/get_basket',
            'parameters' => [
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Адрес доставки',
                ],
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'get_discount' => [
            'controller' => '\Controllers\Order@getDiscount',
            'description' => 'Получить скидку из промокода',
            'url' => '/api/get_discount',
            'response' => [
                'base_price' => 'Изначальная цена',
                'price' => 'Текущая цена',
                'discount' => 'Скидка на всю корзину',
            ],
            'parameters' => [
                'coupon' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Номер промокода',
                ],
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                ],
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'get_orders' => [
            'controller' => '\Controllers\Order@getOrders',
            'url' => '/api/get_orders',
            'description' => 'Вывод всех заказов пользователя',
            'response' => [
                'order_id' => 'id заказа',
                'payment_type' => 'Типо оплаты',
                'date' => 'Дата заказа',
                'price' => 'Общая цена заказа, учитывая доставку',
                'status' => 'Статус заказа. CH - отменён, N - новый, Q - ожидает курьера, S - собран, P - собирается, F - выполнен, R - ожидает вызова курьера',
                'address' => 'Адрес доставки',
                'products' => [
                    'product_id' => 'id товара',
                    'preview_picture' => 'Превью товара',
                    'detail_picture' => 'Картинка на странице товара',
                    'name' => 'Название товара',
                    'quantity' => 'Количество товара',
                    'price' => 'Цена товара'
                ]
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'order_detail' => [
            'controller' => '\Controllers\Order@getDetail',
            'url' => '/api/order_detail',
            'description' => 'Детали заказа',
            'parameters' => [
                'order_id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'id заказа',
                ],
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'repeat_order' => [
            'controller' => '\Controllers\Order@repeatOrder',
            'url' => '/api/repeat_order',
            'description' => 'Повтор заказа',
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'parameters' => [
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Адрес доставки',
                ],
                'order_id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'Номер заказа',
                ],
            ]
        ],
        'refund_order' => [
            'controller' => '\Controllers\Order@refundOrder',
            'url' => '/api/refund_order',
            'description' => 'Возврат товара',
            'parameters' => [
                'order_id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'Номер заказа',
                ],
                'type' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Причина возврата',
                ],
                'comment' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Комментарии к возврату',
                ],
                'files' => [
                    'required' => false,
                    'type' => 'array',
                    'description' => 'Товары',
                    'parameters' => [
                        [
                            'file' => [
                                'required' => false,
                                'type' => 'integer',
                                'description' => 'id изображения',
                            ]
                        ]
                    ]
                ],
                'products' => [
                    'required' => true,
                    'type' => 'array',
                    'description' => 'Товары',
                    'parameters' => [
                        [
                            'product_id' => [
                                'required' => true,
                                'type' => 'integer',
                                'description' => 'id товара',
                            ],
                            'quantity' => [
                                'required' => false,
                                'type' => 'integer',
                                'description' => 'Если единица измерения шт, то нужно высылать кол-во',
                            ],
                        ]
                    ]
                ],
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
        ],
        'add_file' => [
            'controller' => '\Controllers\Order@addFile',
            'url' => '/api/add_file',
            'description' => 'Добавление файла',
            'response' => [
                'id' => 'id файла'
            ],
            'parameters' => [
                'file' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Файл в Data URI - data:content/type;base64',
                ]
            ]
        ],
        'get_payment_info' => [
            'controller' => '\Controllers\Order@getPaymentInfo',
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'parameters' => [
                'order_id' => [
                    'required' => true,
                    'type' => 'integer',
                ],
            ]
        ],
        'create_payment' => [
            'controller' => '\Controllers\Order@createPayment',
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'parameters' => [
                'token' => [
                    'required' => true,
                    'type' => 'string',
                ],
                'order_id' => [
                    'required' => true,
                    'type' => 'integer',
                ],
            ]
        ],
        'new_order' => [
            'controller' => '\Controllers\Order@newOrder',
            'url' => '/api/new_order',
            'description' => 'Добавление нового заказа',
            'response' => [
                'id' => 'id нового заказа'
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'parameters' => [
                'fio' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Ф.И.О',
                ],
                'payment_type' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => '1 - оплата картой при получении, 0 - оплата картой онлайн',
                ],
                'email' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Электронная почта',
                ],
                'phone' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Номер телефона',
                ],
                'address' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Адрес доставки',
                ],
                'number_apartment' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Номер квартиры/офис',
                ],
                'floor' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Этаж',
                ],
                'entrance' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Подъезд',
                ],
                'time_delivery' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Время доставки',
                ],
                'date_delivery' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Дата доставки',
                ],
                'replacement_product' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Замена товара. Позвоните мне - PZ_1; Замените на аналог, без звонка - PZ_2; Уберите из заказа, без звонка - PZ_3',
                ],
                'lift' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Наличие лифта 1 - есть, 0 - нету',
                ],
                'intercom_code' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Код домофона',
                ],
                'comment_delivery' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Комментарии по доставке',
                ],
                'comment_order' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Комментарии к заказу',
                ],
                'coupon' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Номер промокода',
                ],
                'bonus' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => '1, если надо списать баллы',
                ],
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Адрес доставки',
                ],
                'version' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Версия приложения',
                ],
            ]
        ],
        'add_delivery' => [
            'controller' => '\Controllers\Order@addDelivery',
            'url' => '/api/add_delivery',
            'description' => 'Добавление адреса доставки',
            'response' => [
                'status' => 'success | error'
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'parameters' => [
                'address' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Полный адрес'
                ],
                'lift' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Есть ли лифт (1 и 0)'
                ],
                'number_apartment' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Номер квартиры'
                ],
                'floor' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Этаж'
                ],
                'intercom_code' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Код домофона'
                ],
                'postal_code' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Почтовый индекс'
                ],
                'city' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Город'
                ],
                'street' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Улица'
                ],
                'house' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Дом'
                ]
            ]
        ],
        'comment_vote' => [
            'controller' => '\Controllers\Products@commentVote',
            'url' => '/api/comment_vote',
            'description' => 'Добавление оценки к комментарию',
            'response' => [
                'status' => 'success | error'
            ],
            'parameters' => [
                'comment_id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'id комментария'
                ],
                'vote' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => '1 - лайк, 0 - дизлайк'
                ],
            ]
        ],
        'add_comment' => [
            'controller' => '\Controllers\Products@addComment',
            'url' => '/api/add_comment',
            'description' => 'Добавление комментария к товару',
            'response' => [
                'status' => 'success | error',
                "message" => 'Сообщение об ошибке',
                "id" => "id комментария"
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'parameters' => [
                'product_id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'id товара'
                ],
                'name' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Имя автора'
                ],
                'email' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Почта автора'
                ],
                'raiting' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'Оценка автора'
                ],
                'virtues' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Достоинства товара'
                ],
                'limitations' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Недостатки товара'
                ],
                'comment' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Комментарии к товару'
                ],
                'parent_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Родительский id (используется если пользователь отвечает на отзыв)'
                ],
                'files' => [
                    'required' => false,
                    'type' => 'array',
                    'description' => 'Приложенные файлы',
                    'parameters' => [
                        [
                            'file' => [
                                'required' => false,
                                'type' => 'integer',
                                'description' => 'id изображения',
                            ]
                        ]
                    ]
                ]
            ],
        ],
        'delete_comment' => [
            'controller' => '\Controllers\Products@deleteComment',
            'url' => '/api/delete_comment',
            'description' => 'Удаления комментария',
            'response' => [
                'status' => 'success | error',
                "message" => 'Сообщение об ошибке',
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'parameters' => [
                'id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'id комментария'
                ],
            ],
        ],
        'update_info' => [
            'controller' => '\Controllers\User@updateInfo',
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'url' => '/api/update_info',
            'description' => 'Обновить личные данные',
            'parameters' => [
                'surname' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Фамилия'
                ],
                'name' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Имя'
                ],
                'birthday' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'День рождения'
                ],
                'email' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Почта'
                ],
            ],
        ],
        'user_info' => [
            'controller' => '\Controllers\User@userInfo',
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'url' => '/api/user_info',
            'description' => 'Возвращает информацию о пользователе (включая карту лояльности)',
            'response' => [
                'id' => 'id пользователя',
                'login' => 'Логин пользователя',
                'email' => 'Почта пользователя',
                'date_register' => 'Дата регистрации пользователя',
                'name' => 'Имя пользователя',
                'second_name' => 'Отчество пользователя',
                'birthday' => 'День рождения пользователя',
                'last_name' => 'Фамилия пользователя',
                'personal_phone' => 'Телефон пользователя',
                'qr-code' => 'qr-code для карты лояльности',
                'card_number' => 'Номер карты лояльности',
                'card_code' => 'Пин код карты лояльности',
                'bonus_sum' => 'Накоплено бонусов',
                'accrual_volume' => 'Объём начисляемых баллов (колличество баллов на 10 рублей)',
                'purchase_amount' => 'Сумма покупок',
            ],
        ],
        'new_card' => [
            'controller' => '\Controllers\User@newCard',
            'parameters' => [
                'name' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Имя'
                ],
                'last_name' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Фамилия'
                ],
                'second_name' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Отчество'
                ],
                'birthday' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'День рождения'
                ],
                'phone' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Телефон'
                ],
                'email' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Электронная почта'
                ]
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'url' => '/api/new_card',
            'description' => 'Выпуск новой виртуальной карты',
            'response' => [
                'status' => 'success | error',
                'message' => 'Сообщение, если есть ошибка'
            ],
        ],
        'register_card' => [
            'controller' => '\Controllers\User@registerCard',
            'parameters' => [
                'card_number' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'Номер карты'
                ],
                'card_code' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'Пин код'
                ],
                'name' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Имя'
                ],
                'last_name' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Фамилия'
                ],
                'second_name' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Отчество'
                ],
                'birthday' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'День рождения'
                ],
                'phone' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Телефон'
                ],
                'email' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Электронная почта'
                ]
            ],
            'security' => [
                'auth' => [
                    'required' => true,
                    'type' => 'token',
                ],
            ],
            'url' => '/api/register_card',
            'description' => 'Активация пластиковой карты из супермаркета',
            'response' => [
                'status' => 'success | error',
                'message' => 'Сообщение, если есть ошибка'
            ],
        ],
        'code' => [
            'controller' => '\Controllers\User@checkCode',
            'parameters' => [
                'phone' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Номер телефона'
                ],
                'code' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'Код с телефона'
                ]
            ],
            'url' => '/api/code',
            'description' => 'Проверка отправленного кода. Обязательно передавать таким же форматом, как на сайте +9 (999) 999-99-99',
            'response' => [
                'token' => [
                    'UF_REST_API_TOKEN' => 'Клиентский токен (его надо передавать в пользовательских методах). Токен нужно передавать в заголовке Authorization-Token в следующем формате: auth-mobile:токен',
                    'UF_API_TOKEN_EXPIRE' => 'Срок действия токена. По умолчанию у токена нету срока действия.'
                ]
            ],
        ],
        'sms' => [
            'controller' => '\Controllers\User@sendSms',
            'parameters' => [
                'phone' => [
                    'required' => true,
                    'type' => 'string',
                    'description' => 'Номер телефона'
                ],
            ],
            'url' => '/api/sms',
            'description' => 'Отправка смс существующему пользователю. Если пользователя нету в бд, то создаёт нового пользователя. Обязательно передавать таким же форматом, как на сайте +9 (999) 999-99-99',
            'response' => [
                'status' => 'success | error'
            ],
        ],
        'stores' => [
            'controller' => $_SERVER['DOCUMENT_ROOT'] . '/local/api/stores.php',
            'parameters' => [
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'ID магазина'
                ],
            ],
            'url' => '/api/stores',
            'description' => 'Вывод магазинов',
            'response' => [
                'ID' => 'id магазина',
                "TITLE" => 'Название магазина',
                "ADDRESS" => 'Адрес магазина',
                "DESCRIPTION" => 'Описание магазина',
                "GPS_N" => 'Долгота (для вывода на карту)',
                "GPS_S" => 'Широта (для вывода на карту)',
                "IMAGE_ID" => 'Изображение магазина',
                "PHONE" => 'Номер магазина',
                "SCHEDULE" => 'Расписание магазина',
            ],
        ],
        'category' => [
            'controller' => '\Controllers\Categories@one',
            'parameters' => [
                'category_id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'ID элемента'
                ],
            ],
            'url' => '/api/category',
            'description' => 'Вывод категории по id',
            'response' => [
                'ID' => 'id категории',
                'NAME' => 'Название категории',
                'CODE' => 'Символьный код категории',
                'DETAIL_PICTURE' => 'Картинка внутри категории',
                'PICTURE' => 'Превью категории',
                'SORT' => 'Сортировка по умолчанию',
                'childs' => 'Дочерние категории'
            ],
        ],
        'products' => [
            'controller' => '\Controllers\Products@list',
            'url' => '/api/products',
            'description' => 'Вывод списка товаров',
            'response' => [
                'ID' => 'id товара',
                'CATEGORY_ID' => 'id категории',
                'NAME' => 'Название товара',
                'DETAIL_PICTURE' => 'Картинка внутри товара',
                'PREVIEW_PICTURE' => 'Превью товра',
                'PRODUCT_PRICE' => 'Цена',
                'SHOW_COUNTER' => 'Количество просмотров',
                'MEASURE' => 'Единица измерения',
                'QUANTITY' => 'Количество товара',
            ],
            'parameters' => [
                'filter' => [
                    'required' => false,
                    'type' => 'array',
                    'description' => 'Фильтрация',
                    'parameters' => [
                        'category_id' => [
                            'required' => false,
                            'type' => 'integer',
                            'description' => 'ID категории'
                        ],
                        'price_min' => [
                            'required' => false,
                            'type' => 'integer',
                            'description' => 'Минимальная сумма'
                        ],
                        'price_max' => [
                            'required' => false,
                            'type' => 'integer',
                            'description' => 'Максимальная сумма сумма'
                        ],
                        'sales' => [
                            'required' => false,
                            'type' => 'integer',
                            'description' => 'Значение 1, если нужно выбрать товары по скидке'
                        ],
                        'properties' => [
                            'required' => false,
                            'type' => 'array',
                            'parameters' => [
                                [
                                    'property_name' => [
                                        'required' => false,
                                        'type' => 'string'
                                    ],
                                    'property_value' => [
                                        'required' => false,
                                        'type' => 'array',
                                        'parameters' => [
                                            [
                                                'name' => [
                                                    'required' => false,
                                                    'type' => 'string'
                                                ],
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'query' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Поиск по строке'
                ],
                'store_id' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Остатки по адресу'
                ],
                'page' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Номер страницы'
                ],
                'limit' => [
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'Кол-во товаров на вывод'
                ],
                'sort' => [
                    'required' => false,
                    'type' => 'string',
                    'description' => 'Сортировка (price_desc | price_asc | popularity_desc | name_desc | name_asc | stock_asc)'
                ]
            ]
        ],
        'product' => [
            'controller' => '\Controllers\Products@one',
            'url' => '/api/product',
            'description' => 'Вывод товара по id',
            'response' => [
                'ID' => 'id товара',
                'CATEGORY_ID' => 'id категории',
                'NAME' => 'Название товара',
                'DETAIL_PICTURE' => 'Картинка внутри товара',
                'PREVIEW_PICTURE' => 'Превью товра',
                'PRODUCT_PRICE' => 'Цена',
                'SHOW_COUNTER' => 'Количество просмотров',
                'DETAIL_TEXT' => 'Описание товара',
                'stores' => 'Наличие в магазинах',
                'sales' => 'В каких акциях участвует',
                'properties' => 'Свойства товара',
                'MEASURE' => 'Единица измерения',
                'QUANTITY' => 'Количество товара',
                'comments' => [ // 'Отзывы о товаре'
                    'AUTHOR_NAME' => 'Имя автора',
                    'AUTHOR_EMAIL' => 'Почта автора',
                    'POST_TEXT' => 'virtues - достоинства, limitations - недостатки, comment - комментарий к заказу',
                    'UF_ASPRO_COM_RATING' => 'Оценка',
                    'UF_ASPRO_COM_LIKE' => 'Сколько раз лайкнули отзыв',
                    'UF_ASPRO_COM_DISLIKE' => 'Сколько раз дизлайкнули отзыв',
                    'UF_ASPRO_COM_APPROVE' => 'Реальный покупатель (boolean)',
                    'ID' => 'id отзыва',
                    "PARENT_ID" => 'id Родительского отзыва'
                ]
            ],
            'parameters' => [
                'product_id' => [
                    'required' => true,
                    'type' => 'integer',
                    'description' => 'ID товара'
                ],
            ]
        ]
    ],
];
