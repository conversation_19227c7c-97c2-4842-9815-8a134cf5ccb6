class PanelService {
    async getBasketInfo() {
        const response = await fetch('/CartApi/info');

        if (response.status !== 200) throw new Error('Ошибка при получении данных')

        return response.json();
    }

    async getFavoriteCount() {
        const response = await fetch('/FavoriteApi/count');

        if (response.status !== 200) throw new Error('Ошибка при получении данных')

        return response.json();
    }
}

export default new PanelService();