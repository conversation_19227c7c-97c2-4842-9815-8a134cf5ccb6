<script>
import DeliveryMapAddress from "./DeliveryMapAddress.vue";
export default {
    name: "DeliveryMapMobile",
    props: {
        address: {
            type: String,
            required: true
        },
        loading: {
            type: Boolean,
            required: false
        }
    },
    components: {
        DeliveryMapAddress
    },
}
</script>

<template>
  <div class="DMLabMobileDelivery">
    <div
        class="DMLabMobileDelivery__block active"
        data-toggle="modal"
        data-target="#callback5"
    >
                <span class="DMLabMobileDelivery__block--text">
                        <svg width="12" height="17" viewBox="0 0 7 9" fill="white">
                                <path
                                    d="M763.433,464.4l0,0.007L761,468h-1l-2.469-3.626,0.011-.018A3.5,3.5,0,1,1,763.433,464.4ZM760.5,461a1.5,1.5,0,0,0-1.5,1.5,1.461,1.461,0,0,0,.184.681l-0.028.006,1.313,2.594,1.406-2.625-0.037-.006A1.47,1.47,0,0,0,762,462.5,1.5,1.5,0,0,0,760.5,461Z"
                                    transform="translate(-757 -459)"></path>
                        </svg>
                        <span class="DMLabMobileDelivery__block--address">{{ address }}</span>
                </span>
      <svg class="test" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
           color="currentColor">
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M8.22 5.24a1 1 0 00.02 1.41l5.4 5.29-5.4 5.28a1 1 0 101.4 1.43l6.12-6a1 1 0 000-1.43l-6.12-6a1 1 0 00-1.42.02z"
              fill="currentColor"></path>
      </svg>
    </div>
  </div>
</template>