class BasketService {

    async add(productId, quantity) {

        const data = new FormData();
        data.append('id', productId);
        data.append('quantity', quantity);

        const response = await fetch('/CartApi/add', {
            method:'post',
            body: data
        });

        document.dispatchEvent(new CustomEvent("DmlabBasketChange"));

        if (response.status !== 200) throw new Error('Ошибка при получении данных')

        return response.json();
    }
    async delete(productId) {

        const data = new FormData();
        data.append('id', productId);


        const response = await fetch('/CartApi/remove', {
            method:'post',
            body: data
        });

        document.dispatchEvent(new CustomEvent("DmlabBasketChange"));

        if (response.status !== 200) throw new Error('Ошибка при получении данных')

        return response.json();
    }


}


export default new BasketService();