<script>
/**
 * теперь этот файл/поток будет кодироваться в UTF-8
 */

import BasketService from "../BasketService";

export default {
    name: "FavoriteAddToCartButton",
    props: {
        product: {
            type: Object,
            required: true
        }
    },
    data () {
        return {
            inBasket: this.product.IN_BASKET ?? false,
            quantity: this.product.MIN
        }
    },
    methods: {
        async addToBasket(){
            if (this.inBasket) {
                BasketService.delete(this.product.ID)
            } else {
                BasketService.add(this.product.ID, this.quantity)
            }

            this.inBasket = !this.inBasket;
        }
    },
}
</script>

<template>
  <button class="dm-add2Cart" @click="addToBasket(product.MIN)">
    <span v-if="!inBasket">В корзину</span>
    <span v-else>В корзине</span>
  </button>
</template>
