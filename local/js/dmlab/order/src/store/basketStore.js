import {defineStore} from "pinia";
import BasketService from "../BasketService";
import CouponService from "../CouponService";

export const useBasketStore = defineStore('basketStore', {
    state: () => ({
        products: [],
        info: {},
        loading: false,
        crossSales: [],
        coupon: '',
        couponActive: false,
        couponError: '',
        discountType: '',
        delivery: {},
        isBonusesToWriteOff: false,
        bonusesError: false,
        bonusesLoader: false
    }),
    getters: {
        sortedProducts(state) {
            return [...state.products].sort((a, b) => {
                return a.isPack === b.isPack ? 0 : (a.isPack ? 1 : -1);
            });
        },
        isLoading(state) {
            return state.loading;
        },
        basketInfo(state) {
            return state.info;
        },
        deliveryInfo(state) {
            return state.delivery
        },
        crossSalesProducts(state) {
            return state.crossSales;
        },
        getDiscountType(state) {
            return state.discountType;
        }
    },
    actions: {
        setLoading(value) {
            this.loading = value;
        },
        setCoupon(value) {
            this.coupon = value;
        },
        setDiscountType(value) {
            this.discountType = value;
        },
        setBonusesError(value) {
            this.bonusesError = value;
        },
        setBonusesLoader(value) {
            this.bonusesLoader = value;
        },

        async addCoupon() {
            if (this.coupon) {
                this.couponError = '';
                const response = await CouponService.add(this.coupon);
                if (response.status === 'success') {
                    this.coupon = response.data;
                    this.couponActive = true;
                }
                if (response.status === 'error') {
                    this.couponError = response.errors[0].message;
                    this.couponActive = false;
                    console.error('Ошибка: данные не были успешно получены');
                }
            }
        },

        async isCouponActive() {
            const response = await CouponService.isActive();
            if (response.status === 'success') {
                if (response.data === false) {
                    this.couponActive = false;
                    this.coupon = '';
                } else {
                    this.coupon = response.data;
                    this.setDiscountType('coupon');
                    this.couponActive = true;
                }
                this.couponError = '';
            } else {
                console.error('Ошибка: данные не были успешно получены');
            }
        },

        async deleteCoupon() {
            this.couponError = '';
            const response = await CouponService.delete();
            if (response.status === 'success') {
                this.coupon = '';
                this.couponActive = false;
            } else {
                console.error('Ошибка: данные не были успешно получены');
            }
        },

        async getBasketItems() {
            const response = await BasketService.getBasketItems();
            if (response.status === 'success') {
                this.products = response.data.products;
            } else {
                console.error('Ошибка: данные не были успешно получены');
            }

        },

        async getBasketInfo() {
            const response = await BasketService.getBasketInfo();
            if (response.status === 'success') {
                this.info = response.data;
            } else {
                console.error('Ошибка: данные не были успешно получены');
            }

        },

        async getCrossSalesProducts() {
            const response = await BasketService.getCrossSalesProducts();
            if (response.status === 'success') {
                this.crossSales = response.data;
            } else {
                console.error('Ошибка: данные не были успешно получены');
            }

        },

        async getDeliveryInfo() {
            const response = await BasketService.getDeliveryInfo();
            if (response.status === 'success') {
                this.delivery = response.data;
            } else {
                console.error('Ошибка: данные не были успешно получены');
            }
        },
    },
});
