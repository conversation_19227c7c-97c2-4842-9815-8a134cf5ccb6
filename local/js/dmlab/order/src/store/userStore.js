import {defineStore} from "pinia";
import UserService from "../UserService";

export const useUserStore = defineStore('userStore', {
    state: () => ({
        authorized: false,
    }),
    actions: {
        async getAuthorized() {
            try {
                const response = await UserService.getAuthorized();
                if (response.status === 'success') {
                    this.authorized = response.data.authorized;
                } else {
                    console.error('Ошибка при получении авторизации');
                }
            } catch (error) {
                console.error('Ошибка при проверке авторизации:', error);
            }
        },
    },
    getters: {
        isAuthorized(state) {
            return state.authorized;
        }
    }
});
