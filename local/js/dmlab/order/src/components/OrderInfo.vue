<script>
/**
 * теперь этот файл/поток будет кодироваться в UTF-8
 */

import './orderInfo.scss';
import LoyaltyService from "./../LoyaltyService";
import {useUserStore} from "../store/userStore";
import {useOrderStore} from "../store/orderStore";
import OrderService from "../OrderService";
import {useBasketStore} from "../store/basketStore";
import FreeDelivery from "./FreeDelivery.vue"
import Bonuses from "./Bonuses.vue";

export default {
  name: "OrderInfo",
  components: {
    FreeDelivery,
    Bonuses
  },
  props: {
    auth: {
      type: Boolean,
      required: true,
    },
    basketInfo: {
      type: Object,
      required: true,
    }
  },
  data() {
    return {
      userStore: useUserStore(),
      orderStore: useOrderStore(),
      basketStore: useBasketStore(),
      errorCreateOrder: '',
      isShowFreeDelivery: window.innerWidth >= 1200,
    }
  },
  methods: {
    validateTimeDelivery() {
      if (this.orderStore.day === 'today') {
        this.orderStore.setDateDelivery(new Date().toLocaleDateString('ru-RU'));
      }

      if (this.orderStore.day === 'tomorrow') {
        this.orderStore.setDateDelivery(new Date(Date.now() + 86400000).toLocaleDateString('ru-RU'));
      }

      if (!this.orderStore.intervals[this.orderStore.day].includes(this.orderStore.getCurrentTimeDelivery)) {
        this.orderStore.setTimeDelivery(null);
        this.orderStore.setDefaultDeliveryTime();
      }
    },
    checkValidate() {
      let valid = true;

      const fieldErrorsRefs = this.orderStore.fieldErrors.refs;
      for (const fieldName in fieldErrorsRefs) {
        const ref = fieldErrorsRefs[fieldName];
        if (ref && typeof ref.manualValidate === "function") {
          ref.manualValidate();

        }
      }

      const fieldErrorsHasError = this.orderStore.fieldErrors.hasError;
      for (const fieldName in fieldErrorsHasError) {
        const ref = fieldErrorsHasError[fieldName];
        if (ref) {
          fieldErrorsRefs[fieldName].scrollToElement();
          valid = false;
          break;
        }
      }

      return valid;
    },
    async createOrder() {
      const order = this.orderStore.orderData;

      if (!this.checkValidate()) {
        return;
      }

      this.basketStore.setLoading(true);
      try {
        this.errorCreateOrder = '';
        const response = await OrderService.create(order);
        if (response.status === 'success') {
          if (response.data.createdOrder && this.orderStore.orderData.payment_type === 1) {
            this.orderStore.setCreatedOrderId(parseInt(response.data.createdOrder));
            this.orderStore.setOrderCreated(true);
          }
          if (response.data.createdOrder && response.data.link) {
            window.location.replace(response.data.link)
          }
        }

        if (response.status === 'error') {
          this.errorCreateOrder = response.errors[0].message;
          await this.orderStore.getIntervals();
          this.validateTimeDelivery();
        }
      } catch (e) {
        console.error(e)
      } finally {
        this.basketStore.setLoading(false);
      }
    },
    async addCoupon() {
      if (this.basketStore.coupon) {
        this.basketStore.setDiscountType('coupon');
        await this.basketStore.addCoupon();
        await this.updateBasket();
      }
    },
    async deleteCoupon() {
      await this.basketStore.deleteCoupon();
      await this.updateBasket();
      this.basketStore.setDiscountType('');
    },

    async updateBasket() {
      await Promise.all([this.basketStore.getBasketItems(),this.basketStore.getBasketInfo()]);
    },

    updateWidth() {
      this.isShowFreeDelivery = window.innerWidth >= 1200;
    }
  },
  computed: {
    discountType: {
      get() {
        return this.basketStore.getDiscountType;
      }
    },
    coupon: {
      get() {
        return this.basketStore.coupon;
      },
      set(value) {
        this.basketStore.setCoupon(value);
      }
    },
    couponError: {
      get() {
        return this.basketStore.couponError;
      },
    },
    couponActive: {
      get() {
        return this.basketStore.couponActive;
      },
    },
    showWeightText() {
      return this.basketStore.sortedProducts.some((product) => {
        return product.measureShortName === 'кг' || product.measureShortName === 'гр';
      });
    },
    createOrderDisabled() {
      const productsAvailable = this.basketStore.sortedProducts.filter((product) => {
        return product.canBuy === true;
      });

      if (productsAvailable.length === 1) {
        if (productsAvailable[0].isPack === true) {
          return true;
        }
      }

      return false;
    },
    totalPrice() {
      if (this.basketStore.isBonusesToWriteOff) {
        return this.basketStore.info.totalPriceUsingBonuses;
      }
      return this.basketStore.info.totalPrice;
    }
  },
  async mounted() {
    if (this.userStore.isAuthorized) {
      await this.basketStore.isCouponActive();
    }
    window.addEventListener("resize", this.updateWidth);
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.updateWidth);
  },
}
</script>

<template>
  <div class="OrderInfo" v-if="Number.isFinite(basketStore.info.productsPrice)">
    <FreeDelivery v-if="Number.isFinite(basketInfo.productsPrice) && isShowFreeDelivery"/>

    <h2 class="OrderInfo__title">Ваш заказ</h2>

    <div class="OrderInfo__body infoOrder">
      <ul class="infoOrder__list">
        <li class="infoOrder__item">
          <span class="infoOrder__key">{{ basketInfo.count }} товаров</span>
          <span class="infoOrder__value">{{ basketStore.info.productsPrice }} ₽</span>
        </li>
        <li class="infoOrder__item">
          <span class="infoOrder__key">Общий вес</span>
          <span class="infoOrder__value">{{ basketInfo.weight }} кг</span>
        </li>
        <!--                    TODO рефакторинг условия-->
        <li class="infoOrder__item"
            v-if="basketStore.info.productsPrice < basketStore.deliveryInfo.freeFrom || (basketStore.info.priceUsingBonuses < basketStore.deliveryInfo.freeFrom) && basketStore.isBonusesToWriteOff">
          <span class="infoOrder__key">Доставка</span>
          <span class="infoOrder__value"> {{ basketStore.deliveryInfo.price }} ₽</span>
        </li>
      </ul>
    </div>

    <div class="OrderInfo__promocodes"
         v-if="discountType === 'coupon' || discountType === '' && userStore.isAuthorized">

      <div class="OrderInfo__promocodes__wrapper">
        <input
            type="text"
            v-model="coupon"
            placeholder="Введите промокод"
            class="OrderInfo__promocodes--value"
            :class="{
                        'OrderInfo__promocodes--value--error': couponError,
                        'OrderInfo__promocodes--value--active': couponActive
                    }"

        >
        <button v-if="!couponActive && couponError === ''" @click="addCoupon" class="OrderInfo__promocodes--submit">
          применить
        </button>
        <button v-else
                @click="deleteCoupon"
                class="OrderInfo__promocodes--submit"
                :class="{
                       'OrderInfo__promocodes--submit--active': couponActive
                    }"
        >сбросить
        </button>
      </div>


      <div class="OrderInfo__promocodes__error" v-if="couponError">{{ couponError }}</div>
    </div>

    <Bonuses/>

    <div class="OrderInfo__total totalOrder">
      <div class="totalOrder__key">Итого</div>
      <div class="totalOrder__value">
        {{ totalPrice }} ₽
      </div>
    </div>

    <button v-if="auth" @click="createOrder"
            :disabled="createOrderDisabled || basketInfo.productsPrice < basketStore.deliveryInfo.minimal"
            class="OrderInfo__toOrder btn btn-default btn-lg has-ripple">оформить заказ
    </button>
    <button v-else
            class="OrderInfo__toOrder btn btn-default btn-lg has-ripple"
            data-event="jqm"
            data-param-backurl="/order/"
            data-param-type="auth"
            data-name="auth"
            :disabled="createOrderDisabled"
            href="/basketNew/">
      Оформить заказ
    </button>
    <div class="OrderInfo__weightText" v-if="showWeightText">
      В заказе есть весовые товары - после сборки сумма может измениться
    </div>

    <div class="OrderInfo__error" v-if="errorCreateOrder">
      {{ errorCreateOrder }}
    </div>
  </div>
</template>