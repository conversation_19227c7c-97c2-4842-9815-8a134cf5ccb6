<script>
import {useOrderStore} from "../store/orderStore";
import './orderPayments.scss';

export default {
  name: "OrderPayments",
  data() {
    return {
      orderStore: useOrderStore()
    };
  },
  computed: {
    paymentType: {
      get() {
        return this.orderStore.orderData.payment_type;
      },
      set(value) {
        this.orderStore.setPaymentType(value);
      }
    }
  },
};
</script>

<template>
  <div class="Order__payments payments">
    <div class="OrderProducts__header">
      <h2 class="OrderProducts__title">
        <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 9.88889H29M1 13H29M4.88889 17.6667H15.7778M4.88889 20.7778H11.1111M4.11111 6H25.8889C27.6072 6 29 7.39289 29 9.11111V21.5556C29 23.2738 27.6072 24.6667 25.8889 24.6667H4.11111C2.39289 24.6667 1 23.2738 1 21.5556V9.11111C1 7.39289 2.39289 6 4.11111 6ZM25.1111 20C25.1111 20.4295 24.7628 20.7778 24.3333 20.7778C23.9038 20.7778 23.5556 20.4295 23.5556 20C23.5556 19.5705 23.9038 19.2222 24.3333 19.2222C24.7628 19.2222 25.1111 19.5705 25.1111 20Z" stroke="#539348" stroke-width="2" stroke-linecap="round"/>
        </svg>
        Способ оплаты
      </h2>
    </div>

    <div class="payments__body">
      <div class="radio__wrapper">
        <input type="radio" value="1" name="payment_type" v-model="paymentType" id="paymentTypeCash" :checked="paymentType === 1">
        <label for="paymentTypeCash">Картой при получении</label>
      </div>
      <div class="radio__wrapper">
        <input type="radio" value="0" name="payment_type" v-model="paymentType" id="paymentTypeCard" :checked="paymentType === 0">
        <label for="paymentTypeCard">Картой онлайн</label>
      </div>


    </div>
  </div>
</template>