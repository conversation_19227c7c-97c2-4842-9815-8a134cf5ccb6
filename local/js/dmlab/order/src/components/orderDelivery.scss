.delivery {

  &__title {
    color: #333333;
    font-family: Montser<PERSON>;
    font-size: 16px;
    font-weight: 600;
    line-height: 19.5px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    margin: 0px;
  }

  &__data {
    display: flex;
    gap: 20px;
    align-items: baseline;
  }

  &__address {
    display: flex;
    flex-direction: column;
    gap: 8px;

    &--value {
      color: #333333;
      font-family: Montserrat;
      font-size: 13px;
      font-weight: 400;
      line-height: 15.85px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;

    }

    &--change {
      color: #539348;
      font-family: Montserrat;
      font-size: 12px;
      font-weight: 400;
      line-height: 14.63px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      text-transform: uppercase;
      background: transparent;
      border: none;
    }
  }

  &__inputs {
    &--item {

      & label {
        font-family: Montserrat;
        font-size: 13px;
        font-weight: 400;
        line-height: 22px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #444444;

      }
    }

    &--grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      margin-top: 20px;
    }
  }

  &__body {
    border: 1px solid #ECECEC;
    padding: 32px;
    margin-top: 36px;

    & label[for="commentOrder"] {
      color: #333333;
      font-family: Montserrat;
      font-size: 13px;
      font-weight: 400;
      line-height: 22px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }

    & label[for="lift"] {
      cursor: pointer;
    }

    & input[type="text"] {
      width: 100%;
      color: #44444480;
      font-family: Open Sans;
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      padding: 9px 14px;
      border: 1px solid #ECECEC;
      background: #FAFAFA;
    }
  }

  &__lift {
    margin: 20px 0px;

    &--input {
      margin: 0;
      position: absolute;
      z-index: -1;
      opacity: 0;

      & + label {
        padding-left: 59px;
        position: relative;
        margin: 0;
        color: #333333;
        padding-top: 2px;
        font-family: Montserrat;
        font-size: 13px;
        font-weight: 400;
        line-height: 22px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;

        &::after {
          content: '';
          position: absolute;
          background: #DDDDDD;
          top: 2px;
          left: 2px;
          width: 48px;
          height: 26px;
          border-radius: 20px;
        }


        &::before {
          content: '';
          position: absolute;
          background: #FFFFFF;
          width: 22px;
          height: 22px;
          top: 4px;
          left: 4px;
          border-radius: 20px;
          z-index: 1;
        }

        &::after {

        }
      }

      &:checked + label {


        &::after {
          background: #539348;
        }


        &::before {
          left: 26px;
        }
      }

    }

  }


}

@media (max-width: 767px) {
  .delivery__inputs--grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 575px) {

  .delivery__inputs--grid {
    grid-template-columns: repeat(1, 1fr);
  }

}