<script>

import { useOrderStore } from "../../store/orderStore";
import "./checkboxField.scss";

export default {
  name: "checkboxField",
  props: {
    modelValue: { type: Boolean, required: true },
    label: { type: String, required: true },
    name: { type: String, required: true },
    required: { type: Boolean, default: false },
  },
  data() {
    return {
      orderStore: useOrderStore(),
      hasError: false,
      errorMessage: "",
    };
  },
  computed: {
    inputValue: {
      get() {
        return this.orderStore.order[this.name];
      },
      set(newValue){
        this.$emit('update:modelValue', newValue);
        this.orderStore.setFieldData(this.name, newValue);
        this.validate();
        this.setErrorsInOrderStore();
      }
    }
  },
  methods: {
    manualValidate() {
      this.validate();
      this.setErrorsInOrderStore()
    },
    validate() {
      this.resetValidation();

      if (this.required && !this.inputValue) {
        this.setError("Обязательно для заполнения");
        return;
      }
    },
    resetValidation() {
      this.hasError = false;
      this.errorMessage = "";
    },
    setError(message) {
      this.hasError = true;
      this.errorMessage = message;
    },
    setErrorsInOrderStore(){
      this.orderStore.fieldErrors.hasError[this.name] = this.hasError;
    },
    onChange(event) {
      this.inputValue = event.target.checked;
      this.validate();
      this.setErrorsInOrderStore();
    },
    scrollToElement(){
      this.$refs[this.name].scrollIntoView({ behavior: 'smooth', block: "center"});
    }
  },
  mounted() {
    this.orderStore.fieldErrors.refs[this.name] = this;
    this.orderStore.fieldErrors.hasError[this.name] = this.hasError;
  },
};
</script>

<template>
  <input
      class="checkboxField--input"
      type="checkbox"
      v-model="inputValue"
      @change="onChange"
      :id="name"
      :class="{ 'is-invalid': hasError }"
      :ref="name"
  >
  <label :for="name" v-html="label"></label>
  <div v-if="hasError" class="error-message">{{ errorMessage }}</div>
</template>