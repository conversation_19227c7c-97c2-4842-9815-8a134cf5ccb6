<script>
import "./Skeleton.scss";

export default {
  name: "Skeleton",
  props: {
    type: {
      type: String,
      default: "rectangle",
    },
    bgClass: {
      type: String,
      default: "bg-gray-300",
    },
    cssClass: {
      type: String,
      default: "",
    },
    shimmerColor: {
      type: String,
      default: "#ffffff",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "20px",
    },
  },

  data() {
    return {
      containerStyle: {
        width: this.width,
        height: this.height,
      },
      shimmerStyle: {},
      loaderTypes: { rectangle: "rectangle", circle: "circle" },
      loaderCssClasses: {
        rectangle: "skeleton-rounded",
        circle: "skeleton-rounded-full",
      },
    };
  },

  computed: {
    loaderClass() {
      return this.cssClass ? this.cssClass : this.loaderCssClasses[this.type];
    },
  },

  methods: {
    hexToRgb(hex) {
      const rgb = hex.match(/\w\w/g)?.map((x) => +`0x${x}`);
      return rgb ? rgb.join(",") : "255, 255, 255";
    },
    isHexColor(color) {
      const hex = color.replace("#", "");
      return (
          typeof color === "string" &&
          color.startsWith("#") &&
          hex.length === 6 &&
          !isNaN(Number("0x" + hex))
      );
    },
    isValidLoaderType(value) {
      return Object.values(this.loaderTypes).includes(value);
    },
  },

  watch: {
    shimmerColor: {
      immediate: true,
      handler(newColor) {
        const rgb = this.isHexColor(newColor)
            ? this.hexToRgb(newColor)
            : this.shimmerColor;
        this.shimmerStyle = {
          backgroundImage: `linear-gradient(90deg, rgba(${rgb}, 0) 0%, rgba(${rgb}, 0.2) 20%, rgba(${rgb}, 0.5) 60%, rgba(${rgb}, 0))`,
        };
      },
    },
  },
};

</script>

<template>
  <div :class="['skeleton-container', loaderClass, bgClass]" :style="containerStyle">
    <div class="skeleton-shimmer" :style="shimmerStyle"></div>
    <slot />
  </div>
</template>