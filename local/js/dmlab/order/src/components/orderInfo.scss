.OrderInfo {
  min-width: 376px;
  border-radius: 2px;
  grid-column: 1 span;
  box-shadow: 0px 0px 20px 0px #0000001A;
  padding: 32px;
  align-self: self-start;
  position: sticky;
  top: 0px;

  &__weightText {
    font-family: Montser<PERSON>;
    font-size: 14px;
    font-weight: 400;
    line-height: 17.07px;
    text-align: center;
    margin-top: 11px;
  }
  &__error {
    font-family: Montserrat;
    font-size: 14px;
    font-weight: 400;
    line-height: 17.07px;
    text-align: center;
    margin-top: 11px;
    color:#DB3636;
  }
  &__title {
    font-family: Montserrat;
    font-size: 18px;
    font-weight: 600;
    line-height: 21.94px;
    text-align: left;
    color: #333333;
    padding-bottom: 20px;
    border-bottom: 1px dashed #E9E9E9;

  }

  &__total {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px dashed #E9E9E9;
  }

  &__toOrder {
    margin-top: 40px !important;
    width: 100%;
  }

  &__bonuses {
    margin-top: 20px;

  }

  &__promocodes {
    display: flex;
    gap: 4px;
    margin-top: 20px;
    flex-direction: column;

    &__error {
      color: #DB3636;
      font-family: Montserrat;
      font-size: 13px;
      font-weight: 400;
      line-height: 18.2px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }

    &__wrapper {
      display: flex;
      gap: 11px;
    }

    &--value {
      border: 1px solid #ECECEC !important;
      font-family: Montserrat;
      font-size: 14px !important;
      font-weight: 500;
      line-height: 18px !important;
      text-align: left;
      background: #FAFAFA !important;
      padding: 9px 16px;
      border-radius: 3px !important;
      width: 100%;
      color: #333333 !important;

      &::placeholder {
        font-weight: 400;
      }

      &--error {
        border: 1px solid #DB3636 !important;
      }

      &--active {
        border: 1px solid #539348 !important;
        background: #fff !important;
      }
    }

    &--submit {
      background: transparent;
      color: #797979;
      font-family: Montserrat;
      font-size: 14px;
      font-weight: 500;
      line-height: 17.07px;
      text-align: left;
      border: none;
      text-transform: uppercase;

      &--active {
        color: #539348;
      }
    }
  }

}


.infoOrder {
  &__list {
    padding-left: 0px;
    margin: 0 !important;
  }

  &__item {
    &:before {
      display: none;
    }

    &:not(:first-child) {
      padding-top: 20px;
    }

    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__key {
    font-family: Montserrat;
    font-size: 14px;
    font-weight: 400;
    line-height: 17.07px;
    text-align: center;

  }

  &__value {
    color: #333333;
    font-weight: 600;
    font-family: Montserrat;
    font-size: 14px;
    font-weight: 600;
    line-height: 17.07px;
    text-align: center;

  }
}

.totalOrder {
  color: #333333;
  font-family: Montserrat;
  font-size: 20px;
  font-weight: 700;
  line-height: 24.38px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bonusesOrder {
  padding-top: 20px;
  border-top: 1px dashed #E9E9E9;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &__key {
    & span {
      font-family: Montserrat;
      font-size: 14px;
      font-weight: 600;
      line-height: 17.07px;
      text-align: left;
      color: #333333;

    }
  }

  &__value {
    display: flex;
  }

  &__checkbox {
    margin: 0;
    position: absolute;
    z-index: -1;
    opacity: 0;

    & + label {
      background: #DDDDDD;
      width: 48px;
      height: 26px;
      border-radius: 20px;
      position: relative;
      cursor: pointer;
      margin: 0;
    }

    & + label {
      background: #DDDDDD;
      width: 48px;
      height: 26px;
      border-radius: 20px;
      position: relative;
    }

    & + label::before {
      content: '';
      position: absolute;
      background: #FFFFFF;
      width: 22px;
      height: 22px;
      top: 2px;
      left: 2px;
      border-radius: 20px;
    }

    &:checked + label {
      background: #539348;
    }

    &:checked + label::before {
      right: 2px;
      left: auto;
    }
  }

}
.bonusesError {
  color:red;
  font-size:14px;
  line-height: normal;
  background: #D800000D;
  border: 1px solid #D800001A;
  margin-top: 20px;
  padding: 20px 5px 20px 16px;
}