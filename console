#!/usr/bin/env php
<?php

$_SERVER["DOCUMENT_ROOT"] = getenv('DOCUMENT_ROOT');
require($_SERVER["DOCUMENT_ROOT"] . "/bitrix/modules/main/include/prolog_before.php");

define("NO_KEEP_STATISTIC", true);
define("NOT_CHECK_PERMISSIONS", true);
define("NO_AGENT_CHECK", true);
define("BX_UTF", true);
define("BX_BUFFER_USED", true);

use Symfony\Component\Console\Application;

class CronApplication
{
    private Application $application;

    public function __construct()
    {
        $this->application = new Application();
        $this->loadAutoloader();
        $this->registerCommands();
    }

    private function loadAutoloader(): void
    {
        $autoloadPath = $_SERVER["DOCUMENT_ROOT"] . "/local/php_interface/autoload.php";
        if (file_exists($autoloadPath)) {
            require_once($autoloadPath);
        }
    }

    private function registerCommands(): void
    {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($_SERVER["DOCUMENT_ROOT"] . '/local/php_interface/App/Cron/')
        );

        $regex = new RegexIterator($iterator, '/^.+Command\.php$/i', RegexIterator::GET_MATCH);

        foreach ($regex as $file) {
            include $file[0];
        }

        foreach (get_declared_classes() as $className) {
            if (is_subclass_of($className, '\Symfony\Component\Console\Command\Command')) {
                $this->application->add(new $className());
            }
        }
    }

    public function run(): void
    {
        try {
            $this->application->run();
        } catch (Exception $e) {
            throw new Exception("Error Processing Cron Console: " . $e->getMessage());
        }
    }
}

$cronApp = new CronApplication();
$cronApp->run();
